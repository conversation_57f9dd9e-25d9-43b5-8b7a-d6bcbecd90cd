# Homepage & Files UI Implementation Plan

## Executive Summary
This plan maps the homepage and files UI designs to the existing ChatLo architecture, introducing a **Context Vault** system that organizes conversations, files, and artifacts into project-based contexts while maintaining seamless integration with the current chat and history functionality.

## Design Analysis & File-Based Context Philosophy

### Homepage UI Design Features
- **Context Cards Grid**: File system folder-based organization with visual cards showing chat/file counts
- **Search & Filtering**: Global search across file system contexts with view toggles (grid/list)
- **Context Details Modal**: 3-panel view (Recent Chats 30% | Files 30% | Master.md Preview 40%)
- **VSCode-style Navigation**: Icon bar with Home, Chat, History, Files tabs

### Files UI Design Features
- **File Tree Panel**: Real file system hierarchical structure (20% width)
- **Markdown Preview**: Live preview of selected files with master.md focus (60% height)
- **Quick Actions Panel**: AI-powered file operations optimized for Gemma models (right sidebar)
- **Recent Chats Integration**: Context-aware chat history linked to file system (40% height)

## File-Based Context Management Philosophy

### Core Principles
1. **File System as Source of Truth**: All contexts exist as physical folders in user's vault
2. **Database as Index Only**: DB stores metadata, relationships, and search indices - not content
3. **User-Friendly Archive**: Users can backup, sync, and view contexts using standard file tools
4. **Markdown as Memory**: Master.md serves as evolving context memory and overview
5. **Gemma Model Optimization**: Content chunking for 32k (preferences) and 128k (relationships) limits

## Feature Mapping & Data Flow

### 1. File-Based Context Vault System

#### File System Structure
```
~/Documents/Chatlo/                    # User-configurable vault location
├── .chatlo/                          # System metadata (hidden)
│   ├── index.db                      # SQLite index database
│   ├── preferences.json              # User preferences
│   └── context-registry.json         # Context metadata cache
├── project-alpha/                    # Context folder (user-created)
│   ├── master.md                     # Context overview & memory
│   ├── .context/                     # Context system files
│   │   ├── metadata.json             # Context configuration
│   │   ├── chat-history.md           # Linked conversations summary
│   │   └── memory-chunks/            # Gemma-optimized memory files
│   │       ├── preferences-32k.md    # User habits & preferences
│   │       └── relationships-128k.md # Data relationships & context
│   ├── documents/                    # User documents
│   ├── images/                       # Context images
│   └── exports/                      # Generated artifacts
└── research-notes/                   # Another context folder
    ├── master.md
    ├── .context/
    └── ...
```

#### Database Schema (Index Only)
```sql
-- Context index (file system metadata)
CREATE TABLE context_index (
  id TEXT PRIMARY KEY,
  folder_path TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  master_doc_path TEXT,
  last_scanned TEXT NOT NULL,
  file_count INTEGER DEFAULT 0,
  chat_count INTEGER DEFAULT 0,
  is_pinned INTEGER DEFAULT 0
);

-- File index (metadata only, content in files)
CREATE TABLE file_index (
  id TEXT PRIMARY KEY,
  context_id TEXT,
  file_path TEXT NOT NULL UNIQUE,
  filename TEXT NOT NULL,
  file_type TEXT NOT NULL,
  size INTEGER,
  content_hash TEXT,
  last_modified TEXT,
  last_indexed TEXT,
  FOREIGN KEY (context_id) REFERENCES context_index (id) ON DELETE CASCADE
);

-- Chat-context relationships
CREATE TABLE chat_context_links (
  id TEXT PRIMARY KEY,
  conversation_id TEXT NOT NULL,
  context_id TEXT NOT NULL,
  linked_at TEXT NOT NULL,
  FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE,
  FOREIGN KEY (context_id) REFERENCES context_index (id) ON DELETE CASCADE
);
```

#### Data Flow Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Homepage      │    │   Files Page    │    │   Chat Page     │
│   (Contexts)    │◄──►│   (File Tree)   │◄──►│   (Messages)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                File System Context Store                       │
│  - vaultPath         - contextFolders[]   - activeMasterDoc    │
│  - fileSystemTree    - chatContextLinks   - memoryChunks      │
└─────────────────────────────────────────────────────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│     File System (Source of Truth)    │    Database (Index)     │
│  ~/Documents/Chatlo/contexts/        │  context_index | files  │
│  ├── master.md                       │  chat_context_links     │
│  ├── .context/metadata.json          │  search indices         │
│  └── documents/                      │                         │
└─────────────────────────────────────────────────────────────────┘
```

## Data Strategy: File-First with Database Indexing

### Core Data Strategy Principles

#### 1. File System as Primary Storage
- **Context Folders**: Each context is a physical folder in user's vault
- **Master.md**: Living document that evolves with context development
- **User Control**: Users can backup, sync, edit contexts with any tool
- **Transparency**: All data visible and accessible outside ChatLo

#### 2. Database as Smart Index
- **Metadata Only**: Store file paths, timestamps, relationships
- **Search Optimization**: Full-text search indices for fast queries
- **Relationship Tracking**: Chat-context links, file dependencies
- **Cache Layer**: Performance optimization for UI rendering

#### 3. Gemma Model Memory Management
```markdown
# Memory Chunk Strategy

## preferences-32k.md (Gemma 3 32k context)
- User interaction patterns
- Preferred file types and structures
- Common queries and responses
- UI preferences and shortcuts
- Model selection habits

## relationships-128k.md (Gemma 3 128k context)
- Inter-file relationships and dependencies
- Context evolution history
- Cross-reference patterns
- Project structure insights
- Conversation themes and topics
```

### 2. File System Integration Strategy

#### Real File System Tree
- **Direct File Access**: Read actual file system structure from vault path
- **Watch for Changes**: Monitor file system events for real-time updates
- **Context Folder Scanning**: Automatically detect new contexts and files
- **Master Document Priority**: Always highlight master.md in context root

#### File Tree Component Architecture
```typescript
interface FileSystemNode {
  path: string                    // Actual file system path
  name: string
  type: 'folder' | 'file'
  isContext: boolean             // True for context root folders
  isMaster: boolean              // True for master.md files
  children?: FileSystemNode[]
  metadata?: {
    size: number
    modified: Date
    fileType: string
    isIndexed: boolean
  }
}

interface ContextFileSystem {
  vaultPath: string              // User-configured vault location
  contexts: ContextFolder[]      // Scanned context folders
  activeContextPath: string | null
  masterDocPath?: string         // Path to active master.md
}

interface ContextFolder {
  path: string                   // Full folder path
  name: string                   // Folder name (context name)
  masterDocPath?: string         // Path to master.md
  fileCount: number
  lastModified: Date
  metadata: {
    description?: string
    tags?: string[]
    color?: string
    icon?: string
  }
}
```

### 3. Chat & History Integration Strategy

#### Enhanced Navigation Flow
1. **Homepage → Files**: Click context card → Navigate to `/files/:contextPath`
2. **Files → Chat**: Click "Ask about this file" → Create chat with file context + master.md
3. **Chat → Context**: Add conversation to context → Update chat-history.md
4. **History → Context Filter**: Filter conversations by linked contexts
5. **Context Selection in Chat**: Choose context when starting new conversation

#### Chat-Context Integration Features
```typescript
interface ChatContextIntegration {
  // Context selection in new chat
  contextSelection: {
    showContextPicker: boolean
    availableContexts: ContextFolder[]
    selectedContext?: ContextFolder
    autoLinkFiles: boolean          // Auto-attach master.md and related files
  }

  // Context-aware chat creation
  createContextChat: (contextPath: string, fileRef?: string) => Promise<string>

  // Link existing chat to context
  linkChatToContext: (chatId: string, contextPath: string) => Promise<void>

  // Context memory integration
  loadContextMemory: (contextPath: string) => Promise<{
    preferences: string    // From preferences-32k.md
    relationships: string  // From relationships-128k.md
    masterDoc: string     // From master.md
  }>
}
```

#### History Page Context Features
```typescript
interface HistoryContextFeatures {
  // Context filtering
  contextFilter: {
    selectedContext?: string
    showUnlinked: boolean
    groupByContext: boolean
  }

  // Context assignment
  assignToContext: (chatId: string, contextPath: string) => Promise<void>

  // Context-based organization
  contextGroups: Array<{
    contextPath: string
    contextName: string
    conversations: Conversation[]
    masterDocPreview?: string
  }>
}
```

#### State Management Integration
```typescript
// Extend existing store with file-system approach
interface AppStore {
  // Existing state...

  // File-system context state
  vaultPath: string
  contexts: ContextFolder[]
  activeContextPath: string | null
  fileSystemTree: FileSystemNode[]
  selectedFilePath: string | null

  // Context-chat integration
  chatContextLinks: Record<string, string>  // chatId -> contextPath
  contextMemoryCache: Record<string, ContextMemory>

  // New actions
  scanVaultForContexts: () => Promise<void>
  createNewContext: (name: string, template?: string) => Promise<string>
  setActiveContext: (contextPath: string) => void
  linkChatToContext: (chatId: string, contextPath: string) => Promise<void>
  loadContextMemory: (contextPath: string) => Promise<ContextMemory>
  updateMasterDocument: (contextPath: string, content: string) => Promise<void>
}
```

## File Structure & Components

### New Pages & Components (File-System Focused)
```
src/
├── pages/
│   ├── HomePage.tsx              # Context folder cards grid
│   └── FilesPage.tsx             # Real file tree + preview
├── components/
│   ├── contexts/
│   │   ├── ContextCard.tsx       # File system folder card
│   │   ├── ContextGrid.tsx       # Grid with folder scanning
│   │   ├── ContextModal.tsx      # 3-panel with master.md focus
│   │   ├── CreateContextModal.tsx # New folder + template creation
│   │   └── ContextPicker.tsx     # Context selection for chats
│   ├── files/
│   │   ├── FileSystemTree.tsx    # Real file system browser
│   │   ├── MasterDocPreview.tsx  # Master.md focused preview
│   │   ├── FileQuickActions.tsx  # Gemma-optimized AI actions
│   │   ├── ContextChats.tsx      # Context-linked chat history
│   │   └── MemoryChunks.tsx      # Gemma memory management
│   ├── chat/
│   │   ├── ContextSelector.tsx   # Context picker in chat
│   │   ├── FileReference.tsx     # @filename with context awareness
│   │   └── ContextMemoryLoader.tsx # Load context memory for AI
│   ├── history/
│   │   ├── ContextFilter.tsx     # Filter by context
│   │   ├── ContextGroups.tsx     # Group chats by context
│   │   └── ChatContextLinker.tsx # Link existing chats to contexts
│   └── shared/
│       ├── VaultPathSelector.tsx # Vault location settings
│       ├── FileSystemWatcher.tsx # Real-time file monitoring
│       └── ContextSearch.tsx     # Search across contexts
├── hooks/
│   ├── useVaultScanner.tsx       # Scan vault for contexts
│   ├── useFileSystemTree.tsx     # Real file tree operations
│   ├── useContextMemory.tsx      # Gemma memory management
│   ├── useMasterDocument.tsx     # Master.md operations
│   └── useChatContextLinks.tsx   # Chat-context relationships
└── services/
    ├── vaultService.ts           # Vault folder operations
    ├── fileSystemService.ts      # File system operations
    ├── contextMemoryService.ts   # Gemma memory chunking
    ├── masterDocService.ts       # Master.md management
    └── chatContextService.ts     # Chat-context linking
```

### File System Service Extensions
```typescript
// electron/vaultService.ts - New file system service
class VaultService {
  private vaultPath: string
  private watcher: FSWatcher

  // Vault management
  async setVaultPath(path: string): Promise<void>
  async getVaultPath(): Promise<string>
  async scanForContexts(): Promise<ContextFolder[]>

  // Context folder operations
  async createContext(name: string, template?: ContextTemplate): Promise<string>
  async getContextMetadata(contextPath: string): Promise<ContextMetadata>
  async updateContextMetadata(contextPath: string, metadata: ContextMetadata): Promise<void>

  // Master document operations
  async createMasterDocument(contextPath: string, template: string): Promise<string>
  async getMasterDocument(contextPath: string): Promise<string>
  async updateMasterDocument(contextPath: string, content: string): Promise<void>

  // Memory chunk management (Gemma optimization)
  async getPreferencesMemory(contextPath: string): Promise<string>
  async updatePreferencesMemory(contextPath: string, content: string): Promise<void>
  async getRelationshipsMemory(contextPath: string): Promise<string>
  async updateRelationshipsMemory(contextPath: string, content: string): Promise<void>

  // File system watching
  async startWatching(): Promise<void>
  async stopWatching(): Promise<void>
  onContextChanged: (callback: (contextPath: string) => void) => void
}

// electron/database.ts extensions (index only)
class DatabaseManager {
  // Context index methods
  async indexContext(contextPath: string): Promise<void>
  async getContextIndex(): Promise<ContextIndex[]>
  async updateContextStats(contextPath: string, stats: ContextStats): Promise<void>

  // Chat-context linking
  async linkChatToContext(chatId: string, contextPath: string): Promise<void>
  async getChatContext(chatId: string): Promise<string | null>
  async getContextChats(contextPath: string): Promise<Conversation[]>

  // Search index
  async indexFileContent(filePath: string, content: string): Promise<void>
  async searchInContext(contextPath: string, query: string): Promise<SearchResult[]>
  async searchAcrossContexts(query: string): Promise<SearchResult[]>
}
```

## User Stories & Workflows

### Story 1: Project Manager - File-Based Design System Context
**As a** project manager
**I want to** organize all design system files in a dedicated folder with evolving documentation
**So that** I can track project progress and maintain institutional knowledge

**Workflow:**
1. Navigate to Homepage → Create new "Design System" context folder
2. ChatLo creates folder structure: `~/Documents/Chatlo/design-system/` with master.md template
3. Add files directly to folder or via ChatLo interface → Files automatically indexed
4. Edit master.md to document project overview, goals, and progress
5. Start conversations → Link to context → Updates reflected in chat-history.md
6. View context card showing "12 chats, 24 files" with master.md preview

### Story 2: Developer - Gemma-Optimized AI Assistance
**As a** developer
**I want to** get AI insights about files with context memory and relationship awareness
**So that** I can understand code structure with full project context

**Workflow:**
1. Navigate to Files page → Browse real file tree for "project-alpha" context folder
2. Select master.md → Preview with live file content from disk
3. Click "Ask about this file" → Chat opens with:
   - File content loaded
   - Context memory (preferences-32k.md + relationships-128k.md)
   - Master document context
4. AI responses optimized for Gemma models with chunked context
5. Conversation updates context memory files automatically

### Story 3: Knowledge Worker - File-Based Context Management
**As a** knowledge worker
**I want to** manage contexts as folders I can backup, sync, and edit externally
**So that** I maintain control over my knowledge base

**Workflow:**
1. Set vault location in Settings → Point to Dropbox/OneDrive folder for sync
2. Homepage scans vault and shows context folders as cards
3. Create new context → Physical folder created with template structure
4. Edit master.md in external editor → Changes reflected in ChatLo immediately
5. Backup entire vault → All contexts, chats, and files preserved
6. Search across contexts → Full-text search through all files and memory chunks

### Story 4: Chat-Context Integration
**As a** user
**I want to** link conversations to contexts and filter history by context
**So that** I can organize my AI interactions by project

**Workflow:**
1. Start new chat → Context picker shows available contexts
2. Select "Design System" context → Master.md and memory loaded automatically
3. Chat about files → Conversation linked to context, updates chat-history.md
4. Navigate to History → Filter by context to see related conversations
5. Assign existing chats to contexts → Retroactively organize conversations

## Technical Implementation Phases

### Phase 1: File System Foundation (Week 1-2)
- [ ] Vault path configuration in Settings integration
- [ ] File system scanning and context detection service
- [ ] Database index schema for file-based contexts
- [ ] Homepage route with context folder cards
- [ ] Basic context folder creation with templates

### Phase 2: Master Document & Memory System (Week 3-4)
- [ ] Master.md template system and management
- [ ] Gemma memory chunking (preferences-32k.md, relationships-128k.md)
- [ ] Real file system tree component with live updates
- [ ] File preview with direct file system reading
- [ ] Context metadata management (.context/metadata.json)

### Phase 3: Chat-Context Integration (Week 5-6)
- [ ] Context picker in chat creation
- [ ] Chat-context linking system
- [ ] Context memory loading for AI (master.md + memory chunks)
- [ ] History page context filtering and grouping
- [ ] Automatic context memory updates from conversations

### Phase 4: Advanced Features & Polish (Week 7-8)
- [ ] File system watching for real-time updates
- [ ] Cross-context search with file content indexing
- [ ] Context templates and quick setup
- [ ] Backup/sync validation and conflict resolution
- [ ] Performance optimization for large vaults

## Success Metrics
- **File-Based Adoption**: 80% of users set custom vault paths and create contexts
- **Context Organization**: Users maintain 3+ active context folders on average
- **Master Document Usage**: 90% of contexts have actively maintained master.md files
- **External Tool Integration**: 60% of users edit context files in external editors
- **Chat-Context Linking**: 70% of new chats linked to contexts with memory loading
- **Backup/Sync Usage**: 50% of users leverage external sync tools for vault backup

## Risk Mitigation & File System Considerations
- **File System Permissions**: Handle read/write permissions gracefully across platforms
- **Sync Conflicts**: Detect and resolve conflicts when files modified externally
- **Large Vault Performance**: Optimize scanning and indexing for vaults with 1000+ files
- **Cross-Platform Paths**: Ensure path handling works across Windows/Mac/Linux
- **Database Corruption**: Rebuild index from file system if database corrupted
- **Memory Management**: Efficient chunking for Gemma models without context overflow
- **File Watching**: Handle file system events without overwhelming the system

This implementation transforms ChatLo from a chat-centric application into a comprehensive context vault while preserving all existing functionality and providing clear upgrade paths for current users.

## Detailed Component Specifications

### Homepage Context Cards
```typescript
interface ContextCardProps {
  context: Context
  stats: {
    chatCount: number
    fileCount: number
    lastUpdated: string
    hasArtifacts: boolean
  }
  onSelect: (contextId: string) => void
  onInfo: (contextId: string) => void
}

// Visual Design Mapping:
// - Gradient backgrounds based on context color theme
// - Hover animations with translateY(-1px)
// - Stats display: "12 chats • 24 files"
// - Status indicator dot with color coding
// - Action buttons: View (primary) + Info (secondary)
```

### Files Page Layout
```typescript
interface FilesPageLayout {
  leftPanel: {
    width: '20%'
    component: 'FileTree'
    features: ['hierarchical', 'collapsible', 'search', 'add-file']
  }
  rightPanel: {
    top: {
      height: '60%'
      component: 'FilePreview'
      features: ['markdown-render', 'syntax-highlight', 'quick-actions']
    }
    bottom: {
      height: '40%'
      component: 'RecentChats'
      features: ['context-filtered', 'file-related', 'quick-reply']
    }
  }
}
```

### Context Modal 3-Panel Design
```typescript
interface ContextModalPanels {
  recentChats: {
    width: '30%'
    maxItems: 10
    features: ['click-to-open', 'timestamp', 'message-preview']
  }
  files: {
    width: '30%'
    features: ['file-icons', 'type-indicators', 'quick-preview']
  }
  masterPreview: {
    width: '40%'
    features: ['markdown-render', 'scroll-sync', 'edit-button']
  }
}
```

## Integration with Existing Systems

### Router Updates
```typescript
// src/App.tsx - Add new routes
<Routes>
  <Route path="/" element={<HomePage />} />           {/* NEW */}
  <Route path="/chat" element={<ChatArea />} />       {/* MOVED */}
  <Route path="/chat/:id" element={<ChatArea />} />   {/* EXISTING */}
  <Route path="/files" element={<FilesPage />} />     {/* NEW */}
  <Route path="/files/:contextId" element={<FilesPage />} /> {/* NEW */}
  <Route path="/history" element={<HistoryPage />} />  {/* EXISTING */}
  <Route path="/settings" element={<SettingsPage />} /> {/* EXISTING */}
</Routes>
```

### IconBar Navigation Updates
```typescript
// src/components/IconBar.tsx - Update navigation items
const navigationItems = [
  { name: 'home', path: '/', icon: 'fa-solid fa-home', tooltip: 'Home' },
  { name: 'chat', path: '/chat', icon: 'fa-solid fa-comment', tooltip: 'Chat' },
  { name: 'history', path: '/history', icon: 'fa-solid fa-clock-rotate-left', tooltip: 'History' },
  { name: 'files', path: '/files', icon: 'fa-solid fa-folder-tree', tooltip: 'Files' }
]
```

### Store Integration Pattern
```typescript
// src/store/contextSlice.ts - New store slice
interface ContextSlice {
  // State
  contexts: Context[]
  activeContextId: string | null
  contextFiles: Record<string, FileTreeNode[]>
  selectedFileId: string | null
  searchQuery: string
  viewMode: 'grid' | 'list'

  // Actions
  loadContexts: () => Promise<void>
  createContext: (data: CreateContextData) => Promise<string>
  updateContext: (id: string, updates: Partial<Context>) => Promise<void>
  deleteContext: (id: string) => Promise<void>
  setActiveContext: (id: string | null) => void

  // File operations
  loadContextFiles: (contextId: string) => Promise<void>
  addFileToContext: (contextId: string, fileId: string) => Promise<void>
  removeFileFromContext: (contextId: string, fileId: string) => Promise<void>
  setMasterDocument: (contextId: string, fileId: string) => Promise<void>

  // UI state
  setSearchQuery: (query: string) => void
  setViewMode: (mode: 'grid' | 'list') => void
  setSelectedFile: (fileId: string | null) => void
}
```

## Migration Strategy

### Existing Data Migration
1. **Auto-Context Creation**: Create default contexts for existing conversations
2. **File Association**: Link existing files to appropriate contexts based on usage patterns
3. **Conversation Grouping**: Group related conversations using title similarity and file references
4. **Master Document Detection**: Identify potential master documents (README.md, index.md, etc.)

### Backward Compatibility
- Existing chat URLs remain functional
- Current conversation sidebar preserved as fallback
- File attachment system continues to work
- Settings and preferences maintained

### User Onboarding
1. **Welcome Tour**: Guided introduction to context system
2. **Migration Assistant**: Help users organize existing data
3. **Quick Start Templates**: Pre-configured contexts for common use cases
4. **Progressive Disclosure**: Gradually introduce advanced features

## Performance Considerations

### Optimization Strategies
- **Virtual Scrolling**: Handle large file trees efficiently
- **Lazy Loading**: Load file content and previews on demand
- **Caching**: Cache file tree structures and search results
- **Debounced Search**: Prevent excessive API calls during typing
- **Pagination**: Limit context cards and file lists per page

### Memory Management
- **Component Cleanup**: Proper cleanup of file watchers and subscriptions
- **Image Optimization**: Compress and cache file previews
- **Database Indexing**: Optimize queries for context and file relationships
- **State Pruning**: Remove unused context data from memory

This comprehensive plan provides a clear roadmap for transforming ChatLo into a powerful context vault while maintaining the familiar chat experience users expect.
