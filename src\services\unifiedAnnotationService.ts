/**
 * Unified Annotation Storage Service
 * REPLACES: Legacy annotationStorageService with isVirtualPath system
 * IMPLEMENTS: Unified path resolution using PathResolver and VaultContextService
 */

import { SmartAnnotationNote, FileIntelligence } from '../types/fileIntelligenceTypes'
import { intelligence as intelligenceClient } from '../api/UnifiedAPIClient'
import { vaultContextService } from './vaultContextService'

export class UnifiedAnnotationService {
  
  /**
   * Load all annotations for a file using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Array of annotations
   */
  async loadAnnotations(filePath: string, currentContextId?: string | null): Promise<SmartAnnotationNote[]> {
    try {
      // Use unified path resolution - NO MORE isVirtualPath
      const resolution = await vaultContextService.resolvePath(filePath, {
        context: currentContextId || undefined
      })
      
      console.log('[UNIFIED-ANNOTATIONS] 📖 Loading annotations with unified path resolution:', { 
        originalPath: filePath,
        resolvedType: resolution.type,
        vault: resolution.vault,
        context: resolution.context,
        finalPath: resolution.path
      })

      const result = await intelligenceClient.read(resolution.path, resolution.vault)

      if (result && result.success !== false && result.data) {
        const intelligence = result.data as FileIntelligence

        console.log('[UNIFIED-ANNOTATIONS] 🔍 Intelligence data structure:', {
          hasKeyIdeas: !!intelligence.key_ideas,
          keyIdeasCount: intelligence.key_ideas?.length || 0,
          hasSmartAnnotations: !!intelligence.smart_annotations,
          smartAnnotationsCount: intelligence.smart_annotations?.length || 0,
          pathType: resolution.type
        })

        if (intelligence.smart_annotations && Array.isArray(intelligence.smart_annotations)) {
          return intelligence.smart_annotations
        }
      }

      return []
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to load annotations:', error)
      return []
    }
  }

  /**
   * Save annotations for a file using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param annotations - Array of annotations to save
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Success status
   */
  async saveAnnotations(
    filePath: string, 
    annotations: SmartAnnotationNote[], 
    currentContextId?: string | null
  ): Promise<boolean> {
    try {
      // Use unified path resolution - NO MORE isVirtualPath
      const resolution = await vaultContextService.resolvePath(filePath, {
        context: currentContextId || undefined
      })
      
      console.log('[UNIFIED-ANNOTATIONS] 💾 Saving annotations with unified path resolution:', { 
        originalPath: filePath,
        resolvedType: resolution.type,
        vault: resolution.vault,
        context: resolution.context,
        finalPath: resolution.path,
        annotationCount: annotations.length
      })

      // Load existing intelligence data
      let existingIntelligence: FileIntelligence = {
        smart_annotations: []
      }

      try {
        const existingResult = await intelligenceClient.read(resolution.path, resolution.vault)
        if (existingResult && existingResult.success !== false && existingResult.data) {
          existingIntelligence = existingResult.data as FileIntelligence
        }
      } catch (error) {
        console.log('[UNIFIED-ANNOTATIONS] ℹ️ No existing intelligence data, creating new')
      }

      // Update with new annotations
      const updatedIntelligence: FileIntelligence = {
        ...existingIntelligence,
        smart_annotations: annotations
      }

      // Save updated intelligence
      const saveResult = await intelligenceClient.write(
        resolution.path,
        updatedIntelligence,
        resolution.vault
      )

      if (saveResult && saveResult.success !== false) {
        console.log('[UNIFIED-ANNOTATIONS] ✅ Annotations saved successfully')
        return true
      } else {
        console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to save annotations:', saveResult)
        return false
      }
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to save annotations:', error)
      return false
    }
  }

  /**
   * Add a single annotation using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param annotation - The annotation to add
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Success status
   */
  async addAnnotation(
    filePath: string, 
    annotation: SmartAnnotationNote, 
    currentContextId?: string | null
  ): Promise<boolean> {
    try {
      // Load existing annotations
      const existingAnnotations = await this.loadAnnotations(filePath, currentContextId)
      
      // Add new annotation
      const updatedAnnotations = [...existingAnnotations, annotation]
      
      // Save updated annotations
      return await this.saveAnnotations(filePath, updatedAnnotations, currentContextId)
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to add annotation:', error)
      return false
    }
  }

  /**
   * Update an existing annotation using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param annotationId - The ID of the annotation to update
   * @param updatedAnnotation - The updated annotation data
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Success status
   */
  async updateAnnotation(
    filePath: string, 
    annotationId: string, 
    updatedAnnotation: Partial<SmartAnnotationNote>, 
    currentContextId?: string | null
  ): Promise<boolean> {
    try {
      // Load existing annotations
      const existingAnnotations = await this.loadAnnotations(filePath, currentContextId)
      
      // Find and update the annotation
      const annotationIndex = existingAnnotations.findIndex(ann => ann.id === annotationId)
      if (annotationIndex === -1) {
        console.error('[UNIFIED-ANNOTATIONS] ❌ Annotation not found:', annotationId)
        return false
      }
      
      // Update the annotation
      existingAnnotations[annotationIndex] = {
        ...existingAnnotations[annotationIndex],
        ...updatedAnnotation
      }
      
      // Save updated annotations
      return await this.saveAnnotations(filePath, existingAnnotations, currentContextId)
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to update annotation:', error)
      return false
    }
  }

  /**
   * Delete an annotation using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param annotationId - The ID of the annotation to delete
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Success status
   */
  async deleteAnnotation(
    filePath: string, 
    annotationId: string, 
    currentContextId?: string | null
  ): Promise<boolean> {
    try {
      // Load existing annotations
      const existingAnnotations = await this.loadAnnotations(filePath, currentContextId)
      
      // Filter out the annotation to delete
      const updatedAnnotations = existingAnnotations.filter(ann => ann.id !== annotationId)
      
      if (updatedAnnotations.length === existingAnnotations.length) {
        console.error('[UNIFIED-ANNOTATIONS] ❌ Annotation not found for deletion:', annotationId)
        return false
      }
      
      // Save updated annotations
      return await this.saveAnnotations(filePath, updatedAnnotations, currentContextId)
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to delete annotation:', error)
      return false
    }
  }

  /**
   * Clear all annotations for a file using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Success status
   */
  async clearAnnotations(filePath: string, currentContextId?: string | null): Promise<boolean> {
    try {
      return await this.saveAnnotations(filePath, [], currentContextId)
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to clear annotations:', error)
      return false
    }
  }

  /**
   * Update key ideas for a file using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param keyIdeas - Array of key ideas to save
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Success status
   */
  async updateKeyIdeas(filePath: string, keyIdeas: any[], currentContextId?: string | null): Promise<boolean> {
    try {
      // Use unified path resolution
      const resolution = await vaultContextService.resolvePath(filePath, {
        context: currentContextId || undefined
      })

      console.log('[UNIFIED-ANNOTATIONS] 🏷️ Updating key ideas with unified path resolution:', {
        originalPath: filePath,
        resolvedType: resolution.type,
        vault: resolution.vault,
        context: resolution.context,
        finalPath: resolution.path,
        keyIdeasCount: keyIdeas.length
      })

      // Load existing intelligence data
      let existingIntelligence: FileIntelligence = {}

      try {
        const existingResult = await intelligenceClient.read(resolution.path, resolution.vault)
        if (existingResult && existingResult.success !== false && existingResult.data) {
          existingIntelligence = existingResult.data as FileIntelligence
        }
      } catch (error) {
        console.log('[UNIFIED-ANNOTATIONS] ℹ️ No existing intelligence data for key ideas, creating new')
      }

      // Update with new key ideas
      const updatedIntelligence: FileIntelligence = {
        ...existingIntelligence,
        key_ideas: keyIdeas,
        updated_at: new Date().toISOString()
      }

      // Save updated intelligence
      const saveResult = await intelligenceClient.write(
        resolution.path,
        updatedIntelligence,
        resolution.vault
      )

      if (saveResult && saveResult.success !== false) {
        console.log('[UNIFIED-ANNOTATIONS] ✅ Key ideas saved successfully')
        return true
      } else {
        console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to save key ideas:', saveResult)
        return false
      }
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to update key ideas:', error)
      return false
    }
  }
}

// Export singleton instance
export const unifiedAnnotationService = new UnifiedAnnotationService()
