import { app, BrowserWindow, ipc<PERSON>ain, IpcMainInvokeEvent, dialog, screen } from 'electron'
import { autoUpdater } from 'electron-updater'
import path from 'path'
import { isDev } from './utils'
import { DatabaseManager } from './database'
import { FileSystemManager } from './fileSystem'
import { APIRegistry } from './api/APIRegistry'
import { UniversalPluginManager } from './plugins/PluginManager'
import { PluginCapability } from './plugins/types'
import { IntelligenceCoreService } from './core/IntelligenceCoreService'
import { PathResolver } from './core/PathResolver'
import { FileCoreService } from './core/FileCoreService'
import { EventBus } from './core/EventBus'
// TODO: Removed FileProcessingDiagnostics import - module marked for removal

class App {
  public mainWindow: BrowserWindow | null = null
  private db: DatabaseManager
  private fileSystem: FileSystemManager
  private apiRegistry: APIRegistry
  private pluginManager: UniversalPluginManager
  private fileCore: FileCoreService
  private isQuitting: boolean = false

  constructor() {
    this.db = new DatabaseManager()
    this.fileSystem = new FileSystemManager(this.db)

    // Initialize API registry with middleware
    this.apiRegistry = new APIRegistry({
      logging: {
        logRequests: true,
        logResponses: false,
        logErrors: true,
        logPerformance: true,
        maxLogLength: 500
      },
      rateLimiting: {
        maxRequests: 200,
        windowMs: 60000 // 1 minute
      },
      security: {
        allowedOrigins: isDev ? ['file://', 'http://localhost:5173'] : ['file://'],
        requireAuth: false
      },
      errorHandling: {
        logErrors: true,
        sanitizeErrors: false // Keep detailed errors for development
      }
    })

    // Initialize plugin manager
    this.pluginManager = new UniversalPluginManager(this.apiRegistry)

    // Add plugin directories
    this.pluginManager.addPluginDirectory(path.join(__dirname, 'plugins'))
    this.pluginManager.addPluginDirectory(path.join(app.getPath('userData'), 'plugins'))
    // Initialize core file service
    this.fileCore = new FileCoreService()
  }

  private openPDFViewer(filePath: string): void {
    const normalizedPath = path.normalize(filePath)
    const fileUrl = `file:///${normalizedPath.replace(/\\/g, '/')}`

    console.log('[PDF] Opening PDF viewer for:', fileUrl)

    // Create a new window specifically for PDF viewing
    const pdfWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        webSecurity: false, // Allow loading local files
        nodeIntegration: false,
        contextIsolation: true,
      },
      title: `PDF Viewer - ${path.basename(filePath)}`,
      icon: path.join(__dirname, '../assets/icon.png'),
      show: false, // Don't show until ready
    })

    // Load the PDF directly
    pdfWindow.loadURL(fileUrl)

    // Show window when ready
    pdfWindow.once('ready-to-show', () => {
      pdfWindow.show()
      console.log('[PDF] PDF viewer window shown')
    })

    // Handle window closed
    pdfWindow.on('closed', () => {
      console.log('[PDF] PDF viewer window closed')
    })
  }

  private createWindow(): void {
    // Following Electron best practices from official documentation
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false, // Don't show until ready-to-show event
      frame: false, // Frameless window for custom chrome
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'hidden',
      titleBarOverlay: false,
      backgroundColor: '#111827', // Match our app's dark theme (gray-900)
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true,
      },
    })

    // Intercept native window close to show renderer confirmation
    this.mainWindow.on('close', (e) => {
      if (this.isQuitting) return
      e.preventDefault()
      try { this.mainWindow?.webContents.send('app:request-close') } catch {}
    })


    // Use ready-to-show event for graceful window display (Electron best practice)
    this.mainWindow.once('ready-to-show', () => {
      if (this.mainWindow) {
        this.mainWindow.show()

        // Open DevTools in development after window is shown
        if (isDev) {
          this.mainWindow.webContents.openDevTools()
        }
      }
    })

    if (isDev) {
      this.mainWindow.loadURL('http://localhost:5173')
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
    }

    this.mainWindow.on('closed', () => {
      this.mainWindow = null
    })
  }

  private validateSender(frame: any): boolean {
    // Validate the sender is from your app
    if (!frame || !frame.url) return false
    return frame.url.startsWith('file://') ||
           frame.url.startsWith('http://localhost:5173')
  }

  private validateInput(input: any, expectedType: string, maxLength?: number): boolean {
    try {
      console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT START ===')
      console.log('🔍 [VAULT-SETTING] Input:', input)
      console.log('🔍 [VAULT-SETTING] Expected type:', expectedType)
      console.log('🔍 [VAULT-SETTING] Max length:', maxLength)
      console.log('🔍 [VAULT-SETTING] Actual type:', typeof input)
      
      // Type validation
      if (typeof input !== expectedType) {
        console.error(`🔍 [VAULT-SETTING] ❌ Invalid input type. Expected ${expectedType}, got ${typeof input}`)
        console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (TYPE MISMATCH) ===')
        return false
      }

      // Length validation for strings
      if (expectedType === 'string' && maxLength && input.length > maxLength) {
        console.error(`🔍 [VAULT-SETTING] ❌ Input too long. Max length: ${maxLength}, got: ${input.length}`)
        console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (TOO LONG) ===')
        return false
      }

      // SECURITY: Path injection prevention for string inputs
      if (expectedType === 'string') {
        console.log('🔍 [VAULT-SETTING] Performing security validation for string input...')
        
        // ALWAYS block directory traversal attempts (essential security)
        const hasDirectoryTraversal = input.includes('..') || input.includes('\\\\')
        console.log('🔍 [VAULT-SETTING] Has directory traversal:', hasDirectoryTraversal)
        
        if (hasDirectoryTraversal) {
          console.error('🔍 [VAULT-SETTING] 🚨 Rejecting input with directory traversal:', input)
          console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (BLOCKED TRAVERSAL) ===')
          return false
        }

        // Get user's security level preference
        const securityLevel = this.getUserSecurityLevel()
        console.log('🔍 [VAULT-SETTING] User security level:', securityLevel)
        
        // If security is disabled, only check directory traversal
        if (securityLevel === 'disabled') {
          console.log('🔍 [VAULT-SETTING] ✅ Security disabled - only blocking directory traversal')
          console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (SECURITY DISABLED) ===')
          return true
        }

        // For relaxed mode, only check directory traversal (already done above)
        if (securityLevel === 'relaxed') {
          console.log('🔍 [VAULT-SETTING] ✅ Relaxed security - only blocking directory traversal')
          console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (RELAXED SECURITY) ===')
          return true
        }

        // For moderate and strict modes, apply additional checks
        if (securityLevel === 'moderate' || securityLevel === 'strict') {
          console.log('🔍 [VAULT-SETTING] Applying moderate/strict security checks...')
          
          // Check for Windows drive letters and absolute paths
          const hasWindowsDrive = input.match(/^[A-Z]:\\/) || input.match(/^\/[A-Z]:\//)
          console.log('🔍 [VAULT-SETTING] Has Windows drive pattern:', hasWindowsDrive)
          
          if (hasWindowsDrive) {
            // Check against user's custom vault patterns first
            const customPatterns = this.getUserVaultPatterns()
            const matchesCustomPattern = customPatterns.some(pattern => {
              const regex = new RegExp(pattern.replace(/\*/g, '.*'))
              return regex.test(input)
            })
            
            if (matchesCustomPattern) {
              console.log('🔍 [VAULT-SETTING] ✅ Input matches custom vault pattern:', input)
              console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (CUSTOM PATTERN) ===')
              return true
            }

            // For strict mode, block all Windows drive paths unless they match custom patterns
            if (securityLevel === 'strict') {
              console.error('🔍 [VAULT-SETTING] 🚨 Strict mode: Rejecting Windows drive path:', input)
              console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (STRICT BLOCKED) ===')
              return false
            }

            // For moderate mode, allow common legitimate paths and user directories
            const legitimatePaths = ['Documents', 'Desktop', 'Downloads', 'ChatLo_Vaults', 'Post-Kernel-Test', 'Users', 'augment-projects', 'chat-locally', 'chatlo']
            const hasLegitimatePath = legitimatePaths.some(path => input.includes(path))

            // Also allow any path under user's home directory (C:\Users\<USER>\...)
            const isUserPath = input.match(/^[A-Z]:\\Users\\[^\\]+\\/) !== null
            
            if (hasLegitimatePath || isUserPath) {
              console.log('🔍 [VAULT-SETTING] ✅ Moderate mode: Input contains legitimate path or is user path:', input)
              console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (MODERATE ALLOWED) ===')
              return true
            }
            
            console.error('🔍 [VAULT-SETTING] 🚨 Moderate mode: Rejecting suspicious Windows path:', input)
            console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (MODERATE BLOCKED) ===')
            return false
          }
        }
      }

      console.log('🔍 [VAULT-SETTING] ✅ Input validation passed')
      console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (SUCCESS) ===')
      return true
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error during input validation:', error)
      console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (ERROR) ===')
      return false
    }
  }

  /**
   * Helper method to detect base64 strings
   */
  private isBase64String(str: string): boolean {
    try {
      // Check if string looks like base64 and can be decoded
      return Buffer.from(str, 'base64').toString('base64') === str
    } catch {
      return false
    }
  }

  /**
   * Get user's security level preference from database
   */
  private getUserSecurityLevel(): 'disabled' | 'relaxed' | 'moderate' | 'strict' {
    try {
      const securityLevel = this.db.getSetting('securityLevel')
      if (securityLevel && ['disabled', 'relaxed', 'moderate', 'strict'].includes(securityLevel)) {
        return securityLevel as 'disabled' | 'relaxed' | 'moderate' | 'strict'
      }
      return 'moderate' // Default fallback
    } catch (error) {
      console.warn('🔍 [VAULT-SETTING] Could not read security level, using default:', error)
      return 'moderate'
    }
  }

  /**
   * Get user's custom vault patterns from database
   */
  private getUserVaultPatterns(): string[] {
    try {
      const patterns = this.db.getSetting('allowedVaultPatterns')
      if (patterns && typeof patterns === 'string') {
        // Split by newlines and filter out empty lines
        return patterns.split('\n').filter(pattern => pattern.trim().length > 0)
      }
      // Default patterns for backward compatibility
      return ['ChatLo_Vaults', 'Post-Kernel-Test']
    } catch (error) {
      console.warn('🔍 [VAULT-SETTING] Could not read custom vault patterns, using defaults:', error)
      return ['ChatLo_Vaults', 'Post-Kernel-Test']
    }
  }

  private async createContextStructure(contextPath: string, contextName: string): Promise<void> {
    const fs = await import('fs')
    const path = await import('path')

    // Create context directory
    await fs.promises.mkdir(contextPath, { recursive: true })

    // Create subdirectories
    await fs.promises.mkdir(path.join(contextPath, 'documents'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, 'images'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, 'artifacts'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, '.intelligence'), { recursive: true })

    // Create master.md
    const masterContent = `# ${contextName}

Welcome to your intelligent context vault! This is your master document that serves as the central hub for organizing and understanding your project.

## Overview
This context vault helps you organize files, conversations, and AI insights in one place.

## Quick Start
1. **Add files** to the \`documents/\` folder to get started
2. **Start a conversation** using the chat interface with this context selected
3. **Organize insights** and progress notes right here in this document

## How It Works
- 📁 **Documents folder**: Store your project files here
- 🖼️ **Images folder**: Add screenshots, diagrams, and visual assets
- 🎯 **Artifacts folder**: Save important outputs from AI conversations
      - 🧠 **AI Memory**: The \`.intelligence/\` folder contains AI memory optimized for Gemma models

## AI Insights
*This section will be automatically updated as you add files and have conversations*

## Project Progress
*Use this space to track your progress and key milestones*

---
*Last updated: ${new Date().toLocaleString()}*
*Files: 0 | Conversations: 0*`

    await fs.promises.writeFile(path.join(contextPath, 'master.md'), masterContent, 'utf8')

    // Create context metadata
    const metadata = {
      id: contextName.toLowerCase().replace(/\s+/g, '-'),
      name: contextName,
      created: new Date().toISOString(),
      description: `Context vault for ${contextName}`,
      contextType: 'getting-started'
    }

    await fs.promises.writeFile(
      path.join(contextPath, '.intelligence', 'metadata.json'),
      JSON.stringify(metadata, null, 2),
      'utf8'
    )

    // Create AI memory file
    const memoryContent = `# AI Memory for ${contextName}

## Context Understanding
This context vault was just created and is ready for your first project.

## Key Concepts
*AI will learn and document key concepts from your files and conversations*

## Relationships
*Connections between files, ideas, and conversations will be tracked here*

## Memory Chunks
*Optimized memory chunks for Gemma models will be stored here*

---
*This file is automatically managed by ChatLo's AI system*`

    // TODO: Write the AI memory file (this was removed from registerCoreAPIs)
    // await fs.promises.writeFile(path.join(contextPath, '.intelligence', 'ai-memory.md'), memoryContent, 'utf8')
  }



  private setupIPC(): void {
    console.log('[MAIN] Setting up IPC handlers...')

    // Register core API categories
    console.log('[MAIN] Registering core APIs...')
    this.registerCoreAPIs()

    // Initialize API registry (sets up IPC handlers)
    console.log('[MAIN] Initializing API registry...')
    this.apiRegistry.initialize()
    console.log('[MAIN] API registry initialized successfully')

    // All database operations are now handled through APIRegistry

    // All intelligence and message operations are now handled through APIRegistry

    // All settings operations are now handled through APIRegistry

    // All file system operations are now handled through APIRegistry







    // All vault operations are now handled through APIRegistry


  }

  private setupAutoUpdater(): void {
    if (!isDev) {
      autoUpdater.checkForUpdatesAndNotify()
    }

    // Auto-updater events
    autoUpdater.on('checking-for-update', () => {
      console.log('Checking for update...')
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:checking-for-update')
      }
    })

    autoUpdater.on('update-available', (info) => {
      console.log('Update available:', info.version)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-available', info)
      }
    })

    autoUpdater.on('update-not-available', (info) => {
      console.log('Update not available')
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-not-available')
      }
    })

    autoUpdater.on('error', (err) => {
      console.error('Auto-updater error:', err)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:error', err.message)
      }
    })

    autoUpdater.on('download-progress', (progressObj) => {
      console.log(`Download progress: ${progressObj.percent}%`)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:download-progress', progressObj)
      }
    })

    autoUpdater.on('update-downloaded', (info) => {
      console.log('Update downloaded:', info.version)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-downloaded', info)
      }
    })
  }

  private setupCustomProtocol(): void {
    const { protocol } = require('electron')
    const fs = require('fs')
    const path = require('path')
    const mime = require('mime-types')

    protocol.registerFileProtocol('chatlo-file', (request, callback) => {
      try {
        // Extract file path from URL
        let encodedPath = request.url.substr(13) // Remove 'chatlo-file://'

        // Remove any leading slashes that might be added by the URL parsing
        if (encodedPath.startsWith('/')) {
          encodedPath = encodedPath.substr(1)
        }
        let filePath

        console.log('[PROTOCOL] Requested URL:', request.url)
        console.log('[PROTOCOL] Encoded path:', encodedPath)

        // Try to detect if it's base64 or URL encoded
        if (encodedPath.includes('%')) {
          // URL encoded (old method) - decode and fix path
          filePath = decodeURIComponent(encodedPath)
          console.log('[PROTOCOL] Using URL decoding')
        } else {
          // Base64 encoded (new method)
          try {
            filePath = Buffer.from(encodedPath, 'base64').toString('utf8')
            console.log('[PROTOCOL] Using Base64 decoding')
          } catch (e) {
            // Fallback to URL decoding if base64 fails
            filePath = decodeURIComponent(encodedPath)
            console.log('[PROTOCOL] Base64 failed, using URL decoding fallback')
          }
        }

        // Fix Windows path separators and normalize
        filePath = path.normalize(filePath.replace(/\//g, path.sep))

        console.log('[PROTOCOL] Decoded path:', filePath)
        console.log('[PROTOCOL] File exists:', fs.existsSync(filePath))

        // Validate file exists and is accessible
        if (!fs.existsSync(filePath)) {
          console.error('[PROTOCOL] File not found:', filePath)
          callback({ error: -6 }) // FILE_NOT_FOUND
          return
        }

        // Get MIME type
        const mimeType = mime.lookup(filePath) || 'application/octet-stream'
        console.log('[PROTOCOL] Serving file:', filePath, 'as', mimeType)

        callback({
          path: filePath,
          headers: {
            'Content-Type': mimeType,
            'Access-Control-Allow-Origin': '*'
          }
        })
      } catch (error) {
        console.error('[PROTOCOL] Error serving file:', error)
        callback({ error: -2 }) // FAILED
      }
    })

    console.log('[ELECTRON] Custom file protocol registered')
  }

  // Register core API endpoints
  private registerCoreAPIs(): void {
    try {
      console.log('[MAIN] Starting registerCoreAPIs...')

      // Database APIs
      console.log('[MAIN] Registering database APIs...')
      this.apiRegistry.registerCategory('db')

    // Conversation endpoints
    this.apiRegistry.registerEndpoint('db', 'getConversations',
      () => this.db.getConversations(),
      { description: 'Get all conversations' }
    )
    this.apiRegistry.registerEndpoint('db', 'getConversation',
      (id: string) => this.db.getConversation(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Get a specific conversation'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'createConversation',
      (title: string) => this.db.createConversation(title),
      {
        validator: (title: string) => {
          if (!this.validateInput(title, 'string', 200)) throw new Error('Invalid conversation title')
        },
        description: 'Create a new conversation'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'updateConversation',
      (id: string, title: string) => this.db.updateConversation(id, title),
      {
        validator: (id: string, title: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
          if (!this.validateInput(title, 'string', 200)) throw new Error('Invalid conversation title')
        },
        description: 'Update a conversation'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'deleteConversation',
      (id: string) => this.db.deleteConversation(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Delete a conversation'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'togglePinConversation',
      (id: string) => this.db.togglePinConversation(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Toggle pin status of a conversation'
      }
    )

    // Message endpoints
    this.apiRegistry.registerEndpoint('db', 'getMessages',
      (conversationId: string) => this.db.getMessages(conversationId),
      {
        validator: (conversationId: string) => {
          if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Get messages for a conversation'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'addMessage',
      (conversationId: string, message: any) => this.db.addMessage(conversationId, message),
      {
        validator: (conversationId: string, message: any) => {
          if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
          if (!message || typeof message !== 'object') throw new Error('Invalid message object')
        },
        description: 'Add a new message'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'togglePinMessage',
      (id: string) => this.db.togglePinMessage(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid message ID')
        },
        description: 'Toggle pin status of a message'
      }
    )

    // File endpoints
    this.apiRegistry.registerEndpoint('db', 'getFiles',
      () => this.db.getFiles(),
      { description: 'Get all files' }
    )
    this.apiRegistry.registerEndpoint('db', 'getFile',
      (id: string) => this.db.getFile(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid file ID')
        },
        description: 'Get a specific file'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'addFile',
      (file: any) => this.db.addFile(file),
      {
        validator: (file: any) => {
          if (!file || typeof file !== 'object') throw new Error('Invalid file object')
        },
        description: 'Add a new file'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'updateFile',
      (id: string, updates: any) => this.db.updateFile(id, updates),
      {
        validator: (id: string, updates: any) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid file ID')
          if (!updates || typeof updates !== 'object') throw new Error('Invalid updates object')
        },
        description: 'Update a file'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'deleteFile',
      (id: string) => this.db.deleteFile(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid file ID')
        },
        description: 'Delete a file'
      }
    )

    // Artifact endpoints
    this.apiRegistry.registerEndpoint('db', 'getArtifacts',
      (messageId: string) => this.db.getArtifacts(messageId),
      {
        validator: (messageId: string) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
        },
        description: 'Get artifacts for a message'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'addArtifact',
      (messageId: string, artifact: any) => this.db.addArtifact(messageId, artifact),
      {
        validator: (messageId: string, artifact: any) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
          if (!artifact || typeof artifact !== 'object') throw new Error('Invalid artifact object')
        },
        description: 'Add an artifact to a message'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'updateArtifact',
      (id: string, updates: any) => this.db.updateArtifact(id, updates),
      {
        validator: (id: string, updates: any) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid artifact ID')
          if (!updates || typeof updates !== 'object') throw new Error('Invalid updates object')
        },
        description: 'Update an artifact'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'removeArtifact',
      (id: string) => this.db.removeArtifact(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid artifact ID')
        },
        description: 'Remove an artifact'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'getConversationArtifacts',
      (conversationId: string) => this.db.getConversationArtifacts(conversationId),
      {
        validator: (conversationId: string) => {
          if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Get all artifacts for a conversation'
      }
    )

    // Intelligence endpoints
    this.apiRegistry.registerEndpoint('db', 'updateMessageIntelligence',
      (messageId: string, entities: string, topics: string, confidence: number) =>
        this.db.updateMessageIntelligence(messageId, entities, topics, confidence),
      {
        validator: (messageId: string, entities: string, topics: string, confidence: number) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
          if (!this.validateInput(entities, 'string', 5000)) throw new Error('Invalid entities')
          if (!this.validateInput(topics, 'string', 5000)) throw new Error('Invalid topics')
          if (typeof confidence !== 'number' || confidence < 0 || confidence > 1) throw new Error('Invalid confidence')
        },
        description: 'Update message intelligence data'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'addPinnedIntelligence',
      (messageId: string, extractionData: string, vaultAssignment: string, processingMetadata: string) =>
        this.db.addPinnedIntelligence(messageId, extractionData, vaultAssignment, processingMetadata),
      {
        validator: (messageId: string, extractionData: string, vaultAssignment: string, processingMetadata: string) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
          if (!this.validateInput(extractionData, 'string', 10000)) throw new Error('Invalid extraction data')
          if (!this.validateInput(vaultAssignment, 'string', 1000)) throw new Error('Invalid vault assignment')
          if (!this.validateInput(processingMetadata, 'string', 5000)) throw new Error('Invalid processing metadata')
        },
        description: 'Add pinned intelligence data'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'getPinnedIntelligence',
      (messageId: string) => this.db.getPinnedIntelligence(messageId),
      {
        validator: (messageId: string) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
        },
        description: 'Get pinned intelligence for a message'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'getAllPinnedIntelligence',
      () => this.db.getAllPinnedIntelligence(),
      { description: 'Get all pinned intelligence data' }
    )
    this.apiRegistry.registerEndpoint('db', 'searchConversations',
      (searchTerm: string) => this.db.searchConversationsAndMessages(searchTerm),
      {
        validator: (searchTerm: string) => {
          if (!this.validateInput(searchTerm, 'string', 200)) throw new Error('Invalid search term')
        },
        description: 'Search conversations and messages'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'getConversationsWithArtifacts',
      () => this.db.getConversationsWithArtifacts(),
      { description: 'Get conversations that have artifacts' }
    )

    // Database diagnostics
    this.apiRegistry.registerEndpoint('db', 'getDatabaseHealth',
      () => this.db.getDatabaseHealth(),
      { description: 'Get database health information' }
    )


    this.apiRegistry.registerEndpoint('db', 'createBackup',
      () => this.db.createBackup(),
      { description: 'Create a database backup' }
    )

    // Settings APIs
    this.apiRegistry.registerCategory('settings')
    this.apiRegistry.registerEndpoint('settings', 'get',
      (key: string) => this.db.getSetting(key),
      {
        validator: (key: string) => {
          if (!this.validateInput(key, 'string', 50)) throw new Error('Invalid settings key')
        },
        description: 'Get a setting value'
      }
    )
    this.apiRegistry.registerEndpoint('settings', 'set',
      (key: string, value: any) => this.db.setSetting(key, value),
      {
        validator: (key: string, value: any) => {
          if (!this.validateInput(key, 'string', 50)) throw new Error('Invalid settings key')
          if (value === undefined || value === null) throw new Error('Invalid setting value')
        },
        description: 'Set a setting value'
      }
    )

    // Portable Mode: validated setters/getters + db open/close
    this.apiRegistry.registerEndpoint('settings', 'setPortableMode',
      async (enabled: boolean) => {
        if (typeof enabled !== 'boolean') throw new Error('enabled must be boolean')
        this.db.setSetting('portable-mode-enabled', enabled)
        return { success: true }
      },
      { description: 'Enable/disable Portable Mode (no background processing). Renderer orchestrates flows.' }
    )

    this.apiRegistry.registerEndpoint('settings', 'setDBPath',
      async (dbPath: string) => {
        if (!this.validateInput(dbPath, 'string', 500)) throw new Error('Invalid dbPath')
        // Guard: must be inside current vault root
        const vaultRoot = this.fileSystem.getVaultRootPath()
        if (!vaultRoot) throw new Error('Vault root is not configured')
        const normalized = PathResolver.normalizePath(dbPath)
        const inVault = normalized.toLowerCase().startsWith(vaultRoot.toLowerCase())
        if (!inVault) throw new Error('dbPath must be within the current vault root')
        this.db.setSetting('db-path', normalized)
        return { success: true, dbPath: normalized }
      },
      { description: 'Set DB path for Portable Mode (must be under vault root)' }
    )

    this.apiRegistry.registerEndpoint('db', 'openAtPath',
      async (dbPath: string) => {
        if (!this.validateInput(dbPath, 'string', 500)) throw new Error('Invalid dbPath')
        const vaultRoot = this.fileSystem.getVaultRootPath()
        if (!vaultRoot) throw new Error('Vault root is not configured')
        const normalized = PathResolver.normalizePath(dbPath)
        const inVault = normalized.toLowerCase().startsWith(vaultRoot.toLowerCase())
        if (!inVault) throw new Error('dbPath must be within the current vault root')
        try {
          this.db.openAtPath(normalized)
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error?.message || String(error) }
        }
      },
      { description: 'Open database at specified path (Portable Mode). Closes current safely.' }
    )

    this.apiRegistry.registerEndpoint('db', 'safeClose',
      async () => { this.db.safeClose(); return { success: true } },
      { description: 'Safely close DB connections (for safe disconnect).'}
    )

    // Day 2: Safe connect/disconnect IPC + migration helper
    this.apiRegistry.registerEndpoint('db', 'prepareForDisconnect',
      async () => { this.db.safeClose(); return { success: true } },
      { description: 'Flush and close DB for safe device removal (user-triggered).'}
    )

    this.apiRegistry.registerEndpoint('db', 'connectPortableDB',
      async () => {
        try {
          const enabled = this.db.getSetting('portable-mode-enabled')
          if (!enabled) return { success: false, error: 'Portable Mode is not enabled' }
          const dbPath = this.db.getSetting('db-path')
          if (!dbPath) return { success: false, error: 'db-path is not configured' }
          const result = this.db.openAtPath(dbPath)
          return result
        } catch (error: any) {
          return { success: false, error: error?.message || String(error) }
        }
      },
      { description: 'Open DB at configured portable path (user-triggered).'}
    )

    this.apiRegistry.registerEndpoint('db', 'migrateToPortablePath',
      async () => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          const enabled = this.db.getSetting('portable-mode-enabled')
          if (!enabled) return { success: false, error: 'Portable Mode is not enabled' }
          const targetPath = this.db.getSetting('db-path')
          if (!targetPath) return { success: false, error: 'db-path is not configured' }

          // Ensure target directory exists
          const dir = path.dirname(targetPath)
          await fs.promises.mkdir(dir, { recursive: true })

          // Determine current DB path (DatabaseManager stores its path internally); we copy file
          // Heuristic: use current this.dbPath via DatabaseManager private field is not exposed.
          // Pragmatically, copy from userData/chatlo.db for prod; in dev, from chatlo-dev.db next to compiled main.
          const defaultSourcePath = isDev
            ? path.join(__dirname, '../chatlo-dev.db')
            : path.join(app.getPath('userData'), 'chatlo.db')

          // Copy to target
          await fs.promises.copyFile(defaultSourcePath, targetPath)

          // Validate by attempting to open
          const openResult = this.db.openAtPath(targetPath) ? { success: true } : { success: false }
          if (!openResult?.success) return { success: false, error: 'Integrity open check failed' }

          return { success: true }
        } catch (error: any) {
          return { success: false, error: error?.message || String(error) }
        }
      },
      { description: 'One-shot migration: copy local DB → portable db-path and integrity check by opening.'}
    )



    // Processing settings helpers
    this.apiRegistry.registerEndpoint('processing', 'enableOCR',
      async (enabled: boolean) => {
        try {
          const { pluginFileProcessor } = await import('./fileProcessors/PluginFileProcessor')
          pluginFileProcessor.setPluginEnabled('OCRPlugin', !!enabled)
          await this.db.setSetting('processing.enableOCR', !!enabled)
          return { success: true, enabled: !!enabled }
        } catch (error: any) {
          return { success: false, error: error?.message || 'Failed to set OCR state' }
        }
      },
      {
        validator: (enabled: any) => {
          if (typeof enabled !== 'boolean') throw new Error('enabled must be boolean')
        },
        description: 'Enable or disable OCR plugin at runtime'
      }
    )

    this.apiRegistry.registerEndpoint('processing', 'getProcessingStatus',
      async () => {
        try {
          const { pluginFileProcessor } = await import('./fileProcessors/PluginFileProcessor')
          const info = pluginFileProcessor.getPluginInfo()
          const ocr = info.find(p => p.name === 'OCRPlugin')
          const enabled = !!ocr?.enabled
          return { success: true, ocr: { enabled } }
        } catch (error: any) {
          return { success: false, error: error?.message || 'Failed to get processing status' }
        }
      },
      { description: 'Get current processing-related plugin status' }
    )

    // Vault APIs
    console.log('[MAIN] Registering vault APIs...')
    this.apiRegistry.registerCategory('vault')
    console.log('[MAIN] Vault category registered')

    this.apiRegistry.registerEndpoint('vault', 'createDirectory',
      async (dirPath: string) => {
        const fs = await import('fs')
        try {
          await fs.promises.mkdir(dirPath, { recursive: true })
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (dirPath: string) => {
          if (!this.validateInput(dirPath, 'string', 500)) throw new Error('Invalid directory path')
        },
        description: 'Create directory recursively'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'writeFile',
      async (filePath: string, content: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          // Normalize path to fix mixed separators (Windows compatibility)
          const normalizedPath = path.normalize(filePath)
          console.log('[VAULT-WRITE] Original path:', filePath)
          console.log('[VAULT-WRITE] Normalized path:', normalizedPath)

          // Ensure directory exists
          const dir = path.dirname(normalizedPath)
          console.log('[VAULT-WRITE] Creating directory:', dir)
          await fs.promises.mkdir(dir, { recursive: true })

          // Detect if content is base64 and handle accordingly
          const isBase64 = this.isBase64String(content)

          if (isBase64) {
            // Convert base64 to binary buffer for proper file writing
            const buffer = Buffer.from(content, 'base64')
            console.log('[VAULT-WRITE] Writing binary file:', normalizedPath)
            await fs.promises.writeFile(normalizedPath, buffer)
          } else {
            // Write as UTF-8 text
            console.log('[VAULT-WRITE] Writing text file:', normalizedPath, 'Length:', content.length)
            await fs.promises.writeFile(normalizedPath, content, 'utf8')
          }

          console.log('[VAULT-WRITE] File written successfully:', normalizedPath)

          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string, content: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(content, 'string', 200000000)) throw new Error('Invalid content')
        },
        description: 'Write content to file with base64 support'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'appendFile',
      async (filePath: string, content: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          // Ensure directory exists
          const dir = path.dirname(filePath)
          await fs.promises.mkdir(dir, { recursive: true })

          // Detect if content is base64 and handle accordingly
          const isBase64 = this.isBase64String(content)

          if (isBase64) {
            // Convert base64 to binary buffer for proper file appending
            const buffer = Buffer.from(content, 'base64')
            await fs.promises.appendFile(filePath, buffer)
          } else {
            // Append as UTF-8 text
            await fs.promises.appendFile(filePath, content, 'utf8')
          }

          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string, content: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(content, 'string', 200000000)) throw new Error('Invalid content')
        },
        description: 'Append content to file with base64 support'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'readDirectory',
      async (dirPath: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          const items = await fs.promises.readdir(dirPath, { withFileTypes: true })

          const result = await Promise.all(items.map(async (item) => {
            const itemPath = path.join(dirPath, item.name)
            const stats = await fs.promises.stat(itemPath)

            return {
              name: item.name,
              path: itemPath,
              isDirectory: item.isDirectory(),
              size: item.isFile() ? stats.size : undefined,
              modified: stats.mtime.toISOString()
            }
          }))

          return { success: true, items: result }
        } catch (error: any) {
          return { success: false, error: error.message, items: [] }
        }
      },
      {
        validator: (dirPath: string) => {
          if (!this.validateInput(dirPath, 'string', 500)) throw new Error('Invalid directory path')
        },
        description: 'Read directory contents'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'removeDirectory',
      async (dirPath: string) => {
        const fs = await import('fs')
        try {
          await fs.promises.rm(dirPath, { recursive: true, force: true })
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (dirPath: string) => {
          if (!this.validateInput(dirPath, 'string', 500)) throw new Error('Invalid directory path')
        },
        description: 'Remove directory recursively'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'removeFile',
      async (filePath: string) => {
        const fs = await import('fs')
        try {
          await fs.promises.unlink(filePath)
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Remove file'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'scanFolder',
      async (folderPath: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          // Check if folder exists
          const stats = await fs.promises.stat(folderPath)
          if (!stats.isDirectory()) {
            throw new Error('Path is not a directory')
          }

          // Read directory contents
          const entries = await fs.promises.readdir(folderPath, { withFileTypes: true })

          const files = await Promise.all(entries.map(async (entry) => {
            const fullPath = path.join(folderPath, entry.name)
            let fileStats

            try {
              fileStats = await fs.promises.stat(fullPath)
            } catch (error) {
              // Skip files that can't be accessed
              return null
            }

            return {
              name: entry.name,
              path: fullPath,
              isDirectory: entry.isDirectory(),
              size: entry.isDirectory() ? 0 : fileStats.size,
              lastModified: fileStats.mtime.toISOString()
            }
          }))

          // Filter out null entries (files that couldn't be accessed)
          const validFiles = files.filter(file => file !== null)

          return { success: true, files: validFiles }
        } catch (error: any) {
          return { success: false, error: error.message, files: [] }
        }
      },
      {
        validator: (folderPath: string) => {
          if (!this.validateInput(folderPath, 'string', 500)) throw new Error('Invalid folder path')
        },
        description: 'Scan folder for files'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'copyFile',
      async (sourcePath: string, destinationPath: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          // Ensure destination directory exists
          const destDir = path.dirname(destinationPath)
          await fs.promises.mkdir(destDir, { recursive: true })

          // Copy the file (handles binary files properly)
          await fs.promises.copyFile(sourcePath, destinationPath)

          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (sourcePath: string, destinationPath: string) => {
          if (!this.validateInput(sourcePath, 'string', 500)) throw new Error('Invalid source path')
          if (!this.validateInput(destinationPath, 'string', 500)) throw new Error('Invalid destination path')
        },
        description: 'Copy file from source to destination'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'pathExists',
      async (targetPath: string) => {
        const fs = await import('fs')
        try {
          console.log('🔍 [VAULT-SETTING] === PATH EXISTS START ===')
          console.log('🔍 [VAULT-SETTING] Target path:', targetPath)
          console.log('🔍 [VAULT-SETTING] Target path type:', typeof targetPath)
          console.log('🔍 [VAULT-SETTING] Target path length:', targetPath ? targetPath.length : 'null/undefined')
          
          // Log path analysis for debugging
          if (targetPath) {
            console.log('🔍 [VAULT-SETTING] Path analysis:', {
              containsWindowsDrive: /^[A-Z]:\\/.test(targetPath),
              containsUnixStyle: /^\/[A-Z]:\//.test(targetPath),
              containsUserDir: /C:\\Users\\<USER>\\\\/.test(targetPath),
              containsUndefined: /undefined/.test(targetPath),
              containsNull: /null/.test(targetPath),
              normalized: targetPath.replace(/\\/g, '/')
            })
          }
          
          // Check if path exists
          console.log('🔍 [VAULT-SETTING] Checking file system access...')
          await fs.promises.access(targetPath)
          console.log('🔍 [VAULT-SETTING] ✅ Path exists and is accessible')
          console.log('🔍 [VAULT-SETTING] === PATH EXISTS END (SUCCESS) ===')
          return { exists: true }
        } catch (error: any) {
          console.log('🔍 [VAULT-SETTING] ❌ Path does not exist or is not accessible')
          console.log('🔍 [VAULT-SETTING] Error details:', {
            message: error.message,
            code: error.code,
            errno: error.errno
          })
          console.log('🔍 [VAULT-SETTING] === PATH EXISTS END (NOT EXISTS) ===')
          return { exists: false, error: error.message }
        }
      },
      {
        validator: (targetPath: string) => {
          console.log('🔍 [VAULT-SETTING] === PATH EXISTS VALIDATION START ===')
          console.log('🔍 [VAULT-SETTING] Validating input path:', targetPath)
          
          if (!this.validateInput(targetPath, 'string', 500)) {
            console.log('🔍 [VAULT-SETTING] ❌ Input validation failed')
            console.log('🔍 [VAULT-SETTING] === PATH EXISTS VALIDATION END (FAILED) ===')
            throw new Error('Invalid path')
          }
          
          console.log('🔍 [VAULT-SETTING] ✅ Input validation passed')
          console.log('🔍 [VAULT-SETTING] === PATH EXISTS VALIDATION END (SUCCESS) ===')
        },
        description: 'Check if path exists'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'readFile',
      async (filePath: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          // Normalize path to fix mixed separators (Windows compatibility)
          const normalizedPath = path.normalize(filePath)
          console.log('[VAULT-READ] Original path:', filePath)
          console.log('[VAULT-READ] Normalized path:', normalizedPath)

          const content = await fs.promises.readFile(normalizedPath, 'utf8')
          console.log('[VAULT-READ] File read successfully:', normalizedPath, 'Length:', content.length)
          return { success: true, content }
        } catch (error: any) {
          console.log('[VAULT-READ] File read failed:', filePath, 'Error:', error.message)
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Read file content'
      }
    )

    // Binary-safe read: returns base64 for PDFs/images and other binary files
    this.apiRegistry.registerEndpoint('vault', 'readFileBase64',
      async (filePath: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          const normalizedPath = path.normalize(filePath)
          const buffer = await fs.promises.readFile(normalizedPath)
          const contentBase64 = buffer.toString('base64')
          return { success: true, contentBase64 }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Read file content as base64 (binary-safe)'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'getVaultRegistry',
      async () => {
        const fs = await import('fs')
        const path = await import('path')
        const os = await import('os')
        try {
          console.log('🔍 [VAULT-SETTING] === MAIN.TS GET VAULT REGISTRY START ===')
          
          // First try to get saved vault root path from database
          let vaultRoot = this.db.getSetting('vault-root-path')
          console.log('🔍 [VAULT-SETTING] Database setting "vault-root-path":', vaultRoot)
          console.log('🔍 [VAULT-SETTING] Database setting type:', typeof vaultRoot)
          console.log('🔍 [VAULT-SETTING] Database setting length:', vaultRoot ? vaultRoot.length : 'null/undefined')

          // Fall back to default if no saved path
          if (!vaultRoot) {
            vaultRoot = path.join(os.homedir(), 'Documents', 'ChatLo_Vaults')
            console.log('🔍 [VAULT-SETTING] ⚠️ No database setting found, using default vault root:', vaultRoot)
            console.log('🔍 [VAULT-SETTING] Default path components:', {
              homedir: os.homedir(),
              documents: 'Documents',
              chatloVaults: 'ChatLo_Vaults'
            })
            // Persist default for consistency across services
            try { 
              this.db.setSetting('vault-root-path', vaultRoot) 
              console.log('🔍 [VAULT-SETTING] ✅ Default vault root saved to database')
            } catch (dbError) {
              console.error('🔍 [VAULT-SETTING] ❌ Failed to save default vault root to database:', dbError)
            }
          }

          console.log('🔍 [VAULT-SETTING] Final vault root path:', vaultRoot)
          console.log('🔍 [VAULT-SETTING] Final path type:', typeof vaultRoot)
          console.log('🔍 [VAULT-SETTING] Final path normalized:', path.normalize(vaultRoot))

          const registryPath = path.join(vaultRoot, '.chatlo', 'vault-registry.json')
          console.log('🔍 [VAULT-SETTING] Registry file path:', registryPath)
          console.log('🔍 [VAULT-SETTING] Registry file normalized:', path.normalize(registryPath))

          // Check if registry file exists
          const registryExists = await fs.promises.access(registryPath).then(() => true).catch(() => false)
          console.log('🔍 [VAULT-SETTING] Registry file exists:', registryExists)

          if (!registryExists) {
            console.log('🔍 [VAULT-SETTING] ⚠️ Registry file not found, returning null')
            console.log('🔍 [VAULT-SETTING] === MAIN.TS GET VAULT REGISTRY END (NO FILE) ===')
            return null
          }

          // Read and parse registry file
          console.log('🔍 [VAULT-SETTING] Reading registry file...')
          const content = await fs.promises.readFile(registryPath, 'utf8')
          console.log('🔍 [VAULT-SETTING] Registry file size:', content.length, 'characters')
          console.log('🔍 [VAULT-SETTING] Registry file preview (first 200 chars):', content.substring(0, 200))
          
          const registry = JSON.parse(content)
          console.log('🔍 [VAULT-SETTING] Registry parsed successfully')
          console.log('🔍 [VAULT-SETTING] Registry structure:', {
            hasVaultRoot: !!registry.vaultRoot,
            vaultRoot: registry.vaultRoot,
            vaultsCount: registry.vaults?.length || 0,
            hasContexts: registry.vaults?.some((v: any) => v.contexts) || false
          })
          
          if (registry.vaults && Array.isArray(registry.vaults)) {
            registry.vaults.forEach((vault: any, index: number) => {
              console.log(`🔍 [VAULT-SETTING] Vault ${index + 1} in registry:`, {
                name: vault.name,
                path: vault.path,
                pathType: typeof vault.path,
                contextsCount: vault.contexts?.length || 0
              })
            })
          }
          
          console.log('🔍 [VAULT-SETTING] === MAIN.TS GET VAULT REGISTRY END (SUCCESS) ===')
          return registry
        } catch (error: any) {
          console.error('🔍 [VAULT-SETTING] 💥 Error getting vault registry:', error)
          console.log('🔍 [VAULT-SETTING] === MAIN.TS GET VAULT REGISTRY END (ERROR) ===')
          return null
        }
      },
      { description: 'Get vault registry configuration' }
    )

    this.apiRegistry.registerEndpoint('vault', 'saveVaultRegistry',
      async (registry: any) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          const registryPath = path.join(registry.vaultRoot, '.chatlo', 'vault-registry.json')
          const registryDir = path.dirname(registryPath)

          // Ensure directory exists
          await fs.promises.mkdir(registryDir, { recursive: true })

          // Save registry
          await fs.promises.writeFile(registryPath, JSON.stringify(registry, null, 2), 'utf8')
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (registry: any) => {
          if (!registry || typeof registry !== 'object') throw new Error('Invalid registry object')
        },
        description: 'Save vault registry configuration'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'initializeVaultRoot',
      async (rootPath: string, template: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          // ✅ UUID CHECK LOGIC: Check if vault registry already exists
          console.log('=== MAIN.TS UUID CHECK LOGIC ===')
          console.log('Checking for existing vault registry at:', rootPath)

          const registryPath = path.join(rootPath, '.chatlo', 'vault-registry.json')
          const registryExists = await fs.promises.access(registryPath).then(() => true).catch(() => false)

          if (registryExists) {
            console.log('✅ Found existing vault registry file')
            try {
              const content = await fs.promises.readFile(registryPath, 'utf8')
              const registry = JSON.parse(content)
              console.log('Existing vaults:', registry.vaults?.length || 0)
              console.log('Skipping template creation - using existing vaults')
              console.log('================================')

              return {
                success: true,
                data: {
                  success: true,
                  vaults: registry.vaults || [],
                  error: undefined
                }
              }
            } catch (parseError) {
              console.log('Registry file exists but corrupted, proceeding with template creation')
            }
          }

          console.log('No existing registry found - proceeding with template creation')
          console.log('================================')

          // Create root directory if it doesn't exist
          await fs.promises.mkdir(rootPath, { recursive: true })

          // Create basic vault structure based on template
          const vaults: any[] = []

          if (template === 'default') {
            // Create Personal and Work vaults
            const personalPath = path.join(rootPath, 'personal-vault')
            const workPath = path.join(rootPath, 'work-vault')

            await fs.promises.mkdir(personalPath, { recursive: true })
            await fs.promises.mkdir(workPath, { recursive: true })

            // Create getting-started context in personal vault
            const gettingStartedPath = path.join(personalPath, 'getting-started')
            await this.createContextStructure(gettingStartedPath, 'Your First Context Vault')

            // Create projects context in work vault
            const projectsPath = path.join(workPath, 'projects')
            await this.createContextStructure(projectsPath, 'Projects')

            vaults.push(
              { id: 'personal-vault', name: 'Personal Vault', path: personalPath },
              { id: 'work-vault', name: 'Work Vault', path: workPath }
            )
          } else if (template === 'simple') {
            // Create single vault
            const vaultPath = path.join(rootPath, 'my-vault')
            await fs.promises.mkdir(vaultPath, { recursive: true })

            // Create getting-started context
            const gettingStartedPath = path.join(vaultPath, 'getting-started')
            await this.createContextStructure(gettingStartedPath, 'Getting Started')

            vaults.push({ id: 'my-vault', name: 'My Vault', path: vaultPath })
          }

          return {
            success: true,
            data: { success: true, vaults }
          }
        } catch (error: any) {
          return {
            success: false,
            error: error.message,
            data: { success: false, vaults: [], error: error.message }
          }
        }
      },
      {
        validator: (rootPath: string, template: string) => {
          if (!this.validateInput(rootPath, 'string', 500)) throw new Error('Invalid root path')
          if (!this.validateInput(template, 'string', 50)) throw new Error('Invalid template')
        },
        description: 'Initialize vault root with template'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'scanContexts',
      async (vaultPath: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          const items = await fs.promises.readdir(vaultPath, { withFileTypes: true })
          const contexts: any[] = []

          for (const item of items) {
            if (item.isDirectory() && !item.name.startsWith('.')) {
              const contextPath = path.join(vaultPath, item.name)
              const masterPath = path.join(contextPath, 'master.md')

              // Check if master.md exists
              const masterExists = await fs.promises.access(masterPath).then(() => true).catch(() => false)

              if (masterExists) {
                contexts.push({
                  id: item.name,
                  name: item.name,
                  path: contextPath,
                  hasMaster: true
                })
              }
            }
          }

          return { success: true, contexts }
        } catch (error: any) {
          return { success: false, contexts: [], error: error.message }
        }
      },
      {
        validator: (vaultPath: string) => {
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
        },
        description: 'Scan vault for contexts'
      }
    )

    console.log('[MAIN] All vault APIs registered successfully')

    // File System APIs
    console.log('[MAIN] Registering file system APIs...')
    this.apiRegistry.registerCategory('files')

    // New vault-first endpoints
    this.apiRegistry.registerEndpoint('files', 'getVaultRootPath',
      () => {
        return this.fileSystem.getVaultRootPath()
      },
      { description: 'Get the current vault root path' }
    )

    this.apiRegistry.registerEndpoint('files', 'setVaultRootPath',
      async (newPath: string) => {
        try {
          // SECURITY: Validate the new path for security vulnerabilities
          if (!this.validateInput(newPath, 'string', 500)) {
            throw new Error('Invalid path format')
          }
          
          // SECURITY: Additional path security checks - only block malicious patterns
          if (newPath.match(/^[A-Z]:\\/) || newPath.match(/^\/[A-Z]:\//)) {
            throw new Error('Absolute Windows paths are not allowed for security reasons')
          }
          
          if (newPath.includes('..') || newPath.includes('\\\\')) {
            throw new Error('Directory traversal is not allowed for security reasons')
          }
          
          // SECURITY: Only block malicious user directory paths, not legitimate ones
          if (newPath.includes('C:\\Users\\<USER>