<html><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>tailwind.config = {
  "theme": {
    "extend": {
      "colors": {
        "primary": "#8AB0BB",
        "secondary": "#FF8383",
        "tertiary": "#1B3E68",
        "supplement1": "#D5D8E0",
        "supplement2": "#89AFBA"
      },
      "fontFamily": {
        "sans": [
          "Inter",
          "sans-serif"
        ]
      }
    }
  }
};</script>
    <script> window.FontAwesomeConfig = { autoReplaceSvg: 'nest'};</script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <style>
        ::-webkit-scrollbar { display: none;}
        body { font-family: 'Inter', sans-serif; }
        .chat-bubble {
            animation: fadeInUp 0.3s ease-out;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&amp;display=swap"><style>
  .highlighted-section {
    outline: 2px solid #3F20FB;
    background-color: rgba(63, 32, 251, 0.1);
  }

  .edit-button {
    position: absolute;
    z-index: 1000;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  html, body {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  </style></head>
<body class="bg-gray-900 text-white overflow-hidden">
    
    <div id="app-container" class="flex h-screen">
        
        <!-- VSCode-style Icon Bar -->
        <div id="icon-bar" class="w-12 bg-gray-900 border-r border-tertiary flex flex-col items-center py-2">
            <!-- Top Navigation Icons -->
            <div class="flex flex-col gap-1 mb-auto">
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i class="fa-solid fa-home"></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Home
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative bg-primary/20 border-l-2 border-primary">
                    <i class="fa-solid fa-comment text-primary"></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Chat
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i class="fa-solid fa-clock-rotate-left"></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        History
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i class="fa-solid fa-folder-tree"></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Files
                    </div>
                </button>
            </div>
            
            <!-- Bottom Navigation Icons -->
            <div class="flex flex-col gap-1">
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i class="fa-solid fa-user"></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Profile
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i class="fa-solid fa-gear"></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Settings
                    </div>
                </button>
            </div>
        </div>
        
        <!-- Left Sidebar -->
        <div id="left-sidebar" class="w-64 bg-gray-800 border-r border-tertiary flex flex-col">
            
            <!-- Logo/Header -->
            <div id="sidebar-header" class="p-4 border-b border-tertiary">
                <h1 class="text-xl font-bold text-primary">Dark Chat</h1>
            </div>
            
            <!-- New Chat Button -->
            <div id="new-chat-section" class="p-4">
                <button class="w-full bg-primary hover:bg-primary/80 text-gray-900 font-medium py-3 px-4 rounded-lg flex items-center gap-3 transition-colors">
                    <i class="fa-solid fa-plus"></i>
                    New Chat
                </button>
            </div>
            
            <!-- Chat History -->
            <div id="chat-history" class="flex-1 px-4 py-2 overflow-y-auto">
                <h3 class="text-sm font-medium text-supplement1 mb-3">Recent Chats</h3>
                <div class="space-y-2">
                    <div class="p-3 rounded-lg bg-gray-700/50 hover:bg-gray-700 cursor-pointer transition-colors">
                        <p class="text-sm font-medium truncate">Project Discussion</p>
                        <p class="text-xs text-gray-400 mt-1">2 hours ago</p>
                    </div>
                    <div class="p-3 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                        <p class="text-sm font-medium truncate">Team Meeting Notes</p>
                        <p class="text-xs text-gray-400 mt-1">Yesterday</p>
                    </div>
                    <div class="p-3 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                        <p class="text-sm font-medium truncate">Code Review</p>
                        <p class="text-xs text-gray-400 mt-1">3 days ago</p>
                    </div>
                </div>
            </div>
            
            <!-- User Profile -->
            <div id="user-profile" class="p-4 border-t border-tertiary">
    <div class="flex items-center gap-3 mb-4">
        <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
            <i class="fa-solid fa-wifi text-gray-900 text-sm"></i>
        </div>
        <div class="flex-1 flex items-center">
            <p class="text-xs text-gray-400">Online</p>
        </div>
    </div>
    
    <!-- Private Mode Toggle -->
    <div class="bg-gray-700/50 rounded-lg p-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
                <span class="text-sm font-medium text-supplement1">Private Mode</span>
                <button class="group relative">
                    <i class="fa-solid fa-info-circle text-gray-400 text-xs hover:text-supplement1 transition-colors"></i>
                    <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap border border-tertiary shadow-lg w-64">
                        Local models only. All documents are not sharing with large language models
                    </div>
                </button>
            </div>
            <div class="flex items-center gap-2">
                <span class="text-xs text-secondary font-medium">ON</span>
                <button class="relative inline-flex h-6 w-11 items-center rounded-full bg-secondary transition-colors">
                    <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6"></span>
                </button>
            </div>
        </div>
    </div>
</div>
        </div>
        
        <!-- Main Chat Area -->
        <div id="main-chat" class="flex-1 flex flex-col">
            
            <!-- Chat Header -->
            <div id="chat-header" class="bg-gray-800 border-b border-tertiary px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-secondary rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-robot text-white"></i>
                        </div>
                        <div>
                            <h2 class="font-semibold">AI Assistant</h2>
                            <p class="text-sm text-gray-400">Ready to help</p>
                        </div>
                    </div>
                    <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors">
                        <i class="fa-solid fa-ellipsis-vertical text-gray-400"></i>
                    </button>
                </div>
            </div>
            
            <!-- Chat Messages -->
            <div id="chat-messages" class="flex-1 overflow-y-auto p-6 space-y-4">
                
                <!-- Welcome Message -->
                <div class="flex justify-center">
                    <div class="bg-gray-800 px-4 py-2 rounded-full text-sm text-gray-400">
                        Today
                    </div>
                </div>
                
                <!-- AI Message -->
                <div class="flex gap-3 chat-bubble">
                    <div class="w-8 h-8 bg-secondary rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fa-solid fa-robot text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <div class="bg-gray-800 rounded-2xl rounded-tl-md p-4 max-w-lg">
                            <p class="text-supplement1">Hello! I'm your AI assistant. How can I help you today?</p>
                        </div>
                        <p class="text-xs text-gray-400 mt-1 ml-1">10:30 AM</p>
                    </div>
                </div>
                
                <!-- User Message -->
                <div class="flex gap-3 justify-end chat-bubble">
                    <div class="flex-1 flex justify-end">
                        <div class="bg-primary rounded-2xl rounded-tr-md p-4 max-w-lg">
                            <p class="text-gray-900">I need help with creating a modern dashboard design. Can you provide some guidance?</p>
                        </div>
                    </div>
                    <img src="https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/avatar-2.jpg" alt="User" class="w-8 h-8 rounded-full flex-shrink-0">
                </div>
                
                <!-- AI Response -->
                <div class="flex gap-3 chat-bubble">
                    <div class="w-8 h-8 bg-secondary rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fa-solid fa-robot text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <div class="bg-gray-800 rounded-2xl rounded-tl-md p-4 max-w-lg">
                            <p class="text-supplement1">Absolutely! I'd be happy to help you create a modern dashboard design. Here are some key principles to consider:</p>
                            <ul class="mt-2 space-y-1 text-supplement1">
                                <li>• Clean, minimalist layout</li>
                                <li>• Consistent color scheme</li>
                                <li>• Clear data visualization</li>
                                <li>• Responsive design</li>
                            </ul>
                        </div>
                        <p class="text-xs text-gray-400 mt-1 ml-1">10:32 AM</p>
                    </div>
                </div>
            </div>
            
            <!-- Message Input -->
            <div id="message-input" class="border-t border-tertiary p-4">
    <div class="flex gap-3 items-center">
        <button class="p-2 text-gray-400 hover:text-supplement1 transition-colors">
            <i class="fa-solid fa-paperclip"></i>
        </button>
        <div class="flex-1 bg-gray-800 rounded-2xl border border-tertiary">
            <textarea placeholder="Type your message..." class="w-full bg-transparent px-4 py-3 resize-none focus:outline-none text-supplement1 placeholder-gray-400" rows="1"></textarea>
        </div>
        <button class="p-2 text-gray-400 hover:text-supplement1 transition-colors group relative">
            <i class="fa-solid fa-sliders"></i>
            <div class="absolute bottom-12 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                Chat Settings
            </div>
        </button>
        <button class="bg-primary hover:bg-primary/80 text-gray-900 w-10 h-10 rounded-xl transition-colors flex items-center justify-center">
            <i class="fa-solid fa-paper-plane"></i>
        </button>
    </div>
    <div class="mt-2 text-xs text-gray-400 flex items-center gap-2">
        <span>Model: DeepSeek V3 Base (free)</span>
        <span>•</span>
        <span>Temp: 0.1</span>
        <span>•</span>
        <span>Max: 4,096</span>
        <span>•</span>
        <span>Top-P: 0.90</span>
        <span class="ml-auto">Shift+Enter for new line</span>
    </div>
</div>
        </div>
        
        <!-- Right Sidebar - Artifacts -->
        <div id="right-sidebar" class="w-80 bg-gray-800 border-l border-tertiary flex flex-col">
            
            <!-- Artifacts Header -->
            <div id="artifacts-header" class="p-4 border-b border-tertiary">
    <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold text-supplement1">Artifacts</h2>
        <div class="flex items-center gap-1">
            <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors">
                <i class="fa-solid fa-expand text-gray-400"></i>
            </button>
            <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors">
                <i class="fa-solid fa-xmark text-gray-400"></i>
            </button>
        </div>
    </div>
</div>
            
            <!-- Artifacts Content -->
            <div id="artifacts-content" class="flex-1 flex flex-col overflow-hidden">
    
    <!-- Filter Labels Row -->
    <div class="px-4 py-2 border-b border-tertiary/50">
        <div class="flex gap-2">
            <div class="bg-primary/20 text-primary border border-primary/30 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                <i class="fa-solid fa-code text-xs"></i>
                Code <span class="bg-primary/30 px-1 rounded">3</span>
            </div>
            <div class="bg-supplement2/20 text-supplement2 border border-supplement2/30 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                <i class="fa-solid fa-file-text text-xs"></i>
                Doc <span class="bg-supplement2/30 px-1 rounded">2</span>
            </div>
            <div class="bg-secondary/20 text-secondary border border-secondary/30 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                <i class="fa-brands fa-markdown text-xs"></i>
                MD <span class="bg-secondary/30 px-1 rounded">1</span>
            </div>
        </div>
    </div>
    
    <!-- Action Icons Row -->
    <div class="px-4 py-2 border-b border-tertiary/50">
        <div class="flex gap-1">
            <button class="p-1.5 hover:bg-gray-700 rounded transition-colors group relative">
                <i class="fa-solid fa-eye text-gray-400 text-xs"></i>
                <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Render
                </div>
            </button>
            <button class="p-1.5 hover:bg-gray-700 rounded transition-colors group relative">
                <i class="fa-solid fa-code text-gray-400 text-xs"></i>
                <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Source
                </div>
            </button>
            <button class="p-1.5 hover:bg-gray-700 rounded transition-colors group relative">
                <i class="fa-solid fa-play text-gray-400 text-xs"></i>
                <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Play
                </div>
            </button>
            <button class="p-1.5 hover:bg-gray-700 rounded transition-colors group relative">
                <i class="fa-solid fa-copy text-gray-400 text-xs"></i>
                <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Copy
                </div>
            </button>
            <button class="p-1.5 hover:bg-gray-700 rounded transition-colors group relative">
                <i class="fa-brands fa-html5 text-gray-400 text-xs"></i>
                <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    HTML
                </div>
            </button>
            <button class="p-1.5 hover:bg-gray-700 rounded transition-colors group relative">
                <i class="fa-solid fa-download text-gray-400 text-xs"></i>
                <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Download
                </div>
            </button>
            <button class="p-1.5 hover:bg-gray-700 rounded transition-colors group relative ml-auto">
                <i class="fa-solid fa-sort text-gray-400 text-xs"></i>
                <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Sort
                </div>
            </button>
        </div>
    </div>
    
    <!-- Artifacts List -->
    <div class="flex-1 p-4 overflow-y-auto">
        
        <!-- Code Artifact -->
        <div class="bg-gray-700/50 rounded-lg p-4 mb-4">
            <div class="flex items-center gap-2 mb-3">
                <i class="fa-solid fa-code text-supplement2"></i>
                <h3 class="font-medium text-supplement1">Dashboard Component</h3>
            </div>
            <div class="bg-gray-900 rounded p-3 text-sm font-mono">
                <div class="text-supplement2">const Dashboard = () =&gt; {</div>
                <div class="text-gray-400 ml-4">return (</div>
                <div class="text-gray-400 ml-8">&lt;div className="dashboard"&gt;</div>
                <div class="text-gray-400 ml-12">...</div>
                <div class="text-gray-400 ml-8">&lt;/div&gt;</div>
                <div class="text-gray-400 ml-4">);</div>
                <div class="text-supplement2">};</div>
            </div>
        </div>
        
        <!-- Image Artifact -->
        <div class="bg-gray-700/50 rounded-lg p-4 mb-4">
            <div class="flex items-center gap-2 mb-3">
                <i class="fa-solid fa-image text-supplement2"></i>
                <h3 class="font-medium text-supplement1">Design Mockup</h3>
            </div>
            <div class="bg-gray-600 rounded h-32 flex items-center justify-center">
                <i class="fa-solid fa-image text-gray-400 text-2xl"></i>
            </div>
        </div>
        
        <!-- Text Artifact -->
        <div class="bg-gray-700/50 rounded-lg p-4">
            <div class="flex items-center gap-2 mb-3">
                <i class="fa-solid fa-file-text text-supplement2"></i>
                <h3 class="font-medium text-supplement1">Design Guidelines</h3>
            </div>
            <p class="text-sm text-gray-300 leading-relaxed">
                Modern dashboard design principles focusing on user experience, accessibility, and visual hierarchy...
            </p>
        </div>
    </div>
</div>
        </div>
    </div>


</body></html>