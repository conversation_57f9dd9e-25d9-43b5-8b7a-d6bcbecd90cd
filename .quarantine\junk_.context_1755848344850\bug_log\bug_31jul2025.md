FileActionsPanel.tsx:306 Could not find icon undefined
log @ @fortawesome_react-fontawesome.js?v=b703ff76:4005
FontAwesomeIcon @ @fortawesome_react-fontawesome.js?v=b703ff76:4082
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateForwardRef @ react-dom_client.js?v=b703ff76:6461
beginWork @ react-dom_client.js?v=b703ff76:7864
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<FontAwesomeIcon>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FileActionsPanel.tsx:306
(anonymous) @ FileActionsPanel.tsx:290
FileActionsPanel @ FileActionsPanel.tsx:270
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<FileActionsPanel>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
FileOperationsHub @ FileOperationsHub.tsx:337
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<FileOperationsHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FilesPage.tsx:930
FilesPage @ FilesPage.tsx:955
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<FilesPage>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
App @ App.tsx:194
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ main.tsx:23
Not allowed to load local resource: <URL>
Not allowed to load local resource: <URL>
Not allowed to load local resource: <URL>
Not allowed to load local resource: <URL>
Not allowed to load local resource: <URL>
Not allowed to load local resource: <URL>
Not allowed to load local resource: <URL>
FilePreviewPane.tsx:216 Not allowed to load local resource: file:///C:/Users/<USER>/Documents/Test18/personal-vault/omg--it-works/C:/Users/<USER>/Documents/Test18/personal-vault/omg--it-works/1753852868479_Dentsu_Data_Consciousness_Project_2022.pdf
insertOrAppendPlacementNode @ react-dom_client.js?v=b703ff76:8773
commitPlacement @ react-dom_client.js?v=b703ff76:8804
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
commitReconciliationEffects @ react-dom_client.js?v=b703ff76:9662
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9503
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9551
flushMutationEffects @ react-dom_client.js?v=b703ff76:11098
commitRoot @ react-dom_client.js?v=b703ff76:11079
commitRootWhenReady @ react-dom_client.js?v=b703ff76:10512
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10457
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=b703ff76:11623
performWorkUntilDeadline @ react-dom_client.js?v=b703ff76:36
<iframe>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
renderPreview @ FilePreviewPane.tsx:216
FilePreviewPane @ FilePreviewPane.tsx:411
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=b703ff76:11623
performWorkUntilDeadline @ react-dom_client.js?v=b703ff76:36
<FilePreviewPane>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
FileOperationsHub @ FileOperationsHub.tsx:316
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<FileOperationsHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FilesPage.tsx:930
FilesPage @ FilesPage.tsx:955
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<FilesPage>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
App @ App.tsx:194
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ main.tsx:23
FilePreviewPane.tsx:216 Not allowed to load local resource: file:///C:/Users/<USER>/Documents/Test18/personal-vault/omg--it-works/C:/Users/<USER>/Documents/Test18/personal-vault/omg--it-works/1753852868479_Dentsu_Data_Consciousness_Project_2022.pdf
insertOrAppendPlacementNode @ react-dom_client.js?v=b703ff76:8773
commitPlacement @ react-dom_client.js?v=b703ff76:8804
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
commitReconciliationEffects @ react-dom_client.js?v=b703ff76:9662
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9503
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9551
flushMutationEffects @ react-dom_client.js?v=b703ff76:11098
commitRoot @ react-dom_client.js?v=b703ff76:11079
commitRootWhenReady @ react-dom_client.js?v=b703ff76:10512
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10457
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=b703ff76:11623
performWorkUntilDeadline @ react-dom_client.js?v=b703ff76:36
<iframe>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
renderPreview @ FilePreviewPane.tsx:216
FilePreviewPane @ FilePreviewPane.tsx:411
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=b703ff76:11623
performWorkUntilDeadline @ react-dom_client.js?v=b703ff76:36
<FilePreviewPane>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
FileOperationsHub @ FileOperationsHub.tsx:316
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<FileOperationsHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FilesPage.tsx:930
FilesPage @ FilesPage.tsx:955
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<FilesPage>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
App @ App.tsx:194
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ main.tsx:23
FilePreviewPane.tsx:216 Not allowed to load local resource: file:///C:/Users/<USER>/Documents/Test18/personal-vault/omg--it-works/C:/Users/<USER>/Documents/Test18/personal-vault/omg--it-works/1753852868479_Dentsu_Data_Consciousness_Project_2022.pdf
insertOrAppendPlacementNode @ react-dom_client.js?v=b703ff76:8773
commitPlacement @ react-dom_client.js?v=b703ff76:8804
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
commitReconciliationEffects @ react-dom_client.js?v=b703ff76:9662
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9503
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9551
flushMutationEffects @ react-dom_client.js?v=b703ff76:11098
commitRoot @ react-dom_client.js?v=b703ff76:11079
commitRootWhenReady @ react-dom_client.js?v=b703ff76:10512
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10457
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=b703ff76:11623
performWorkUntilDeadline @ react-dom_client.js?v=b703ff76:36
<iframe>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
renderPreview @ FilePreviewPane.tsx:216
FilePreviewPane @ FilePreviewPane.tsx:411
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=b703ff76:11623
performWorkUntilDeadline @ react-dom_client.js?v=b703ff76:36
<FilePreviewPane>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
FileOperationsHub @ FileOperationsHub.tsx?t=1753945528701:323
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
flushSyncWork$1 @ react-dom_client.js?v=b703ff76:10567
scheduleRefresh @ react-dom_client.js?v=b703ff76:372
(anonymous) @ @react-refresh:228
performReactRefresh @ @react-refresh:217
(anonymous) @ @react-refresh:586
<FileOperationsHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FilesPage.tsx:930
FilesPage @ FilesPage.tsx:955
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
FilePreviewPane.tsx:216 Not allowed to load local resource: file:///C:/Users/<USER>/Documents/Test18/personal-vault/omg--it-works/C:/Users/<USER>/Documents/Test18/personal-vault/omg--it-works/1753852868479_Dentsu_Data_Consciousness_Project_2022.pdf
insertOrAppendPlacementNode @ react-dom_client.js?v=b703ff76:8773
commitPlacement @ react-dom_client.js?v=b703ff76:8804
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
commitReconciliationEffects @ react-dom_client.js?v=b703ff76:9662
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9503
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9551
flushMutationEffects @ react-dom_client.js?v=b703ff76:11098
commitRoot @ react-dom_client.js?v=b703ff76:11079
commitRootWhenReady @ react-dom_client.js?v=b703ff76:10512
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10457
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=b703ff76:11623
performWorkUntilDeadline @ react-dom_client.js?v=b703ff76:36
<iframe>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
renderPreview @ FilePreviewPane.tsx:216
FilePreviewPane @ FilePreviewPane.tsx:411
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=b703ff76:11623
performWorkUntilDeadline @ react-dom_client.js?v=b703ff76:36
<FilePreviewPane>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
FileOperationsHub @ FileOperationsHub.tsx:230
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
flushSyncWork$1 @ react-dom_client.js?v=b703ff76:10567
scheduleRefresh @ react-dom_client.js?v=b703ff76:372
(anonymous) @ @react-refresh:228
performReactRefresh @ @react-refresh:217
(anonymous) @ @react-refresh:586
<FileOperationsHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FilesPage.tsx:930
FilesPage @ FilesPage.tsx:955
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
FilePreviewPane.tsx?t=1753945600888:216 Not allowed to load local resource: file:///C:/Users/<USER>/Documents/Test18/personal-vault/omg--it-works/C:/Users/<USER>/Documents/Test18/personal-vault/omg--it-works/1753852868479_Dentsu_Data_Consciousness_Project_2022.pdf
insertOrAppendPlacementNode @ react-dom_client.js?v=b703ff76:8773
commitPlacement @ react-dom_client.js?v=b703ff76:8804
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
commitReconciliationEffects @ react-dom_client.js?v=b703ff76:9662
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9503
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9551
flushMutationEffects @ react-dom_client.js?v=b703ff76:11098
commitRoot @ react-dom_client.js?v=b703ff76:11079
commitRootWhenReady @ react-dom_client.js?v=b703ff76:10512
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10457
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=b703ff76:11623
performWorkUntilDeadline @ react-dom_client.js?v=b703ff76:36
<iframe>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
renderPreview @ FilePreviewPane.tsx?t=1753945600888:216
FilePreviewPane @ FilePreviewPane.tsx?t=1753945600888:411
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=b703ff76:11623
performWorkUntilDeadline @ react-dom_client.js?v=b703ff76:36
<FilePreviewPane>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
FileOperationsHub @ FileOperationsHub.tsx:230
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
flushSyncWork$1 @ react-dom_client.js?v=b703ff76:10567
scheduleRefresh @ react-dom_client.js?v=b703ff76:372
(anonymous) @ @react-refresh:228
performReactRefresh @ @react-refresh:217
(anonymous) @ @react-refresh:586
<FileOperationsHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FilesPage.tsx:930
FilesPage @ FilesPage.tsx:955
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
FilePreviewPane.tsx?t=1753945615036:219 Not allowed to load local resource: file:///C:/Users/<USER>/Documents/Test18/personal-vault/omg--it-works/C:/Users/<USER>/Documents/Test18/personal-vault/omg--it-works/1753852868479_Dentsu_Data_Consciousness_Project_2022.pdf
insertOrAppendPlacementNode @ react-dom_client.js?v=b703ff76:8773
commitPlacement @ react-dom_client.js?v=b703ff76:8804
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
commitReconciliationEffects @ react-dom_client.js?v=b703ff76:9662
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9503
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9551
flushMutationEffects @ react-dom_client.js?v=b703ff76:11098
commitRoot @ react-dom_client.js?v=b703ff76:11079
commitRootWhenReady @ react-dom_client.js?v=b703ff76:10512
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10457
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=b703ff76:11623
performWorkUntilDeadline @ react-dom_client.js?v=b703ff76:36
<iframe>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
renderPreview @ FilePreviewPane.tsx?t=1753945615036:219
FilePreviewPane @ FilePreviewPane.tsx?t=1753945615036:414
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=b703ff76:11623
performWorkUntilDeadline @ react-dom_client.js?v=b703ff76:36
<FilePreviewPane>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
FileOperationsHub @ FileOperationsHub.tsx:230
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
flushSyncWork$1 @ react-dom_client.js?v=b703ff76:10567
scheduleRefresh @ react-dom_client.js?v=b703ff76:372
(anonymous) @ @react-refresh:228
performReactRefresh @ @react-refresh:217
(anonymous) @ @react-refresh:586
<FileOperationsHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FilesPage.tsx:930
FilesPage @ FilesPage.tsx:955
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
@react-refresh:228 Not allowed to load local resource: file:///C:/Users/<USER>/Documents/Test18/personal-vault/omg--it-works/C:/Users/<USER>/Documents/Test18/personal-vault/omg--it-works/1753852868479_Dentsu_Data_Consciousness_Project_2022.pdf
setProp @ react-dom_client.js?v=b703ff76:12336
updateProperties @ react-dom_client.js?v=b703ff76:13180
commitUpdate @ react-dom_client.js?v=b703ff76:13956
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
commitHostUpdate @ react-dom_client.js?v=b703ff76:8730
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9513
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9502
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9655
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9374
recursivelyTraverseMutationEffects @ react-dom_client.js?v=b703ff76:9365
commitMutationEffectsOnFiber @ react-dom_client.js?v=b703ff76:9551
flushMutationEffects @ react-dom_client.js?v=b703ff76:11098
commitRoot @ react-dom_client.js?v=b703ff76:11079
commitRootWhenReady @ react-dom_client.js?v=b703ff76:10512
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10457
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
flushSyncWork$1 @ react-dom_client.js?v=b703ff76:10567
scheduleRefresh @ react-dom_client.js?v=b703ff76:372
(anonymous) @ @react-refresh:228
performReactRefresh @ @react-refresh:217
(anonymous) @ @react-refresh:586
<iframe>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
renderPreview @ FilePreviewPane.tsx?t=1753946019401:277
FilePreviewPane @ FilePreviewPane.tsx?t=1753946019401:656
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=b703ff76:11623
performWorkUntilDeadline @ react-dom_client.js?v=b703ff76:36
<FilePreviewPane>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
FileOperationsHub @ FileOperationsHub.tsx:230
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
flushSyncWork$1 @ react-dom_client.js?v=b703ff76:10567
scheduleRefresh @ react-dom_client.js?v=b703ff76:372
(anonymous) @ @react-refresh:228
performReactRefresh @ @react-refresh:217
(anonymous) @ @react-refresh:586
<FileOperationsHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FilesPage.tsx:930
FilesPage @ FilesPage.tsx:955
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
FileActionsPanel.tsx:306 Could not find icon undefined
log @ @fortawesome_react-fontawesome.js?v=b703ff76:4005
FontAwesomeIcon @ @fortawesome_react-fontawesome.js?v=b703ff76:4082
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateForwardRef @ react-dom_client.js?v=b703ff76:6461
beginWork @ react-dom_client.js?v=b703ff76:7864
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<FontAwesomeIcon>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FileActionsPanel.tsx:306
(anonymous) @ FileActionsPanel.tsx:290
FileActionsPanel @ FileActionsPanel.tsx:270
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<FileActionsPanel>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
FileOperationsHub @ FileOperationsHub.tsx:337
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<FileOperationsHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FilesPage.tsx:930
FilesPage @ FilesPage.tsx:955
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<FilesPage>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
App @ App.tsx:194
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ main.tsx:23
@react-refresh:228 Could not find icon undefined
log @ @fortawesome_react-fontawesome.js?v=b703ff76:4005
FontAwesomeIcon @ @fortawesome_react-fontawesome.js?v=b703ff76:4082
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateForwardRef @ react-dom_client.js?v=b703ff76:6461
beginWork @ react-dom_client.js?v=b703ff76:7864
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
flushSyncWork$1 @ react-dom_client.js?v=b703ff76:10567
scheduleRefresh @ react-dom_client.js?v=b703ff76:372
(anonymous) @ @react-refresh:228
performReactRefresh @ @react-refresh:217
(anonymous) @ @react-refresh:586
<FontAwesomeIcon>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FileActionsPanel.tsx:306
(anonymous) @ FileActionsPanel.tsx:290
FileActionsPanel @ FileActionsPanel.tsx:270
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<FileActionsPanel>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
FileOperationsHub @ FileOperationsHub.tsx:337
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
<FileOperationsHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FilesPage.tsx:930
FilesPage @ FilesPage.tsx:955
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
@react-refresh:228 Could not find icon undefined
log @ @fortawesome_react-fontawesome.js?v=b703ff76:4005
FontAwesomeIcon @ @fortawesome_react-fontawesome.js?v=b703ff76:4082
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateForwardRef @ react-dom_client.js?v=b703ff76:6461
beginWork @ react-dom_client.js?v=b703ff76:7864
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
flushSyncWork$1 @ react-dom_client.js?v=b703ff76:10567
scheduleRefresh @ react-dom_client.js?v=b703ff76:372
(anonymous) @ @react-refresh:228
performReactRefresh @ @react-refresh:217
(anonymous) @ @react-refresh:586
<FontAwesomeIcon>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FileActionsPanel.tsx:306
(anonymous) @ FileActionsPanel.tsx:290
FileActionsPanel @ FileActionsPanel.tsx:270
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
flushSyncWork$1 @ react-dom_client.js?v=b703ff76:10567
scheduleRefresh @ react-dom_client.js?v=b703ff76:372
(anonymous) @ @react-refresh:228
performReactRefresh @ @react-refresh:217
(anonymous) @ @react-refresh:586
<FileActionsPanel>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
FileOperationsHub @ FileOperationsHub.tsx?t=1753945528701:344
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
flushSyncWork$1 @ react-dom_client.js?v=b703ff76:10567
scheduleRefresh @ react-dom_client.js?v=b703ff76:372
(anonymous) @ @react-refresh:228
performReactRefresh @ @react-refresh:217
(anonymous) @ @react-refresh:586
<FileOperationsHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FilesPage.tsx:930
FilesPage @ FilesPage.tsx:955
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
@react-refresh:228 Could not find icon undefined
log @ @fortawesome_react-fontawesome.js?v=b703ff76:4005
FontAwesomeIcon @ @fortawesome_react-fontawesome.js?v=b703ff76:4082
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateForwardRef @ react-dom_client.js?v=b703ff76:6461
beginWork @ react-dom_client.js?v=b703ff76:7864
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
flushSyncWork$1 @ react-dom_client.js?v=b703ff76:10567
scheduleRefresh @ react-dom_client.js?v=b703ff76:372
(anonymous) @ @react-refresh:228
performReactRefresh @ @react-refresh:217
(anonymous) @ @react-refresh:586
<FontAwesomeIcon>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FileActionsPanel.tsx:306
(anonymous) @ FileActionsPanel.tsx:290
FileActionsPanel @ FileActionsPanel.tsx:270
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
flushSyncWork$1 @ react-dom_client.js?v=b703ff76:10567
scheduleRefresh @ react-dom_client.js?v=b703ff76:372
(anonymous) @ @react-refresh:228
performReactRefresh @ @react-refresh:217
(anonymous) @ @react-refresh:586
<FileActionsPanel>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
FileOperationsHub @ FileOperationsHub.tsx:238
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
flushSyncWork$1 @ react-dom_client.js?v=b703ff76:10567
scheduleRefresh @ react-dom_client.js?v=b703ff76:372
(anonymous) @ @react-refresh:228
performReactRefresh @ @react-refresh:217
(anonymous) @ @react-refresh:586
<FileOperationsHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=b703ff76:250
(anonymous) @ FilesPage.tsx:930
FilesPage @ FilesPage.tsx:955
react-stack-bottom-frame @ react-dom_client.js?v=b703ff76:17424
renderWithHooks @ react-dom_client.js?v=b703ff76:4206
updateFunctionComponent @ react-dom_client.js?v=b703ff76:6619
beginWork @ react-dom_client.js?v=b703ff76:7654
runWithFiberInDEV @ react-dom_client.js?v=b703ff76:1485
performUnitOfWork @ react-dom_client.js?v=b703ff76:10868
workLoopSync @ react-dom_client.js?v=b703ff76:10728
renderRootSync @ react-dom_client.js?v=b703ff76:10711
performWorkOnRoot @ react-dom_client.js?v=b703ff76:10330
performSyncWorkOnRoot @ react-dom_client.js?v=b703ff76:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=b703ff76:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=b703ff76:11558
(anonymous) @ react-dom_client.js?v=b703ff76:11649
client:815  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753945565256 net::ERR_ABORTED 500 (Internal Server Error)
importUpdatedModule @ client:815
fetchUpdate @ client:210
queueUpdate @ client:189
(anonymous) @ client:838
handleMessage @ client:837
await in handleMessage
(anonymous) @ client:459
dequeue @ client:481
(anonymous) @ client:473
enqueue @ client:467
(anonymous) @ client:459
onMessage @ client:306
(anonymous) @ client:414
client:809 [vite] Failed to reload /src/components/FileOperationsHub.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above)
error @ client:809
warnFailedUpdate @ client:181
fetchUpdate @ client:212
await in fetchUpdate
queueUpdate @ client:189
(anonymous) @ client:838
handleMessage @ client:837
await in handleMessage
(anonymous) @ client:459
dequeue @ client:481
(anonymous) @ client:473
enqueue @ client:467
(anonymous) @ client:459
onMessage @ client:306
(anonymous) @ client:414
client:815  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753945587725 net::ERR_ABORTED 500 (Internal Server Error)
importUpdatedModule @ client:815
fetchUpdate @ client:210
queueUpdate @ client:189
(anonymous) @ client:838
handleMessage @ client:837
await in handleMessage
(anonymous) @ client:459
dequeue @ client:481
(anonymous) @ client:473
enqueue @ client:467
(anonymous) @ client:459
onMessage @ client:306
(anonymous) @ client:414
client:809 [vite] Failed to reload /src/components/FileOperationsHub.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above)
error @ client:809
warnFailedUpdate @ client:181
fetchUpdate @ client:212
await in fetchUpdate
queueUpdate @ client:189
(anonymous) @ client:838
handleMessage @ client:837
await in handleMessage
(anonymous) @ client:459
dequeue @ client:481
(anonymous) @ client:473
enqueue @ client:467
(anonymous) @ client:459
onMessage @ client:306
(anonymous) @ client:414
FilesPage.tsx:28  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753945784797 net::ERR_ABORTED 500 (Internal Server Error)
client:809 [vite] Failed to reload /src/pages/FilesPage.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above)
error @ client:809
warnFailedUpdate @ client:181
fetchUpdate @ client:212
await in fetchUpdate
queueUpdate @ client:189
(anonymous) @ client:838
handleMessage @ client:837
await in handleMessage
(anonymous) @ client:459
dequeue @ client:481
(anonymous) @ client:473
enqueue @ client:467
(anonymous) @ client:459
onMessage @ client:306
(anonymous) @ client:414
client:809 [vite] Failed to reload /src/pages/FilesPage.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above)
error @ client:809
warnFailedUpdate @ client:181
fetchUpdate @ client:212
await in fetchUpdate
queueUpdate @ client:189
(anonymous) @ client:838
handleMessage @ client:837
await in handleMessage
(anonymous) @ client:459
dequeue @ client:481
(anonymous) @ client:473
enqueue @ client:467
(anonymous) @ client:459
onMessage @ client:306
(anonymous) @ client:414
FilesPage.tsx:28  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753946019401 net::ERR_ABORTED 500 (Internal Server Error)
FilesPage.tsx:28  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753946034871 net::ERR_ABORTED 500 (Internal Server Error)
FilesPage.tsx:28  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753946034871 net::ERR_ABORTED 500 (Internal Server Error)
FilesPage.tsx:28  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753946049976 net::ERR_ABORTED 500 (Internal Server Error)
FilesPage.tsx:28  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753946049976 net::ERR_ABORTED 500 (Internal Server Error)
client:809 [vite] Failed to reload /src/pages/FilesPage.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above)
error @ client:809
warnFailedUpdate @ client:181
fetchUpdate @ client:212
await in fetchUpdate
queueUpdate @ client:189
(anonymous) @ client:838
handleMessage @ client:837
await in handleMessage
(anonymous) @ client:459
dequeue @ client:481
(anonymous) @ client:473
enqueue @ client:467
(anonymous) @ client:459
onMessage @ client:306
(anonymous) @ client:414
FilesPage.tsx:28  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753946049976 net::ERR_ABORTED 500 (Internal Server Error)
client:815  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753946113430 net::ERR_ABORTED 500 (Internal Server Error)
importUpdatedModule @ client:815
fetchUpdate @ client:210
queueUpdate @ client:189
(anonymous) @ client:838
handleMessage @ client:837
await in handleMessage
(anonymous) @ client:459
dequeue @ client:481
(anonymous) @ client:473
enqueue @ client:467
(anonymous) @ client:459
onMessage @ client:306
(anonymous) @ client:414
client:809 [vite] Failed to reload /src/components/FileOperationsHub.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above)
error @ client:809
warnFailedUpdate @ client:181
fetchUpdate @ client:212
await in fetchUpdate
queueUpdate @ client:189
(anonymous) @ client:838
handleMessage @ client:837
await in handleMessage
(anonymous) @ client:459
dequeue @ client:481
(anonymous) @ client:473
enqueue @ client:467
(anonymous) @ client:459
onMessage @ client:306
(anonymous) @ client:414
FilesPage.tsx:28  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753946113430 net::ERR_ABORTED 500 (Internal Server Error)
client:815  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753946142164 net::ERR_ABORTED 500 (Internal Server Error)
importUpdatedModule @ client:815
fetchUpdate @ client:210
queueUpdate @ client:189
(anonymous) @ client:838
handleMessage @ client:837
await in handleMessage
(anonymous) @ client:459
dequeue @ client:481
(anonymous) @ client:473
enqueue @ client:467
(anonymous) @ client:459
onMessage @ client:306
(anonymous) @ client:414
client:809 [vite] Failed to reload /src/components/FileOperationsHub.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above)
error @ client:809
warnFailedUpdate @ client:181
fetchUpdate @ client:212
await in fetchUpdate
queueUpdate @ client:189
(anonymous) @ client:838
handleMessage @ client:837
await in handleMessage
(anonymous) @ client:459
dequeue @ client:481
(anonymous) @ client:473
enqueue @ client:467
(anonymous) @ client:459
onMessage @ client:306
(anonymous) @ client:414
FilesPage.tsx:28  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753946142164 net::ERR_ABORTED 500 (Internal Server Error)
client:815  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753946161729 net::ERR_ABORTED 500 (Internal Server Error)
importUpdatedModule @ client:815
fetchUpdate @ client:210
queueUpdate @ client:189
(anonymous) @ client:838
handleMessage @ client:837
await in handleMessage
(anonymous) @ client:459
dequeue @ client:481
(anonymous) @ client:473
enqueue @ client:467
(anonymous) @ client:459
onMessage @ client:306
(anonymous) @ client:414
client:809 [vite] Failed to reload /src/components/FileOperationsHub.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above)
error @ client:809
warnFailedUpdate @ client:181
fetchUpdate @ client:212
await in fetchUpdate
queueUpdate @ client:189
(anonymous) @ client:838
handleMessage @ client:837
await in handleMessage
(anonymous) @ client:459
dequeue @ client:481
(anonymous) @ client:473
enqueue @ client:467
(anonymous) @ client:459
onMessage @ client:306
(anonymous) @ client:414
FilesPage.tsx:28  GET http://localhost:5173/src/components/FileOperationsHub.tsx?t=1753946161729 net::ERR_ABORTED 500 (Internal Server Error)
