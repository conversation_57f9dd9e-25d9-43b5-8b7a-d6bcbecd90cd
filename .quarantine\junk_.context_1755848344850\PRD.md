# Product Requirements Document (PRD)
## Smart Context Vault (Chatlo)

---

### 1. Executive Summary
**Product Name:** Chatlo  
**Version:** 0.1  
**Target Release:** Q3 2025

Chatlo is a desktop-based smart context vault and AI assistant. It empowers users to capture, organize, and act on all relevant digital context—files, notes, artifacts, and events—entirely locally. The system features a proactive, always-on background assistant (Rust-based, planned) and a rich, modern UI (Electron/React), leveraging a shared SQLite database for seamless, privacy-first context management. Chatlo is designed for extensibility, privacy, and deep user control, moving beyond traditional chat apps to become the user's trusted digital companion.

---

### 2. Product Vision & Goals
**Vision:** Become the most user-centric, local-first context vault and AI assistant, maximizing productivity and privacy by surfacing the right context at the right time, with or without cloud dependency.

**Primary Goals:**
- Provide a seamless, unified view of user-selected context (files, notes, artifacts, events)
- Enable proactive, privacy-respecting notifications and quick actions
- Support extensible workflows and automations
- Deliver a beautiful, intuitive, and non-intrusive user interface
- Leverage and extend the existing codebase and SQLite DB

---

### 3. Target Audience & Personas
**Primary Users:**
- Knowledge workers and technical professionals
- Privacy-conscious users
- Power users and productivity enthusiasts
- Anyone seeking to organize and act on digital context efficiently

**User Personas:**
- **Project Manager:** Needs to track files, notes, and events across projects
- **Privacy Advocate:** Wants local, secure context management
- **Power User:** Seeks advanced context development, automation, and custom workflows

---

### 4. System Development Progress
This section summarizes the features and modules already implemented in the Chatlo codebase as of January 2025.

#### 4.1 Core Architecture
- Electron + React 19 + TypeScript: Modern desktop app with modular, component-based UI
- SQLite Database (better-sqlite3): Persistent local storage with migration system, WAL mode, and relational schema for conversations, messages, files, and artifacts
- Zustand State Management: Centralized, async-friendly state for UI and business logic

#### 4.2 Context, Chat & LLM Integration
- OpenRouter API Integration: Access to 400+ AI models, including GPT-4, Claude, Gemini, and more
- Streaming Support: Real-time message streaming from LLMs
- Model Selection & Filtering: Enhanced dropdown, model categories (flagship, free, vision, reasoning, code, search)
- System Prompts & Advanced Parameters: Customizable prompts, temperature/top-p/top-k controls

#### 4.3 Artifact & File Handling
- Artifacts System: Sidebar for code, markdown, images, mermaid diagrams, and web links
- Artifact Detection & Management: Automatic extraction, versioning, and organization
- File System Manager: Indexing, metadata extraction, and processing for PDF, Word, Excel, PowerPoint, images, and text files
- File Attachment UI: Drag-and-drop, autocomplete, and preview
- OCR & Image Analysis: Local text extraction and metadata analysis

#### 4.4 Data Management
- Persistent Conversation Storage: All chats and artifacts stored locally
- Backup & Restore: Conversation and settings backup/restore
- Data Encryption: API keys and sensitive data encrypted at rest
- Import/Export: Support for data migration and sharing

#### 4.5 UI/UX & Notifications
- Modern Chat UI: Responsive, dark theme, glassmorphism, and accessibility features
- Sidebar & Settings: Conversation management, settings modal, and advanced configuration
- Toast Notifications: Enhanced feedback for model updates, errors, and system events
- Pinned Conversations & Messages: Visual indicators and quick access

#### 4.6 Advanced Features
- Artifacts Sidebar: Resizable, fullscreen, and multi-type support
- Model Update System: OTA updates, manifest management, and user feedback
- Vision Model Support: Multi-modal AI for image analysis
- File Vectorization Workflow: File content processing for AI context

#### 4.7 In Progress & Planned
- Context-aware files and chat management homepage
- A Rust-based tray app for in-time assistant and deeper integration with OS
- Plugin Architecture: Foundation for extensibility
- MCP integration for AI-tools extensibility with Model Context Protocol
- Testing & CI: Automated testing pipeline in progress
- Accessibility & Internationalization: WCAG compliance and multi-language support underway
- Performance Optimization: Build size reduction, lazy loading, and virtual scrolling


### 5. System Architecture Overview
- **Two-App Model:**
  - **Electron UI App:** Rich, interactive context explorer, artifact viewer, chat interface, notifications, and settings
  - **Rust Background App (Planned):** Proactive, always-on system integration, file/event monitoring, privacy alerts, and quick actions (bubble UI)
- **Shared SQLite Database:** Central source of truth for all context, artifacts, and user data
- **Communication:** Electron and Rust communicate via shared DB and (future) IPC or protocol for real-time events
- **Extensibility:** Plugin/MCP system planned for future automation and workflow expansion


### 6. Core Features & Requirements
#### 6.1 Context Vault
- Unified management of files, notes, artifacts, and events
- Tagging, annotation, linking, and versioning
- Fast search and filtering
- Privacy controls and audit log

#### 6.2 Electron UI App
- Modern, responsive UI for browsing, searching, and managing context
- Artifact/code diff viewers, context details, and preview panels
- Notifications and alerts area
- Quick actions bar (add note, import file, take snapshot)
- Settings and privacy dashboard
- Status indicator (connection to backend, DB version)

#### 6.3 Rust Background App (Planned)
- System tray/bubble UI for quick actions and notifications
- File system and event monitoring
- Proactive context surfacing and privacy alerts
- Launches Electron UI for deep context exploration

#### 6.4 AI Model Integration
- OpenRouter and local LLM support
- Model switching, privacy-aware file handling
- Usage tracking and error handling

#### 6.5 Data Management
- SQLite schema, backup/restore, import/export, encryption
- Persistent, local-first storage

---

### 7. Technical Stack & Integration
- **Electron UI App:** Electron, React 19, TypeScript, Tailwind CSS, Zustand, Monaco Editor, Markdown-it
- **Backend:** Node.js, SQLite (better-sqlite3), OpenRouter SDK, MCP TypeScript SDK
- **Planned Rust App:** Rust, Tauri/winit (for bubble UI), rusqlite, IPC/command protocol
- **Extensibility:** Plugin/MCP system (planned)

---

### 8. User Experience & UI Principles
- Non-intrusive, actionable, and accessible design
- Homepage: Recent context, quick actions, notifications
- Context panels: Artifact, file, and note management
- Responsive, dark theme, and keyboard navigation

---

### 9. Security & Privacy
- Local data storage by default
- API key and sensitive data encryption
- No telemetry without consent
- User approval for sensitive actions
- Open source codebase

---

### 10. Platform Support & Deployment
- **Phase 1:** Windows, macOS, Linux (Electron)
- **Phase 2:** Web version (optional)
- **Phase 3:** Mobile apps (React Native, future)

---

### 11. Success Metrics
- Daily active users
- Context items added per session
- Feature adoption rates (artifacts, linking, notes)
- User satisfaction score > 4.5/5
- Application crash rate < 0.1%

---

### 12. Development Phases & Roadmap
- **Phase 1:** Core foundation (Electron UI, context vault, OpenRouter integration, artifact system)
- **Phase 2:** Advanced features (plugin/MCP, Rust background app, automations, privacy dashboard)
- **Phase 3:** Polish & extensions (performance, accessibility, internationalization, voice, collaboration)

---

### 13. Risks & Mitigation
- Cross-platform compatibility (Electron, Rust, SQLite)
- SQLite schema evolution and migration
- IPC/launch reliability between Rust and Electron
- User onboarding and discoverability
- **Mitigation:** Modular schema, migration scripts, robust error handling, onboarding flows

---

### 14. Future Enhancements
- Voice input and ambient commands
- Advanced workflow automations
- Plugin/extension ecosystem
- Team collaboration (local network)
- Integration with external tools (APIs, scripts)

---

**Document Version:** 2.0  
**Last Updated:** 2025-01-27  
**Next Review:** 2025-02-27
