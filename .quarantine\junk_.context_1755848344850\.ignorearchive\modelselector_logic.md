# ModelSelector Logic Documentation

## Overview
The ModelSelector component is part of a comprehensive system that handles model selection and filtering based on Private Mode state and various model categories. This document outlines the complete architecture including IPC communication, state management, database operations, and the full data flow for model availability and filtering.

## System Architecture

### Component Hierarchy
```
Main Process (Electron)
├── DatabaseManager - Handles all database operations
├── IPC Handlers - Bridge between main and renderer processes
└── FileSystem - Manages file operations

Renderer Process (React)
├── ModelSelector Component - UI for model selection
├── State Management (Zustand)
│   ├── useAppStore - Global app state, models, settings
│   └── useNetworkStore - Network state, private mode, local models
└── Services
    ├── localModelService - Ollama/LM Studio communication
    └── openRouterService - External model API communication
```

## IPC (Inter-Process Communication) Architecture

### IPC Channel Structure
The application uses Electron's IPC system to communicate between the main process and renderer process:

**Main Process IPC Handlers (`electron/main.ts`):**
- `db:*` - Database operations (conversations, messages, settings)
- `settings:get/set` - Settings management
- `files:*` - File system operations

**Preload Script (`electron/preload.ts`):**
- Exposes `window.electronAPI` with typed interfaces
- Provides secure bridge between renderer and main process
- Validates all IPC calls for security

### Model Selection IPC Flow
```
1. Renderer: ModelSelector component requests settings
   → window.electronAPI.settings.get('selectedModel')

2. Main Process: Retrieves from database
   → DatabaseManager.getSetting('selectedModel')

3. Main Process: Returns model ID to renderer
   → IPC response with model data

4. Renderer: Updates local state and UI
   → useAppStore.updateSettings({ selectedModel })
```

### Settings Persistence IPC
```
1. User selects model in ModelSelector
   → onModelSelect(modelId) called

2. Component updates local state
   → useAppStore.updateSettings({ selectedModel: modelId })

3. Settings saved via IPC
   → window.electronAPI.settings.set('selectedModel', modelId)

4. Main process persists to database
   → DatabaseManager.setSetting('selectedModel', modelId)
```

## State Management Architecture

### Zustand Store Structure

#### useAppStore (`src/store/index.ts`)
**Primary application state container:**
```typescript
interface AppState {
  // Model data
  models: OpenRouterModel[]           // External models from OpenRouter
  settings: Settings                  // User preferences including selectedModel

  // UI state
  conversations: Conversation[]
  currentConversationId: string | null
  messages: Message[]
  isLoading: boolean
  streamingMessage: string | null

  // Actions
  setModels: (models: OpenRouterModel[]) => void
  updateSettings: (settings: Partial<Settings>) => void
  sendMessage: (content: string, files?: FileRecord[]) => Promise<void>
}
```

**Key Model-Related Settings:**
- `selectedModel: string` - Currently selected model ID
- `favoriteModels: string[]` - User's bookmarked models
- `modelFilter: string` - Current category filter
- `temperature, maxTokens, topP, topK` - Model parameters

#### useNetworkStore (`src/stores/networkStore.ts`)
**Network and local model state:**
```typescript
interface NetworkState {
  // Network state
  isOnline: boolean                   // Online/offline status
  isPrivateMode: boolean             // Private mode toggle

  // Local model detection
  localModelsAvailable: boolean      // Any local models available
  ollamaConnected: boolean          // Ollama service status
  lmStudioConnected: boolean        // LM Studio service status
  localModels: LocalModel[]         // Available local models

  // Actions
  togglePrivateMode: () => void
  checkLocalModels: () => Promise<void>
  setLocalModels: (models: LocalModel[]) => void
}
```

**State Persistence:**
- `useNetworkStore` uses Zustand persist middleware
- Automatically saves/restores private mode state
- Local model detection runs on app startup

### State Synchronization Flow
```
1. App Initialization:
   useAppStore.loadSettings()
   → IPC: settings.get('app-settings')
   → DatabaseManager.getSetting('app-settings')
   → Updates store with persisted settings

2. Private Mode Toggle:
   useNetworkStore.togglePrivateMode()
   → Updates isPrivateMode state
   → Triggers checkLocalModels() if enabled
   → ModelSelector re-renders with filtered models

3. Model Selection:
   ModelSelector.onModelSelect(modelId)
   → useAppStore.updateSettings({ selectedModel: modelId })
   → IPC: settings.set('selectedModel', modelId)
   → DatabaseManager.setSetting('selectedModel', modelId)
```

## Database Management

### Database Schema (`electron/database.ts`)

#### Settings Table
```sql
CREATE TABLE settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL  -- JSON serialized values
);
```

**Model-Related Settings Stored:**
- `selectedModel` - Current model ID (e.g., "openai/gpt-4", "ollama:llama2")
- `app-settings` - Complete settings object including:
  - `favoriteModels: string[]`
  - `modelFilter: string`
  - `temperature: number`
  - `maxTokens: number`
  - `topP, topK, frequencyPenalty, presencePenalty`

#### Messages Table
```sql
CREATE TABLE messages (
  id TEXT PRIMARY KEY,
  conversation_id TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
  content TEXT NOT NULL,
  model TEXT,  -- Stores which model generated the response
  is_pinned INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
);
```

**Model Tracking:**
- Each message stores the `model` field indicating which model generated it
- Enables conversation history to show model usage
- Supports model performance analysis

### Database Operations for Model Selection

#### Settings Management
```typescript
// Get current model selection
const selectedModel = await window.electronAPI.settings.get('selectedModel')

// Save model selection
await window.electronAPI.settings.set('selectedModel', 'openai/gpt-4')

// Get complete settings
const settings = await window.electronAPI.settings.get('app-settings')
```

#### Message Model Tracking
```typescript
// Save message with model information
const message = {
  conversation_id: 'conv-123',
  role: 'assistant',
  content: 'Response text',
  model: 'openai/gpt-4'  // Track which model generated this
}
await window.electronAPI.db.addMessage('conv-123', message)
```

### Database Persistence Strategy
- **Settings**: JSON serialized in settings table
- **Model History**: Tracked per message for analytics
- **Favorites**: Stored as array in app-settings
- **Backup**: Automatic database backups on corruption
- **Migration**: Version-controlled schema updates

## Private Mode Logic

### Private Mode ON (isPrivateMode = true)
**Available Models:**
- **ONLY local models** from Ollama and LM Studio are shown
- External/cloud models are completely filtered out
- Local models are transformed into OpenRouterModel format with:
  - ID format: `provider:modelname` (e.g., `ollama:llama2`, `lmstudio:codellama`)
  - Pricing set to "0" (free)
  - Default context_length: 4096
  - Description: `Local ${provider} model`

**Model Source:**
- `localModels` array from `useNetworkStore()`
- Models are fetched from:
  - Ollama API: `http://localhost:11434/api/tags`
  - LM Studio API: `http://localhost:1234/v1/models`

**UI Indicators:**
- Private Mode banner shows in dropdown header
- Status text: "Offline"
- Shows count: `Showing X local model(s)`
- If no local models available: "No local models available. Install Ollama or LM Studio."

### Private Mode OFF (isPrivateMode = false)
**Available Models:**
- **ALL models** including external/cloud models from OpenRouter
- Local models are also included if available
- Full model catalog with pricing, context lengths, and capabilities

**Model Source:**
- `models` array from `useAppStore()` (OpenRouter models)
- Local models may also be included

**UI Indicators:**
- No Private Mode banner
- Status text: "Online"
- Full model selection available

## Model Filtering Logic

### 1. Base Model Set Selection
```
// Transform local models to OpenRouterModel format
transformedLocalModels = localModels.map(localModel => ({ ... }))

if (isPrivateMode) {
  availableModels = transformedLocalModels (local models only)
} else {
  availableModels = [...models, ...transformedLocalModels] (both external and local)
}
```

### 2. Category Filtering
Models are filtered by selected category using `modelCategories` filters:

- **All Models** (`all`): No filtering - shows all available models
- **Free Models** (`free`): Models with pricing.prompt = "0" OR pricing.completion = "0"
- **Flagship** (`flagship`): Top-tier models (GPT-4, Claude-3.5-Sonnet, Gemini-1.5-Pro, etc.)
- **Reasoning** (`reasoning`): Models with "o1-", "reasoning", "think", "deepseek-r1" in name/id
- **Code** (`code`): Models with "code", "codestral", "deepseek-coder", "starcoder" in name/id
- **Vision** (`vision`): Models with "vision", "gpt-4o", "claude-3", "gemini" in name/id
- **Local Models** (`local`): Models running locally via Ollama or LM Studio (ID starts with "ollama:" or "lmstudio:")

### 3. Search Filtering
If search query is provided, models are filtered by:
- Model name (case-insensitive)
- Model ID (case-insensitive)
- Model description (case-insensitive)
- Provider name (case-insensitive)

### 4. Sorting Logic
Models are sorted with priority:
1. **Flagship models first** (isFlagship = true)
2. Then by selected sort criteria:
   - `name`: Alphabetical by model name
   - `provider`: Alphabetical by provider name
   - `price`: By total cost (prompt + completion pricing)
   - `context`: By context length (descending)

## Model Enhancement Logic

Each model is enhanced with additional properties via `enhanceModelInfo()`:

### Model Classification
- **isFree**: Pricing is "0" or "0.0" for prompt/completion
- **isFlagship**: Contains "gpt-4", "claude-3.5-sonnet", "claude-3-opus", "gemini-1.5-pro", "gemini-2.0-flash"
- **isReasoning**: Contains "o1-", "reasoning", "think", "deepseek-r1"
- **isCode**: Contains "code", "codestral", "deepseek-coder", "starcoder"
- **isVision**: Contains "vision", "gpt-4o", "claude-3", "gemini"

### Provider Extraction
- Provider name extracted from model ID (before first "/")
- Used for grouping and display purposes

## Recommended Models Logic

When no search query and "All Models" category is selected:
- Shows top 4 recommended models
- Prioritizes: Free + Flagship > Flagship > Free > Others
- Sorted alphabetically within each priority group

## Local Model Detection

### Connection Status
- **Ollama**: Checks `http://localhost:11434/api/tags` with 5s timeout
- **LM Studio**: Checks `http://localhost:1234/v1/models` with 5s timeout
- `localModelsAvailable = ollamaConnected || lmStudioConnected`

### Model ID Format
- **Ollama models**: `ollama:modelname`
- **LM Studio models**: `lmstudio:modelname`

### Periodic Checks
- Local models are checked every 30 seconds when Private Mode is enabled
- Connection status updates automatically affect model availability

## Error Handling

### No Local Models in Private Mode
- Shows message: "No local models available. Install Ollama or LM Studio."
- Provides setup guidance
- Prevents chat functionality until local models are available

### Connection Failures
- Graceful fallback when local services are unavailable
- Does not block app functionality
- Shows appropriate status indicators

## UI States Summary

| Private Mode | Local Models Available | Models Shown | Status Display |
|--------------|----------------------|--------------|----------------|
| OFF | Any | All external + local | "Online" |
| ON | Yes | Local only | "Offline" + count |
| ON | No | None | "Offline" + setup message |

## Filter Interaction in Private Mode

When Private Mode is ON:
- **All categories work** but only apply to local models
- **Free filter**: All local models (pricing = "0")
- **Flagship/Reasoning/Code/Vision**: Based on model name patterns
- **Search**: Works across local model names/IDs
- **Recommended**: Shows top local models if any qualify

This ensures consistent filtering behavior regardless of Private Mode state, but with different base model sets.

## Local Model Service Integration

### Service Architecture (`src/services/localModelService.ts`)

#### Provider Detection
```typescript
class LocalModelService {
  private ollamaBaseUrl = 'http://localhost:11434'
  private lmStudioBaseUrl = 'http://localhost:1234'

  // Check Ollama availability
  async checkOllama(): Promise<{ connected: boolean; models: LocalModel[] }>

  // Check LM Studio availability
  async checkLMStudio(): Promise<{ connected: boolean; models: LocalModel[] }>

  // Get all local models from both providers
  async getAllLocalModels(): Promise<LocalModel[]>
}
```

#### Model ID Format
- **Ollama**: `ollama:modelname` (e.g., `ollama:llama2`)
- **LM Studio**: `lmstudio:modelname` (e.g., `lmstudio:codellama`)

#### Connection Monitoring
```typescript
// Periodic checks in NetworkStatus component
useEffect(() => {
  if (isPrivateMode) {
    checkLocalModels()
    const interval = setInterval(checkLocalModels, 30000) // Every 30s
    return () => clearInterval(interval)
  }
}, [isPrivateMode, checkLocalModels])
```

### Local Model State Flow
```
1. Private Mode Enabled:
   → useNetworkStore.togglePrivateMode()
   → checkLocalModels() called
   → localModelService.getAllLocalModels()
   → Updates localModels array in store

2. ModelSelector Rendering:
   → Reads localModels from useNetworkStore
   → Transforms to OpenRouterModel format
   → Filters and displays local models only

3. Model Selection:
   → User selects local model (e.g., "ollama:llama2")
   → Saved to database via IPC
   → Used for chat completion via localModelService
```

## Chat Integration and Model Usage

### Model Selection in Chat Flow

#### Message Processing (`src/store/index.ts`)
```typescript
const shouldUseLocalModel = (selectedModel?: string): boolean => {
  const networkState = useNetworkStore.getState()

  // Check if selected model is local (contains ":")
  if (selectedModel && selectedModel.includes(':')) {
    // Local models have format "provider:modelname"
    return true
  }

  // If private mode is on and no specific model selected, use local models
  if (networkState.isPrivateMode) {
    return true
  }

  // If private mode is off and no local model selected, use external models
  return false
}
```

#### Service Routing
```typescript
// In sendMessage action
if (useLocalModel) {
  // Route to local model service
  response = await localModelService.sendMessage(
    settings.selectedModel!,
    conversationMessages
  )
} else {
  // Route to OpenRouter service
  response = await openRouterService.createChatCompletion({
    model: settings.selectedModel || 'openai/gpt-3.5-turbo',
    messages: conversationMessages,
    // ... other parameters
  })
}
```

### Error Handling and Fallbacks

#### Local Model Unavailable
```typescript
// Check availability before sending
if (useLocalModel && !areLocalModelsAvailable()) {
  const errorMessage = {
    role: 'assistant',
    content: 'Private mode is enabled but no local models are available. Please install Ollama or LM Studio and download models to continue chatting privately.',
    model: 'system'
  }
  // Save error message to database
  await window.electronAPI.db.addMessage(conversationId, errorMessage)
}
```

#### Connection Failures
- Graceful degradation when local services are unavailable
- Non-blocking app functionality
- Status indicators update automatically
- User guidance for setup requirements

## Model Enhancement and Categorization

### Model Information Enhancement (`src/utils/modelUtils.ts`)

#### Classification Logic
```typescript
export const enhanceModelInfo = (model: OpenRouterModel): EnhancedModelInfo => {
  const provider = model.id.split('/')[0] || 'unknown'
  const modelName = model.name.toLowerCase()
  const modelId = model.id.toLowerCase()

  return {
    ...model,
    isFree: model.pricing.prompt === "0" || model.pricing.completion === "0",
    isFlagship: modelId.includes('gpt-4') || modelId.includes('claude-3.5-sonnet'),
    isReasoning: modelId.includes('o1-') || modelId.includes('reasoning'),
    isCode: modelId.includes('code') || modelId.includes('codestral'),
    isVision: modelId.includes('vision') || modelId.includes('gpt-4o'),
    provider,
    maxTokensSupported: model.top_provider?.max_completion_tokens || Math.min(model.context_length * 0.75, 100000)
  }
}
```

#### Category Filtering
- **All Models**: No filtering
- **Free Models**: `pricing.prompt = "0" OR pricing.completion = "0"`
- **Flagship**: Top-tier models from major providers
- **Reasoning**: Models optimized for complex reasoning
- **Code**: Programming-focused models
- **Vision**: Multi-modal models with image support

### Local Model Enhancement
```typescript
// Transform local models to OpenRouterModel format
const availableModels = useMemo(() => {
  // Transform local models to OpenRouterModel format
  const transformedLocalModels = localModels.map(localModel => ({
    id: localModel.id,                    // "ollama:llama2"
    name: localModel.name,                // "llama2"
    description: `Local ${localModel.provider} model`,
    pricing: { prompt: "0", completion: "0" },
    context_length: 4096,                 // Default context
    architecture: { modality: "text", tokenizer: "unknown", instruct_type: null },
    top_provider: { context_length: 4096, max_completion_tokens: null },
    per_request_limits: null
  })) as OpenRouterModel[]

  if (isPrivateMode) {
    // In private mode, only show local models
    return transformedLocalModels
  }

  // In non-private mode, show both external and local models
  return [...models, ...transformedLocalModels]
}, [isPrivateMode, localModels, models])
```

## Complete Data Flow Summary

### Initialization Flow
```
1. App Startup:
   → DatabaseManager initializes SQLite database
   → Settings loaded from database via IPC
   → useAppStore populated with settings
   → useNetworkStore checks private mode state
   → If private mode: checkLocalModels() runs

2. Model Loading:
   → External models loaded from OpenRouter API
   → Local models detected from Ollama/LM Studio
   → Both stored in respective Zustand stores
   → ModelSelector receives combined/filtered model list
```

### User Interaction Flow
```
1. User Opens ModelSelector:
   → Component reads from useAppStore.models and useNetworkStore.localModels
   → Applies private mode filtering
   → Renders available models with categories

2. User Selects Model:
   → onModelSelect(modelId) called
   → useAppStore.updateSettings({ selectedModel: modelId })
   → IPC call: settings.set('selectedModel', modelId)
   → DatabaseManager.setSetting() persists to SQLite
   → UI updates to show selected model

3. User Sends Message:
   → shouldUseLocalModel() determines routing
   → Message sent via appropriate service
   → Response saved with model tracking
   → Database stores which model generated response
```

### Private Mode Toggle Flow
```
1. User Toggles Private Mode:
   → useNetworkStore.togglePrivateMode()
   → State persisted via Zustand middleware
   → checkLocalModels() triggered if enabled
   → ModelSelector re-renders with filtered models

2. Local Model Detection:
   → localModelService.getAllLocalModels()
   → Checks Ollama (localhost:11434) and LM Studio (localhost:1234)
   → Updates useNetworkStore.localModels
   → Periodic checks every 30 seconds when private mode active
```

## Filter Selection Flow Chart

```mermaid
flowchart TD
    A[User Clicks Filter Label] --> B{Filter Type?}
    
    B -->|Flagship| C[toggleFilter('flagship')]
    B -->|Free| D[toggleFilter('free')]
    B -->|Local| E[toggleFilter('local')]
    B -->|Reasoning| F[toggleFilter('reasoning')]
    B -->|Vision| G[toggleFilter('vision')]
    B -->|Favorites| H[toggleFilter('favorites')]
    
    C --> I[Update activeFilters State]
    D --> I
    E --> I
    F --> I
    G --> I
    H --> I
    
    I --> J[filteredModels useMemo Triggered]
    
    J --> K[Apply Search Filter First]
    K --> L{activeFilters.length > 0?}
    
    L -->|Yes| M[Filter models by active filters]
    L -->|No| N[Return all availableModels]
    
    M --> O{Check Each Filter}
    O -->|flagship| P[enhanced.isFlagship]
    O -->|free| Q[enhanced.isFree]
    O -->|reasoning| R[enhanced.isReasoning]
    O -->|vision| S[enhanced.isVision]
    O -->|local| T[isPrivateMode check]
    O -->|favorites| U[localSettings.favoriteModels.includes(model.id)]
    
    P --> V[Model passes filter?]
    Q --> V
    R --> V
    S --> V
    T --> V
    U --> V
    
    V -->|Yes| W[Include in filtered results]
    V -->|No| X[Exclude from results]
    
    W --> Y[sortModels by name]
    X --> Y
    N --> Y
    
    Y --> Z[paginatedModels useMemo Triggered]
    Z --> AA[Slice results for pagination]
    AA --> BB[UI Re-renders with filtered models]
    
    BB --> CC[Model cards show/hide based on filter]
    CC --> DD[Filter button visual state updates]
```

### Key Moving Parts in Filter Selection:

1. **State Updates:**
   - `activeFilters` array in ChatSettingsDrawer component
   - Visual state of filter buttons (active/inactive styling)

2. **Model Processing Pipeline:**
   - `availableModels` → `filteredModels` → `paginatedModels`
   - Each step triggers React useMemo recalculation

3. **Filter Logic Application:**
   - `enhanceModelInfo()` adds classification properties
   - Multiple filter conditions can be active simultaneously
   - Filters work on combined model set (external + local when Private Mode OFF)

4. **UI Components Affected:**
   - Filter button styling and active states
   - Model grid/list rendering
   - Model count displays
   - "No models found" state

5. **Performance Optimizations:**
   - useMemo prevents unnecessary recalculations
   - Pagination limits rendered models
   - Search applied before filters for efficiency

The key insight is that **favorites filter works regardless of Private Mode** - if you favorite local models while in Private Mode, they remain favorited and selectable when Private Mode is OFF, allowing you to use local models even when external models are available.

This comprehensive architecture ensures robust model selection with proper state management, database persistence, and seamless integration between local and external model providers.

