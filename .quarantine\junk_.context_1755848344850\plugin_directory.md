Updated Plugin Directory
update: 26/07/2025


## Existing Plugins for ChatLo Functions

### **Intelligence Processing Plugins**

**1. Smart Context Plugin** (`electron/plugins/examples/smart-context/SmartContextPlugin.ts`)
- **Purpose**: Enhances chat conversations with intelligent context awareness
- **Capabilities**: 
  - Analyzes user prompts to extract keywords and identify relevant context
  - Searches through previous conversations and files to find related information
  - Automatically enhances prompts with contextual information
  - Provides smart suggestions based on conversation history
- **Current Status**: Example implementation with basic keyword extraction
- **Intelligence Features**: 
  - Keyword extraction from user input
  - Context relevance scoring (threshold: 0.7)
  - Automatic context injection into prompts

**2. Intelligence Service** (`src/services/intelligenceService.ts`)
- **Purpose**: Core intelligence processing engine for the application
- **Capabilities**:
  - **Entity Extraction**: Identifies people, places, technologies, organizations from text
  - **Topic Analysis**: Categorizes content into business, technology, research domains
  - **Artifact Detection**: Recognizes code blocks, images, links, and structured content
  - **LLM Integration**: Supports local LLM models (Ollama, LM Studio) for enhanced processing
  - **Performance Optimization**: Hybrid approach with fallback to keyword-based extraction
- **Intelligence Features**:
  - Pattern-based entity recognition for technologies (React, Python, AWS, etc.)
  - Organization detection (Microsoft, Google, OpenAI, etc.)
  - Confidence scoring and quality assessment
  - Real-time processing with performance monitoring

### **Chat Enhancement Plugins**

**1. Smart Context Plugin** (also serves chat enhancement)
- **Chat Features**:
  - Pre-processes messages before sending to add relevant context
  - Post-processes responses to enhance with additional information
  - Provides contextual data injection during conversations
  - Maintains conversation continuity through intelligent context management

**2. Custom Toolbar Plugin** (`electron/plugins/examples/custom-toolbar/CustomToolbarPlugin.ts`)
- **Purpose**: Extends chat interface with custom UI elements
- **Capabilities**:
  - Adds custom toolbar items to chat input area
  - Provides template insertion functionality
  - Supports quick actions and shortcuts
  - Integrates with chat workflow for enhanced user experience

### **File Processing Plugins**

**Core File Processors** (`electron/fileProcessors/plugins/`):

**1. Text Plugin** (`TextPlugin.ts`)
- **Supports**: Plain text files (.txt, .log, .md, .json, .xml, .csv, .yaml, .ini, .conf)
- **Features**: UTF-8 encoding, file size validation, metadata extraction (word count, line count)

**2. PDF Plugin** (`PDFPlugin.ts`)
- **Supports**: PDF documents (.pdf)
- **Features**: Text extraction using pdf-parse library, metadata extraction, file size limits (100MB)
- **Status**: Optional plugin requiring pdf-parse dependency

**3. Word Plugin** (`WordPlugin.ts`)
- **Supports**: Word documents (.docx, .doc)
- **Features**: Text extraction using mammoth library, formatting preservation
- **Status**: Optional plugin requiring mammoth dependency

**4. Image Plugin** (`ImagePlugin.ts`)
- **Supports**: Images (.jpg, .jpeg, .png, .gif, .bmp, .webp, .tiff, .svg)
- **Features**: Advanced image processing using Sharp, metadata extraction, thumbnail generation
- **Status**: Optional plugin requiring sharp dependency

**5. Markdown Plugin** (`MarkdownPlugin.ts`)
- **Supports**: Markdown files (.md, .markdown, .mdown, .mkd)
- **Features**: Frontmatter parsing, metadata extraction, heading analysis
- **Status**: Core plugin, always available

**6. Excel Plugin** (`ExcelPlugin.ts`)
- **Supports**: Excel spreadsheets (.xlsx, .xls)
- **Features**: Sheet data extraction, cell content processing

**7. PowerPoint Plugin** (`PowerPointPlugin.ts`)
- **Supports**: PowerPoint presentations (.pptx, .ppt)
- **Features**: Slide content extraction, text processing

**8. OCR Plugin** (`OCRPlugin.ts`)
- **Supports**: Image-to-text conversion
- **Features**: Optical character recognition for extracting text from images

**Universal File Processor** (`electron/plugins/core-file-processor/TextPlugin.ts`):
- **Purpose**: Migrated to universal plugin framework
- **Features**: Handles multiple text-based formats with unified processing approach

### **Artifacts Management Plugins**

**1. Artifact Detection System** (`src/hooks/useArtifactDetection.ts`)
- **Purpose**: Automatically detects and manages artifacts in conversations
- **Capabilities**:
  - **Code Artifact Detection**: Identifies code blocks in messages and creates downloadable artifacts
  - **Image Artifact Management**: Handles generated images and visual content
  - **Content Processing**: Extracts substantial code blocks (>50 characters) for artifact creation
  - **Database Integration**: Automatically saves artifacts to database with metadata
- **Supported Artifact Types**: 
  - Code blocks with language detection
  - Generated images
  - Markdown content
  - Mermaid diagrams
  - HTML content
  - JSON data
  - Web links

**2. Artifact Management Components** (`src/components/artifacts/`)
- **Document Listing**: Provides interface for browsing and managing artifacts
- **Download Functionality**: Supports ZIP download of selected artifacts
- **Artifact Viewer**: Displays artifacts with syntax highlighting and formatting

### **Current Plugin Architecture Status**

**Strengths**:
- **Comprehensive File Support**: Handles most common document formats
- **Intelligence Foundation**: Basic entity extraction and topic analysis in place
- **Artifact System**: Automatic detection and management of conversation artifacts
- **Plugin Framework**: Universal plugin system with clear extension points

**Areas for Enhancement**:
- **Intelligence Plugins**: Currently limited to basic pattern matching; needs more sophisticated NLP plugins
- **Chat Enhancement**: Smart context plugin is basic; needs more advanced conversation analysis
- **Specialized Processors**: Could benefit from domain-specific file processors (legal documents, scientific papers, etc.)
- **Real-time Intelligence**: Current intelligence processing is mostly post-hoc; needs real-time analysis plugins

**Plugin Integration Points**:
- All plugins integrate through the unified IPC handler system
- Extension points defined for File Processing, Chat Enhancement, UI Extension, API Extension, and Intelligence Processing
- Plugin discovery and loading handled by Universal Plugin Manager
- Configuration and state management through centralized plugin registry

The existing plugin ecosystem provides a solid foundation for ChatLo's core functionality, with particular strength in file processing and basic intelligence features, while offering clear pathways for extending chat enhancement and artifacts management capabilities.
