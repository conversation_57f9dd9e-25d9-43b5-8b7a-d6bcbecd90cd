# Bug Report Template

**DEVELOPMENT STANDARD RULE 3.1**: All bugs must be logged with root cause analysis

## Bug Information
**Date**: [YYYY-MM-DD]  
**Reporter**: [Developer Name]  
**Session**: [Development session/feature being worked on]  
**Severity**: [Critical | Major | Minor]  

## Issue Description
**Summary**: [Brief description of the bug]

**Detailed Description**: 
[Comprehensive description of what went wrong]

**Steps to Reproduce**:
1. [Step 1]
2. [Step 2]
3. [Step 3]

**Expected Behavior**: 
[What should have happened]

**Actual Behavior**: 
[What actually happened]

## Root Cause Analysis
**Primary Cause**: 
[Why did this bug occur? What was the fundamental issue?]

**Contributing Factors**:
- [Factor 1: e.g., Missing type definition]
- [Factor 2: e.g., Inconsistent naming convention]
- [Factor 3: e.g., Lack of validation]

**Code Location**: 
[File paths and line numbers where the issue occurred]

## Pattern Recognition
**Similar Issues**: 
[Have we seen this type of bug before? Reference other bug reports if applicable]

**Pattern Type**: 
- [ ] Duplicate variable names
- [ ] Missing type definitions
- [ ] FontAwesome import inconsistencies
- [ ] External dependency issues
- [ ] Interface synchronization problems
- [ ] Unused code/variables
- [ ] Other: [Specify]

## Resolution
**Fix Applied**: 
[Detailed description of how the bug was fixed]

**Code Changes**: 
[List of files modified and key changes made]

**Verification**: 
[How was the fix verified? Tests run, manual verification, etc.]

## Prevention Strategy
**Immediate Prevention**: 
[What can be done right now to prevent this specific bug from recurring?]

**Systematic Prevention**: 
[What rules, tools, or processes should be implemented to prevent this class of bugs?]

**Recommended Actions**:
- [ ] Add linting rule
- [ ] Update development standards
- [ ] Add automated check
- [ ] Improve documentation
- [ ] Add test coverage
- [ ] Other: [Specify]

## Impact Assessment
**User Impact**: [How did/would this affect users?]
**Development Impact**: [How did this affect development workflow?]
**Time to Fix**: [How long did it take to identify and fix?]

## Follow-up Actions
- [ ] Update development standards document
- [ ] Add to common mistakes documentation
- [ ] Implement preventive measures
- [ ] Share learnings with team
- [ ] Add to automated checks

## Related Issues
[Links to related bug reports, GitHub issues, or documentation]

---
**Bug Status**: [Open | In Progress | Resolved | Closed]  
**Resolution Date**: [YYYY-MM-DD]  
**Verified By**: [Name]
