# ChatLo Technical Architecture

## System Overview

ChatLo is a desktop AI chat application built with a modern Electron + React stack, designed for local privacy while accessing cloud AI models through OpenRouter. The application features comprehensive file processing, artifacts management, and advanced AI interaction capabilities.

**Last Updated**: January 8, 2025
**Current Version**: 1.0.0
**Architecture Status**: ⚠️ Needs Optimization (306MB build size)

## Enhanced Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           ChatLo Desktop App                                │
├─────────────────────────────────────────────────────────────────────────────┤
│  Renderer Process (React 19 + TypeScript)                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │   Components    │  │   State Store   │  │   Services  │  │   Hooks     │ │
│  │   - Sidebar     │  │   (Zustand)     │  │   - OpenRouter│  │   - Artifacts│ │
│  │   - ChatArea    │  │   - Messages    │  │   - Database │  │   - Files   │ │
│  │   - Artifacts   │  │   - Settings    │  │   - Updates  │  │   - Images  │ │
│  │   - FileUI      │  │   - Files       │  │   - Router   │  │             │ │
│  │   - Settings    │  │   - Artifacts   │  │              │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│  Main Process (Node.js + Native Modules)                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │   IPC Handlers  │  │   Database      │  │   File Sys  │  │   Updater   │ │
│  │   - DB Ops      │  │   (SQLite)      │  │   - Manager │  │   (electron-│ │
│  │   - File Ops    │  │   - Migrations  │  │   - Indexing│  │    updater) │ │
│  │   - Settings    │  │   - CRUD Ops    │  │   - Watching│  │             │ │
│  │   - Artifacts   │  │   - Files       │  │             │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  └─────────────┘ │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │                    File Processing Layer                                │ │
│  │   ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐   │ │
│  │   │ PDF Parser  │  │ OCR Engine  │  │ Image Proc  │  │ Office Docs │   │ │
│  │   │ (pdf-parse) │  │(Tesseract.js│  │   (Sharp)   │  │  (Mammoth)  │   │ │
│  │   │    ~12MB    │  │   ~45MB)    │  │   ~25MB     │  │    ~8MB     │   │ │
│  │   └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘   │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
                                      │
                                      ▼
                            ┌─────────────────┐
                            │   External APIs │
                            │   - OpenRouter  │
                            │   - GitHub      │
                            │     (Updates)   │
                            └─────────────────┘
```

## Core Components (Updated Architecture)

### 1. Main Process (electron/main.ts)
**Responsibilities:**
- Window management and lifecycle
- Database initialization and management
- IPC communication with renderer
- Auto-updater configuration
- File system operations and watching
- File processing coordination

**Key Features:**
- SQLite database with migration system (v4)
- Secure IPC handlers for all database operations
- File system manager with indexing
- File processors for multiple formats
- Auto-updater with GitHub releases
- Development vs production environment handling

**Size Impact**: ~150MB (Electron runtime + native modules)

### 2. Renderer Process (React 19 App)
**Responsibilities:**
- User interface rendering with advanced components
- User interaction handling (drag-drop, file selection)
- State management with complex data flows
- API communication and streaming
- Artifacts management and display
- File attachment and preview

**Architecture Pattern:** Component-Service-Store-Hook

**Key Components:**
- **Artifacts System**: Sidebar with resizable panels
- **File Handling**: Drag-drop, autocomplete, preview
- **Reasoning Display**: Collapsible sections
- **Model Selection**: Enhanced filtering and categorization

### 3. Database Layer (electron/database.ts)
**Design Pattern:** Repository Pattern with Migration System

**Enhanced Schema (v4):**

```sql
-- Conversations table (enhanced)
CREATE TABLE conversations (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  is_pinned INTEGER DEFAULT 0
);

-- Messages table (enhanced)
CREATE TABLE messages (
  id TEXT PRIMARY KEY,
  conversation_id TEXT NOT NULL,
  role TEXT NOT NULL,
  content TEXT NOT NULL,
  model TEXT,
  created_at TEXT NOT NULL,
  is_pinned INTEGER DEFAULT 0,
  FOREIGN KEY (conversation_id) REFERENCES conversations (id)
);

-- Files table (new)
CREATE TABLE files (
  id TEXT PRIMARY KEY,
  filename TEXT NOT NULL,
  filepath TEXT NOT NULL,
  file_type TEXT NOT NULL,
  mime_type TEXT,
  size INTEGER,
  content_hash TEXT,
  content TEXT,
  metadata TEXT, -- JSON
  indexed_at TEXT NOT NULL
);

-- Artifacts table (new)
CREATE TABLE artifacts (
  id TEXT PRIMARY KEY,
  message_id TEXT NOT NULL,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  metadata TEXT, -- JSON
  original_index INTEGER,
  created_at TEXT NOT NULL,
  FOREIGN KEY (message_id) REFERENCES messages (id)
);

-- File attachments table (new)
CREATE TABLE file_attachments (
  id TEXT PRIMARY KEY,
  message_id TEXT NOT NULL,
  file_id TEXT NOT NULL,
  attachment_type TEXT DEFAULT 'attachment',
  created_at TEXT NOT NULL,
  FOREIGN KEY (message_id) REFERENCES messages (id),
  FOREIGN KEY (file_id) REFERENCES files (id)
);

-- Settings table
CREATE TABLE settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL -- JSON serialized
);
```

**Enhanced Features:**
- WAL mode for better performance
- Foreign key constraints with cascading
- Automatic ID generation with UUIDs
- JSON settings and metadata storage
- Migration versioning system (v4)
- File indexing and content storage
- Artifacts persistence
- Pinned conversations and messages

### 4. File Processing Layer (electron/fileProcessors.ts)
**New Architecture Component** - Handles multiple file formats

**Supported Formats:**
- **PDF**: pdf-parse (~12MB) - Text extraction
- **Word**: mammoth (~8MB) - .docx processing
- **Excel**: xlsx (~8MB) - Spreadsheet processing
- **PowerPoint**: Custom parser - .pptx processing
- **Images**: sharp (~25MB) + tesseract.js (~45MB) - Processing + OCR
- **Text/Markdown**: Native Node.js - Text processing

**Size Impact**: ~98MB total (major contributor to app size)

**Optimization Opportunities:**
- Lazy loading of processors
- Optional feature flags
- Browser API alternatives for basic operations

### 5. State Management (Zustand) - Enhanced
**Store Structure:**
```typescript
interface AppState {
  // Core Data
  conversations: Conversation[]
  messages: Message[]
  settings: Settings
  models: OpenRouterModel[]

  // File System
  files: FileRecord[]
  attachedFiles: FileRecord[]

  // Artifacts System
  artifacts: {
    isOpen: boolean
    isFullscreen: boolean
    currentArtifact: Artifact | null
    artifacts: Artifact[]
    sidebarWidth: number
  }

  // UI State
  sidebarOpen: boolean
  isLoading: boolean
  streamingMessage: string | null
  searchTerm: string

  // Enhanced Actions
  loadConversations: () => Promise<void>
  sendStreamingMessage: (content: string) => Promise<void>
  togglePinMessage: (messageId: string) => Promise<void>
  addArtifact: (artifact: Artifact) => void
  openArtifact: (artifact: Artifact) => void
  // ... many more actions
}
```

**Enhanced Design Principles:**
- Async actions for all database operations
- Optimistic UI updates where appropriate
- Error handling in every async action
- Separation of sync and async state updates
- Complex state management for artifacts and files
- Real-time file system watching integration

### 6. OpenRouter Service (Enhanced)
**Architecture:** Service Layer Pattern with Vision Support

**Enhanced Features:**
- API key management and validation
- Streaming and non-streaming completions
- Multi-modal support (text + images)
- Error handling and retry logic
- Advanced model parameter configuration
- Request/response transformation
- Model categorization and filtering

**Implementation:**
```typescript
class OpenRouterService {
  private apiKey: string
  private baseURL: string

  async createStreamingChatCompletion(
    params: ChatCompletionParams,
    onChunk: (chunk: string) => void,
    onComplete: () => void,
    onError: (error: Error) => void
  ): Promise<void>

  async getModels(): Promise<OpenRouterModel[]>

  // Vision support for image analysis
  async createVisionCompletion(
    params: VisionCompletionParams
  ): Promise<string>
}
```

### 7. File System Manager (electron/fileSystem.ts)
**New Architecture Component** - Manages local file operations

**Responsibilities:**
- File indexing and metadata extraction
- Content processing coordination
- File watching with chokidar
- Chatlo folder management (~Documents/Chatlo)
- File type detection and validation

**Features:**
- Lazy content processing (metadata first)
- File hash-based change detection
- Automatic folder structure creation
- File copying and organization
- Search and autocomplete support

**Size Impact**: ~5MB (chokidar + utilities)

## Data Flow

### 1. Message Sending Flow
```
User Input → InputArea → Store.sendStreamingMessage() → 
OpenRouter API → Streaming Response → UI Update → 
Database Save → State Refresh
```

### 2. Conversation Management Flow
```
User Action → Component → Store Action → IPC Call → 
Database Operation → IPC Response → Store Update → UI Refresh
```

### 3. Settings Flow
```
Settings Change → Component → Store.updateSettings() → 
IPC Call → Database Save → Local State Update
```

## Security Architecture

### 1. Process Isolation
- Main process handles all sensitive operations
- Renderer process has no direct file system access
- IPC communication for all data operations

### 2. API Key Security
- API keys stored in encrypted database
- Never exposed to renderer process logs
- Validated before use

### 3. Content Security Policy
- Strict CSP in renderer process
- No eval() or inline scripts
- Controlled external resource loading

## Performance Optimizations

### 1. Database
- WAL mode for concurrent reads
- Indexed queries for fast lookups
- Prepared statements for repeated operations
- Connection pooling and reuse

### 2. UI Rendering
- React 19 concurrent features
- Virtualized conversation lists (planned)
- Debounced search and filtering
- Lazy loading of message history

### 3. Memory Management
- Streaming message handling
- Cleanup of event listeners
- Proper component unmounting
- Database connection management

## Error Handling Strategy

### 1. Layered Error Handling
```
UI Component → Store Action → Service Layer → 
Database/API → Error Boundary → User Notification
```

### 2. Error Types
- **Network Errors**: API timeouts, connection issues
- **Database Errors**: Schema violations, disk space
- **Validation Errors**: Invalid input, missing data
- **System Errors**: File permissions, memory issues

### 3. User Experience
- Toast notifications for errors
- Graceful degradation
- Retry mechanisms
- Clear error messages

## Deployment Architecture

### 1. Build Process
```
TypeScript Compilation → Vite Build → 
Electron Packaging → Code Signing → 
Distribution (GitHub Releases)
```

### 2. Update Mechanism
- GitHub releases for update distribution
- Differential updates for efficiency
- Automatic background checking
- User-controlled installation

### 3. Platform Support
- Windows (NSIS installer)
- macOS (DMG package)
- Linux (AppImage)

## Development Architecture

### 1. Development Stack
- **Frontend**: React 19 + TypeScript + Tailwind CSS
- **Backend**: Node.js + Electron + SQLite
- **Build**: Vite + Electron Builder
- **State**: Zustand + React Query (planned)

### 2. Code Organization (Current Structure)
```
chatlo/
├── electron/                    # Main process code
│   ├── main.ts                 # Electron main process
│   ├── preload.ts              # Preload script
│   ├── database.ts             # Database manager
│   ├── fileSystem.ts           # File system manager
│   ├── fileProcessors.ts       # File processing services
│   └── utils.ts                # Electron utilities
├── src/                        # Renderer process code
│   ├── components/             # React UI components
│   │   ├── artifacts/          # Artifacts system components
│   │   ├── magicui/           # UI utility components
│   │   ├── ChatArea.tsx        # Main chat interface
│   │   ├── Sidebar.tsx         # Conversation sidebar
│   │   ├── InputArea.tsx       # Message input with file support
│   │   ├── FileAttachments.tsx # File attachment UI
│   │   ├── ModelSelector.tsx   # Enhanced model selection
│   │   └── ...                 # Other components
│   ├── hooks/                  # Custom React hooks
│   │   ├── useArtifactDetection.ts # Artifact processing
│   │   └── ...                 # Other hooks
│   ├── pages/                  # Route pages
│   │   ├── HistoryPage.tsx     # Conversation history
│   │   └── SettingsPage.tsx    # Settings interface
│   ├── services/               # Business logic services
│   │   └── openrouter.ts       # OpenRouter API service
│   ├── store/                  # State management
│   │   └── index.ts            # Zustand store
│   ├── types/                  # TypeScript definitions
│   │   ├── index.ts            # Core types
│   │   └── artifacts.ts        # Artifact types
│   ├── utils/                  # Helper functions
│   │   ├── imageUtils.ts       # Image processing utilities
│   │   └── modelUtils.ts       # Model categorization
│   └── App.tsx                 # Main React component
├── .context/                   # Documentation
│   ├── project-progress.md     # Feature tracking
│   ├── technical-architecture.md # This file
│   └── deployment_progress.md  # Build optimization
└── package.json                # Dependencies and scripts
```

### 3. Development Workflow
- Hot reload in development
- TypeScript strict mode
- ESLint + Prettier
- Concurrent dev server and Electron

## Tech Stack Analysis & Optimization Recommendations

### Current Tech Stack Assessment

#### ✅ **Well-Chosen Core Technologies**
| Technology | Size Impact | Justification | Optimization Potential |
|------------|-------------|---------------|----------------------|
| **Electron 37.1.0** | ~150MB | Essential for desktop app | LOW - Core requirement |
| **React 19** | ~13MB | Modern UI with concurrent features | LOW - Efficient choice |
| **TypeScript 5.8.3** | ~0MB | Compile-time only | NONE - Dev dependency |
| **Tailwind CSS** | ~2MB | Utility-first, tree-shakeable | LOW - Already optimized |
| **Zustand** | ~1MB | Lightweight state management | NONE - Minimal footprint |
| **better-sqlite3** | ~15MB | High-performance local database | LOW - Essential for local storage |

#### ⚠️ **Problematic Dependencies (Size Concerns)**
| Technology | Size Impact | Purpose | Optimization Potential |
|------------|-------------|---------|----------------------|
| **tesseract.js** | ~45MB | OCR text extraction | **HIGH** - Lazy load, optional |
| **sharp** | ~25MB | Image processing | **MEDIUM** - Use Canvas API for basic ops |
| **pdf-parse** | ~12MB | PDF content extraction | **MEDIUM** - Lazy load, optional |
| **mammoth** | ~8MB | Word document processing | **HIGH** - Optional feature |
| **xlsx** | ~8MB | Excel file processing | **HIGH** - Optional feature |
| **react-markdown** | ~3MB | Markdown rendering | **LOW** - Could simplify |

#### 📊 **Size Breakdown Analysis**
```
Total App Size: ~306MB
├── Electron Runtime: ~150MB (49%)
├── File Processing: ~98MB (32%)
│   ├── Tesseract.js: ~45MB
│   ├── Sharp: ~25MB
│   ├── PDF/Office: ~28MB
├── React Ecosystem: ~20MB (7%)
├── Database & Core: ~20MB (7%)
└── Other Dependencies: ~18MB (5%)
```

### 🎯 **Optimization Strategy**

#### **Phase 1: Immediate Wins (Target: -100MB)**
1. **Lazy Load File Processing** (-90MB)
   ```typescript
   // Current: Always loaded
   import Tesseract from 'tesseract.js'
   import sharp from 'sharp'

   // Optimized: Dynamic imports
   const loadOCR = async () => {
     const { default: Tesseract } = await import('tesseract.js')
     return Tesseract
   }

   const loadImageProcessor = async () => {
     const { default: sharp } = await import('sharp')
     return sharp
   }
   ```

2. **Feature Flags** (-50MB)
   ```typescript
   interface FeatureFlags {
     ocr: boolean
     advancedImageProcessing: boolean
     officeDocuments: boolean
     pdfProcessing: boolean
   }
   ```

#### **Phase 2: Architecture Changes (Target: -50MB)**
1. **Browser API Alternatives**
   ```typescript
   // Replace Sharp for basic operations
   const resizeImageCanvas = (file: File, maxWidth: number) => {
     const canvas = document.createElement('canvas')
     const ctx = canvas.getContext('2d')
     // Canvas-based image processing
   }
   ```

2. **Modular Plugin System**
   ```typescript
   interface FileProcessor {
     name: string
     supportedTypes: string[]
     size: number
     load: () => Promise<ProcessorModule>
   }
   ```

#### **Phase 3: Advanced Optimization (Target: -30MB)**
1. **Custom Electron Build**
   - Remove unused Chromium features
   - Custom V8 compilation flags

2. **Native Module Optimization**
   - Replace JS libraries with native code where beneficial
   - Better compression algorithms

### 🔧 **Recommended Tech Stack Changes**

#### **Immediate Changes (Week 1)**
1. **Make File Processing Optional**
   ```json
   {
     "dependencies": {
       // Core dependencies (always loaded)
       "electron": "^37.1.0",
       "react": "^19.1.0",
       "better-sqlite3": "^12.1.1"
     },
     "optionalDependencies": {
       // Heavy processors (lazy loaded)
       "tesseract.js": "^6.0.1",
       "sharp": "^0.34.2",
       "pdf-parse": "^1.1.1",
       "mammoth": "^1.9.1",
       "xlsx": "^0.18.5"
     }
   }
   ```

2. **Implement Feature Detection**
   ```typescript
   const features = {
     ocr: await checkModuleAvailable('tesseract.js'),
     imageProcessing: await checkModuleAvailable('sharp'),
     pdfProcessing: await checkModuleAvailable('pdf-parse')
   }
   ```

#### **Alternative Technologies to Consider**
| Current | Alternative | Size Savings | Trade-offs |
|---------|-------------|--------------|------------|
| tesseract.js | Cloud OCR API | -45MB | Requires internet, privacy concerns |
| sharp | Canvas API | -20MB | Limited functionality |
| pdf-parse | PDF.js | -8MB | Browser-based, limited features |
| mammoth | Cloud conversion | -8MB | Requires internet |
| xlsx | Browser FileReader | -8MB | Limited Excel features |

### 📈 **Performance Impact Analysis**

#### **Current Performance Characteristics**
- **Startup Time**: ~5-8 seconds (heavy module loading)
- **Memory Usage**: ~300-500MB (all modules loaded)
- **File Processing**: Fast (native modules)
- **Build Time**: ~2-3 minutes

#### **Optimized Performance Targets**
- **Startup Time**: <3 seconds (core only)
- **Memory Usage**: <200MB (lazy loading)
- **File Processing**: On-demand (progressive loading)
- **Build Time**: <1 minute (smaller bundle)

## Scalability Considerations (Updated)

### 1. Data Growth
- Conversation pagination with virtual scrolling
- Message archiving and compression
- Database vacuum operations
- Storage quota management
- File content caching strategies

### 2. Feature Expansion
- **Plugin architecture** (MCP integration)
- **Modular component system** with lazy loading
- **Service abstraction layers** for file processing
- **Configuration-driven features** with feature flags
- **Progressive enhancement** model

### 3. Performance Scaling
- **Virtual scrolling** for large conversation lists
- **Background processing** for file operations
- **Intelligent caching** strategies
- **Resource optimization** with lazy loading
- **Memory management** for large files

## 🚀 **Action Plan for Tech Stack Optimization**

### **Critical Priority (Week 1)**
1. **Implement Lazy Loading Architecture**
   ```typescript
   // Create dynamic module loader
   class ModuleLoader {
     private static cache = new Map()

     static async loadProcessor(type: 'ocr' | 'image' | 'pdf' | 'office') {
       if (this.cache.has(type)) return this.cache.get(type)

       const module = await this.dynamicImport(type)
       this.cache.set(type, module)
       return module
     }
   }
   ```

2. **Add Feature Flags System**
   ```typescript
   // Feature flag configuration
   interface AppFeatures {
     fileProcessing: {
       ocr: boolean
       advancedImageProcessing: boolean
       officeDocuments: boolean
       pdfProcessing: boolean
     }
     ui: {
       artifacts: boolean
       reasoning: boolean
       pinning: boolean
     }
   }
   ```

3. **Implement Progressive Loading UI**
   - Show core chat functionality immediately
   - Display "Loading advanced features..." for file processing
   - Graceful degradation when modules unavailable

### **High Priority (Week 2-3)**
1. **Replace Heavy Dependencies**
   - Canvas API for basic image operations
   - FileReader API for simple file handling
   - Browser-native PDF viewer for viewing

2. **Modular Build System**
   ```json
   {
     "scripts": {
       "build:core": "Build core chat functionality only",
       "build:full": "Build with all features",
       "build:custom": "Build with selected features"
     }
   }
   ```

3. **Plugin Architecture Foundation**
   ```typescript
   interface FileProcessorPlugin {
     name: string
     version: string
     supportedTypes: string[]
     size: number
     load(): Promise<ProcessorModule>
     unload(): void
   }
   ```

### **Medium Priority (Month 2)**
1. **Cloud Processing Options**
   - Optional cloud OCR service
   - Hybrid local/cloud processing
   - User choice between local and cloud

2. **Advanced Optimization**
   - Custom Electron build
   - Native module optimization
   - Better compression algorithms

### **Success Metrics**
| Metric | Current | Target | Timeline |
|--------|---------|--------|----------|
| **App Size** | 306MB | <150MB | Week 2 |
| **Startup Time** | 5-8s | <3s | Week 1 |
| **Memory Usage** | 300-500MB | <200MB | Week 2 |
| **Feature Completeness** | 100% | 95%+ | Maintained |

## 📋 **Implementation Checklist**

### **Week 1: Foundation**
- [ ] Implement dynamic module loading system
- [ ] Add feature flags configuration
- [ ] Create progressive loading UI
- [ ] Test core functionality without heavy deps
- [ ] Measure size reduction

### **Week 2: Optimization**
- [ ] Replace Sharp with Canvas API for basic operations
- [ ] Implement lazy loading for Tesseract.js
- [ ] Make PDF/Office processing optional
- [ ] Add feature detection and graceful degradation
- [ ] Test all functionality with optimizations

### **Week 3: Architecture**
- [ ] Implement plugin architecture foundation
- [ ] Create modular build system
- [ ] Add cloud processing options
- [ ] Optimize build configuration
- [ ] Performance testing and validation

### **Month 2: Advanced**
- [ ] Custom Electron build exploration
- [ ] Native module optimization
- [ ] Advanced compression techniques
- [ ] Final size optimization
- [ ] Production deployment testing

## 🎯 **Conclusion**

The current ChatLo architecture is **feature-complete but oversized** at 306MB. The primary issue is the **synchronous loading of heavy file processing dependencies** that account for ~32% of the total size.

**Key Recommendations:**
1. **Immediate**: Implement lazy loading for file processors → Target: 210MB (-31%)
2. **Short-term**: Make file processing optional → Target: 160MB (-48%)
3. **Long-term**: Implement plugin architecture → Target: 100MB (-67%)

The architecture is **well-designed** with good separation of concerns, but needs **optimization for distribution**. The core chat functionality is solid and should remain unchanged while file processing becomes modular and optional.
