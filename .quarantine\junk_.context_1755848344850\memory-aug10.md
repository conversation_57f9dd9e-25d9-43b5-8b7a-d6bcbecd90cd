### Memory – Aug 10

- **Current state**
  - Kernel intelligence APIs active: `intelligence:write/read` (+ legacy `save/get`).
  - Dual storage: always write markdown first; JSON only when explicitly provided or on read stub creation.
  - Events: `intelligence:markdownSaved`, `intelligence:updated`, `task:progress` broadcasting from main.
  - HomePage Organize now uses `UnifiedOrganizeService` (no legacy queue/fallback), respects Just‑in‑Time.

- **Changes made**
  - Added core modules: `PathResolver`, `VaultCoreService`, `EventBus`, `IntelligenceCoreService`.
  - Registered `intelligence:*` and `events:*` via `APIRegistry` and compiled main.
  - Modified save flow: markdown-first; emit `markdownSaved`; JSON optional.
  - Rewired HomePage Organize to new pipeline and minimal event listeners.

- **Lessons learned (module relationships)**
  - Legacy queue + adapter fallback conflicted with the kernel, causing writes to relative paths. Isolation removes this confusion.
  - Main process registration and compile/restart are critical; stale Electron instances lead to “No handler” errors.
  - Windows path normalization and stable hashing must be centralized (PathResolver) to avoid drift.
  - Events decouple processing (markdown persist) from later parsing (JSON), aligning with Just‑in‑Time rules and resource limits.

- **Next steps**
  - Implement dedicated markdown→JSON parser in core, triggered by an explicit action or background task with resource guard.
  - Add Files/History listeners to refresh UI on `markdownSaved/updated` without auto-analysis.
  - Remove legacy RelativeStorage fallback from adapter once all callers migrate.
  - Expand docs (`API_REFERENCE.md`) and add tests: path normalization, dual write, event flow, parser.
