BOOT log:
[0] 
[0] > chatlo@1.0.0 dev:vite
[0] > vite
[0] 
[0] 
[0]   VITE v7.0.0  ready in 530 ms      
[0] 
[0]   ➜  Local:   http://localhost:5173/
[0]   ➜  Network: use --host to expose  
[1] 
[1] > chatlo@1.0.0 dev:electron-no-check
[1] > cross-env NODE_ENV=development electron . --no-type-check
[1] 
[1] 
[1] Initializing database at: C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\chatlo-dev.db
[1] Database integrity check passed
[1] Database encoding set to UTF-8 for international character support
[1] Database initialized successfully
[1] 🔧 FileProcessorService created (plugin-only)
[1] 🔧 Initializing FileProcessorService plugin system...
[1] 🔧 FileSystemManager created with FileProcessorService
[1] [PATH] Using vault root path: C:\Users\<USER>\Documents\Post-Kernel-Test3
[1] Plugin registered: TextPlugin v1.0.0
[1] Plugin registered: MarkdownPlugin v1.0.0
[1] Plugin registered: HTMLPlugin v1.0.0
[1] Plugin registered: CSVPlugin v1.0.0
[1] Plugin registered: BasicImagePlugin v1.0.0
[1] PDFPlugin initialized successfully
[1] Plugin registered: PDFPlugin v1.0.0
[1] WordPlugin initialized successfully with mammoth
[1] Plugin registered: WordPlugin v2.0.0
[1] ExcelPlugin initialized successfully with exceljs
[1] Plugin registered: ExcelPlugin v2.0.0
[1] PowerPointPlugin initialized successfully with officegen
[1] Plugin registered: PowerPointPlugin v2.0.0
[1] ImagePlugin (Sharp) initialized successfully
[1] Plugin registered: ImagePlugin v1.0.0
[1] OCRPlugin (Tesseract.js) initialized successfully
[1] Plugin registered: OCRPlugin v1.0.0
[1] PluginFileProcessorService initialized successfully
[1] 🔧 FileProcessorService plugin system initialized successfully
[1] [ELECTRON] Checking for vault system, vault root path: C:\Users\<USER>\Documents\Post-Kernel-Test3
[1] [ELECTRON] Vault system detected and ready
[1] [MAIN] Setting up IPC handlers...
[1] [MAIN] Registering core APIs...
[1] [MAIN] Starting registerCoreAPIs...
[1] [MAIN] Registering database APIs...
[1] [MAIN] Registering vault APIs...
[1] [MAIN] Vault category registered
[1] [MAIN] All vault APIs registered successfully
[1] [MAIN] Registering file system APIs...
[1] [MAIN] All file system APIs registered successfully
[1] [MAIN] Registering path APIs...
[1] [MAIN] All path APIs registered successfully
[1] [MAIN] Registering shell APIs...
[1] [MAIN] All shell APIs registered successfully
[1] [MAIN] Registering plugin management APIs...
[1] [MAIN] All plugin management APIs registered successfully
[1] [MAIN] Registering updater APIs...
[1] [MAIN] All updater APIs registered successfully
[1] [MAIN] Registering system monitoring APIs...
[1] [MAIN] All system monitoring APIs registered successfully
[1] [MAIN] Registering intelligence APIs...
[1] [MAIN] Registering events APIs...
[1] [MAIN] All intelligence APIs registered successfully
[1] [MAIN] registerCoreAPIs completed successfully
[1] [MAIN] Initializing API registry...
[1] [APIRegistry] Initializing API endpoints...
[1] [APIRegistry] Categories: [
[1]   'db',           'settings',
[1]   'processing',   'vault',
[1]   'files',        'path',
[1]   'shell',        'plugins',
[1]   'updater',      'system',
[1]   'intelligence', 'events'
[1] ]
[1] [APIRegistry] Processing category: db
[1] [APIRegistry] Endpoints in db: [
[1]   'getConversations',
[1]   'getConversation',
[1]   'createConversation',
[1]   'updateConversation',
[1]   'deleteConversation',
[1]   'togglePinConversation',
[1]   'getMessages',
[1]   'addMessage',
[1]   'togglePinMessage',
[1]   'getFiles',
[1]   'getFile',
[1]   'addFile',
[1]   'updateFile',
[1]   'deleteFile',
[1]   'getArtifacts',
[1]   'addArtifact',
[1]   'updateArtifact',
[1]   'removeArtifact',
[1]   'getConversationArtifacts',
[1]   'updateMessageIntelligence',
[1]   'addPinnedIntelligence',
[1]   'getPinnedIntelligence',
[1]   'getAllPinnedIntelligence',
[1]   'searchConversations',
[1]   'getConversationsWithArtifacts',
[1]   'getDatabaseHealth',
[1]   'createBackup',
[1]   'openAtPath',
[1]   'safeClose',
[1]   'prepareForDisconnect',
[1]   'connectPortableDB',
[1]   'migrateToPortablePath'
[1] ]
[1] [APIRegistry] Registering IPC handler: db:getConversations
[1] [APIRegistry] Registering IPC handler: db:getConversation
[1] [APIRegistry] Registering IPC handler: db:createConversation
[1] [APIRegistry] Registering IPC handler: db:updateConversation
[1] [APIRegistry] Registering IPC handler: db:deleteConversation
[1] [APIRegistry] Registering IPC handler: db:togglePinConversation
[1] [APIRegistry] Registering IPC handler: db:getMessages
[1] [APIRegistry] Registering IPC handler: db:addMessage
[1] [APIRegistry] Registering IPC handler: db:togglePinMessage
[1] [APIRegistry] Registering IPC handler: db:getFiles
[1] [APIRegistry] Registering IPC handler: db:getFile
[1] [APIRegistry] Registering IPC handler: db:addFile
[1] [APIRegistry] Registering IPC handler: db:updateFile
[1] [APIRegistry] Registering IPC handler: db:deleteFile
[1] [APIRegistry] Registering IPC handler: db:getArtifacts
[1] [APIRegistry] Registering IPC handler: db:addArtifact
[1] [APIRegistry] Registering IPC handler: db:updateArtifact
[1] [APIRegistry] Registering IPC handler: db:removeArtifact
[1] [APIRegistry] Registering IPC handler: db:getConversationArtifacts
[1] [APIRegistry] Registering IPC handler: db:updateMessageIntelligence
[1] [APIRegistry] Registering IPC handler: db:addPinnedIntelligence
[1] [APIRegistry] Registering IPC handler: db:getPinnedIntelligence
[1] [APIRegistry] Registering IPC handler: db:getAllPinnedIntelligence
[1] [APIRegistry] Registering IPC handler: db:searchConversations
[1] [APIRegistry] Registering IPC handler: db:getConversationsWithArtifacts
[1] [APIRegistry] Registering IPC handler: db:getDatabaseHealth
[1] [APIRegistry] Registering IPC handler: db:createBackup
[1] [APIRegistry] Registering IPC handler: db:openAtPath
[1] [APIRegistry] Registering IPC handler: db:safeClose
[1] [APIRegistry] Registering IPC handler: db:prepareForDisconnect
[1] [APIRegistry] Registering IPC handler: db:connectPortableDB
[1] [APIRegistry] Registering IPC handler: db:migrateToPortablePath
[1] [APIRegistry] Processing category: settings
[1] [APIRegistry] Endpoints in settings: [ 'get', 'set', 'setPortableMode', 'setDBPath' ]
[1] [APIRegistry] Registering IPC handler: settings:get
[1] [APIRegistry] Registering IPC handler: settings:set
[1] [APIRegistry] Registering IPC handler: settings:setPortableMode
[1] [APIRegistry] Registering IPC handler: settings:setDBPath
[1] [APIRegistry] Processing category: processing
[1] [APIRegistry] Endpoints in processing: [ 'enableOCR', 'getProcessingStatus' ]
[1] [APIRegistry] Registering IPC handler: processing:enableOCR
[1] [APIRegistry] Registering IPC handler: processing:getProcessingStatus
[1] [APIRegistry] Processing category: vault
[1] [APIRegistry] Endpoints in vault: [
[1]   'createDirectory',
[1]   'writeFile',
[1]   'appendFile',
[1]   'readDirectory',
[1]   'removeDirectory',
[1]   'removeFile',
[1]   'scanFolder',
[1]   'copyFile',
[1]   'pathExists',
[1]   'readFile',
[1]   'readFileBase64',
[1]   'getVaultRegistry',
[1]   'saveVaultRegistry',
[1]   'initializeVaultRoot',
[1]   'scanContexts'
[1] ]
[1] [APIRegistry] Registering IPC handler: vault:createDirectory
[1] [APIRegistry] Registering IPC handler: vault:writeFile
[1] [APIRegistry] Registering IPC handler: vault:appendFile
[1] [APIRegistry] Registering IPC handler: vault:readDirectory
[1] [APIRegistry] Registering IPC handler: vault:removeDirectory
[1] [APIRegistry] Registering IPC handler: vault:removeFile
[1] [APIRegistry] Registering IPC handler: vault:scanFolder
[1] [APIRegistry] Registering IPC handler: vault:copyFile
[1] [APIRegistry] Registering IPC handler: vault:pathExists
[1] [APIRegistry] Registering IPC handler: vault:readFile
[1] [APIRegistry] Registering IPC handler: vault:readFileBase64
[1] [APIRegistry] Registering IPC handler: vault:getVaultRegistry
[1] [APIRegistry] Registering IPC handler: vault:saveVaultRegistry
[1] [APIRegistry] Registering IPC handler: vault:initializeVaultRoot
[1] [APIRegistry] Registering IPC handler: vault:scanContexts
[1] [APIRegistry] Processing category: files
[1] [APIRegistry] Endpoints in files: [
[1]   'getVaultRootPath',
[1]   'setVaultRootPath',
[1]   'getChatloFolderPath',
[1]   'setChatloFolderPath',
[1]   'getIndexedFiles',
[1]   'getFileProcessorPlugins',
[1]   'setFileProcessorPluginEnabled',
[1]   'searchFiles',
[1]   'getMetadata',
[1]   'reindexTree',
[1]   'processFileContent',
[1]   'indexFile',
[1]   'indexVaultFile',
[1]   'indexAllFiles',
[1]   'copyFileToUploads',
[1]   'saveContentToVault',
[1]   'saveContentAsFile',
[1]   'addFileAttachment',
[1]   'getFileAttachments',
[1]   'getMessageFiles',
[1]   'removeFileAttachment',
[1]   'deleteFile',
[1]   'showOpenDialog',
[1]   'showSaveDialog',
[1]   'openPDFViewer',
[1]   'processFile',
[1]   'getFileContent'
[1] ]
[1] [APIRegistry] Registering IPC handler: files:getVaultRootPath
[1] [APIRegistry] Registering IPC handler: files:setVaultRootPath
[1] [APIRegistry] Registering IPC handler: files:getChatloFolderPath
[1] [APIRegistry] Registering IPC handler: files:setChatloFolderPath
[1] [APIRegistry] Registering IPC handler: files:getIndexedFiles
[1] [APIRegistry] Registering IPC handler: files:getFileProcessorPlugins
[1] [APIRegistry] Registering IPC handler: files:setFileProcessorPluginEnabled
[1] [APIRegistry] Registering IPC handler: files:searchFiles
[1] [APIRegistry] Registering IPC handler: files:getMetadata
[1] [APIRegistry] Registering IPC handler: files:reindexTree
[1] [APIRegistry] Registering IPC handler: files:processFileContent
[1] [APIRegistry] Registering IPC handler: files:indexFile
[1] [APIRegistry] Registering IPC handler: files:indexVaultFile
[1] [APIRegistry] Registering IPC handler: files:indexAllFiles
[1] [APIRegistry] Registering IPC handler: files:copyFileToUploads
[1] [APIRegistry] Registering IPC handler: files:saveContentToVault
[1] [APIRegistry] Registering IPC handler: files:saveContentAsFile
[1] [APIRegistry] Registering IPC handler: files:addFileAttachment
[1] [APIRegistry] Registering IPC handler: files:getFileAttachments
[1] [APIRegistry] Registering IPC handler: files:getMessageFiles
[1] [APIRegistry] Registering IPC handler: files:removeFileAttachment
[1] [APIRegistry] Registering IPC handler: files:deleteFile
[1] [APIRegistry] Registering IPC handler: files:showOpenDialog
[1] [APIRegistry] Registering IPC handler: files:showSaveDialog
[1] [APIRegistry] Registering IPC handler: files:openPDFViewer
[1] [APIRegistry] Registering IPC handler: files:processFile
[1] [APIRegistry] Registering IPC handler: files:getFileContent
[1] [APIRegistry] Processing category: path
[1] [APIRegistry] Endpoints in path: [ 'normalize', 'join', 'getContextDirs' ]
[1] [APIRegistry] Registering IPC handler: path:normalize
[1] [APIRegistry] Registering IPC handler: path:join
[1] [APIRegistry] Registering IPC handler: path:getContextDirs
[1] [APIRegistry] Processing category: shell
[1] [APIRegistry] Endpoints in shell: [ 'openPath', 'showItemInFolder' ]
[1] [APIRegistry] Registering IPC handler: shell:openPath
[1] [APIRegistry] Registering IPC handler: shell:showItemInFolder
[1] [APIRegistry] Processing category: plugins
[1] [APIRegistry] Endpoints in plugins: [
[1]   'getAll',
[1]   'enable',
[1]   'disable',
[1]   'discover',
[1]   'getConfig',
[1]   'updateConfig',
[1]   'getCapabilities',
[1]   'getAPIEndpoints',
[1]   'getAllAPIEndpoints'
[1] ]
[1] [APIRegistry] Registering IPC handler: plugins:getAll
[1] [APIRegistry] Registering IPC handler: plugins:enable
[1] [APIRegistry] Registering IPC handler: plugins:disable
[1] [APIRegistry] Registering IPC handler: plugins:discover
[1] [APIRegistry] Registering IPC handler: plugins:getConfig
[1] [APIRegistry] Registering IPC handler: plugins:updateConfig
[1] [APIRegistry] Registering IPC handler: plugins:getCapabilities
[1] [APIRegistry] Registering IPC handler: plugins:getAPIEndpoints
[1] [APIRegistry] Registering IPC handler: plugins:getAllAPIEndpoints
[1] [APIRegistry] Processing category: updater
[1] [APIRegistry] Endpoints in updater: [ 'check-for-updates', 'download-and-install' ]
[1] [APIRegistry] Registering IPC handler: updater:check-for-updates
[1] [APIRegistry] Registering IPC handler: updater:download-and-install
[1] [APIRegistry] Processing category: system
[1] [APIRegistry] Endpoints in system: [
[1]   'getAPIRegistry',
[1]   'getPerformanceMetrics',
[1]   'cleanupMiddleware',
[1]   'getMonitoringData',
[1]   'getEndpointMetrics',
[1]   'resetMonitoring',
[1]   'getErrorStatistics',
[1]   'clearErrorHistory'
[1] ]
[1] [APIRegistry] Registering IPC handler: system:getAPIRegistry
[1] [APIRegistry] Registering IPC handler: system:getPerformanceMetrics
[1] [APIRegistry] Registering IPC handler: system:cleanupMiddleware
[1] [APIRegistry] Registering IPC handler: system:getMonitoringData
[1] [APIRegistry] Registering IPC handler: system:getEndpointMetrics
[1] [APIRegistry] Registering IPC handler: system:resetMonitoring
[1] [APIRegistry] Registering IPC handler: system:getErrorStatistics
[1] [APIRegistry] Registering IPC handler: system:clearErrorHistory
[1] [APIRegistry] Processing category: intelligence
[1] [APIRegistry] Endpoints in intelligence: [
[1]   'write',
[1]   'read',
[1]   'save',
[1]   'get',
[1]   'analyze',
[1]   'listSessions',
[1]   'writeSession'
[1] ]
[1] [APIRegistry] Registering IPC handler: intelligence:write
[1] [APIRegistry] Registering IPC handler: intelligence:read
[1] [APIRegistry] Registering IPC handler: intelligence:save
[1] [APIRegistry] Registering IPC handler: intelligence:get
[1] [APIRegistry] Registering IPC handler: intelligence:analyze
[1] [APIRegistry] Registering IPC handler: intelligence:listSessions
[1] [APIRegistry] Registering IPC handler: intelligence:writeSession
[1] [APIRegistry] Processing category: events
[1] [APIRegistry] Endpoints in events: [ 'emit', 'subscribe', 'unsubscribe' ]
[1] [APIRegistry] Registering IPC handler: events:emit
[1] [APIRegistry] Registering IPC handler: events:subscribe
[1] [APIRegistry] Registering IPC handler: events:unsubscribe
[1] [MAIN] API registry initialized successfully
[1] [ELECTRON] Custom file protocol registered
[1] [PLUGIN] Starting plugin initialization...
[1] [PLUGIN] Discovering plugins in directories: [
[1]   'C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\plugins',
[1]   'C:\\Users\\<USER>\\AppData\\Roaming\\chatlo\\plugins'
[1] ]
[1] [PLUGIN] Scanning directory: C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\plugins
[1] [PROCESSING] OCR plugin disabled from settings
[1] Plugin registered: TextPlugin v1.0.0
[1] Plugin registered: MarkdownPlugin v1.0.0
[1] Plugin registered: HTMLPlugin v1.0.0
[1] Plugin registered: CSVPlugin v1.0.0
[1] Plugin registered: BasicImagePlugin v1.0.0
[1] PDFPlugin initialized successfully
[1] Plugin registered: PDFPlugin v1.0.0
[1] WordPlugin initialized successfully with mammoth
[1] Plugin registered: WordPlugin v2.0.0
[1] ExcelPlugin initialized successfully with exceljs
[1] Plugin registered: ExcelPlugin v2.0.0
[1] PowerPointPlugin initialized successfully with officegen
[1] Plugin registered: PowerPointPlugin v2.0.0
[1] ImagePlugin (Sharp) initialized successfully
[1] Plugin registered: ImagePlugin v1.0.0
[1] OCRPlugin (Tesseract.js) initialized successfully
[1] Plugin registered: OCRPlugin v1.0.0
[1] PluginFileProcessorService initialized successfully
[1] [PROCESSING] PluginFileProcessor initialized successfully - Office plugins loaded
[1] [PLUGIN] Found 5 entries in C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\plugins
[1] [PLUGIN] Found 0 plugins in C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\plugins
[1] [PLUGIN] Scanning directory: C:\Users\<USER>\AppData\Roaming\chatlo\plugins
[1] [PLUGIN] Directory does not exist: C:\Users\<USER>\AppData\Roaming\chatlo\plugins
[1] [PLUGIN] Found 0 plugins in C:\Users\<USER>\AppData\Roaming\chatlo\plugins
[1] [PLUGIN] Total plugins discovered: 0
[1] [PLUGIN] Discovered 0 plugins
[1] [PLUGIN] No plugins found, skipping plugin loading
[1] [PLUGIN] Plugin initialization completed
[1] [API] db:getConversations - Request: []
[1] [API] db:getConversations - Duration: 1ms
[1] [API] settings:get - Request: ["app-settings"]
[1] [API] settings:get - Duration: 0ms
[1] [API] vault:getVaultRegistry - Request: []
[1] === VAULT ROOT DEBUG ===
[1] Vault root from database: C:\Users\<USER>\Documents\Post-Kernel-Test3
[1] Vault root type: string
[1] Final vault root path: C:\Users\<USER>\Documents\Post-Kernel-Test3
[1] ========================
[1] [API] vault:getVaultRegistry - Duration: 12ms
[1] [API] vault:createDirectory - Request: ["C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\shared-dropbox"]
[1] [API] vault:createDirectory - Duration: 3ms
[1] [API] settings:get - Request: ["model-manifest-version"]
[1] [API] settings:get - Duration: 0ms
[1] [API] vault:pathExists - Request: ["C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\shared-dropbox\\.metadata.json"]
[1] [API] vault:pathExists - Duration: 1ms
[1] [API] vault:getVaultRegistry - Request: []
[1] === VAULT ROOT DEBUG ===
[1] Vault root from database: C:\Users\<USER>\Documents\Post-Kernel-Test3
[1] Vault root type: string
[1] Final vault root path: C:\Users\<USER>\Documents\Post-Kernel-Test3
[1] ========================
[1] [API] vault:getVaultRegistry - Duration: 2ms
[1] [API] vault:pathExists - Request: ["C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\shared-dropbox\\.files.json"]  
[1] [API] vault:pathExists - Duration: 1ms
[1] [18596:0816/125406.643:ERROR:CONSOLE:1] "Request Autofill.enable failed. {"code":-32601,"message":"'Autofill.enable' wasn't found"}", source: devtools://devtools/bundled/core/protocol_client/protocol_client.js (1)
[1] [18596:0816/125406.643:ERROR:CONSOLE:1] "Request Autofill.setAddresses failed. {"code":-32601,"message":"'Autofill.setAddresses' wasn't found"}", source: devtools://devtools/bundled/core/protocol_client/protocol_client.js (1)   

Console:
 [vite] connecting...
 [vite] connected.
 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
 ℹ️ 04:54:05 [OpenRouterService] doInitialize: OpenRouter service initialized Object
 ℹ️ 04:54:05 [LocalModelService] doInitialize: Local model service initialized Object
 🖥️ [SYSTEM] Using default hardware profile: 16GB RAM, mid-range tier
 ⚙️ [ADAPTIVE] Set thresholds for mid-range: JS heap 512MB, RAM 80.0%
 🔥 [CACHE] Loaded cache from storage: 3 warm, 0 cold
 ℹ️ 04:54:05 [CacheManager] doInitialize: Cache manager initialized Object
 getVaultRegistry: Checking cache first...
 ℹ️ 04:54:05 [IntelligenceService] doInitialize: Intelligence service initialized Object
 ℹ️ 04:54:05 [VaultFileHandler] VaultFileHandler initialized with streaming support 
 ℹ️ 04:54:05 [FileAnalysisService] doInitialize: File Analysis Service initialized 
 ℹ️ 04:54:05 [StreamingFileProcessor] doInitialize: Streaming file processor initialized Object
 ℹ️ 04:54:05 [FileProcessingQueue] doInitialize: File processing queue initialized 
 ℹ️ 04:54:05 [BatchFileProcessingService] doInitialize: Batch File Processing Service initialized 
 ℹ️ 04:54:05 [SmartInstructionService] doInitialize: Smart Instruction service initialized with LLM integration Object
 [FileTypeRenderer] Registered plugin for type: pdf
 [FileTypeRenderer] Registered plugin for type: markdown
 [FileTypeRenderer] Registered plugin for type: mermaid
 [FileTypeRenderer] Registered plugin for type: text
 [FileTypeRenderer] Registered plugin for type: image
 [FileTypeRenderer] Registered plugin for type: word
 [FileTypeRenderer] Registered plugin for type: excel
 [FileTypeRenderer] Registered plugin for type: powerpoint
 [FileTypeRenderer] Registered plugin for type: unsupported
 ℹ️ 04:54:05 [AskAINavigationService] Ask AI Navigation Service initialized 
 Window electronAPI available: true
 ElectronAPI methods: Array(12)
 ℹ️ 04:54:05 [OpenRouterService] initialization: Operation completed in 38ms: initialization Object
 ℹ️ 04:54:05 [OpenRouterService] initialize: Operation completed successfully: initialize Object
 ℹ️ 04:54:05 [LocalModelService] initialization: Operation completed in 35ms: initialization Object
 ℹ️ 04:54:05 [LocalModelService] initialize: Operation completed successfully: initialize Object
 ⚙️ [ADAPTIVE] Set thresholds for mid-range: JS heap 512MB, RAM 80.0%
 ℹ️ 04:54:05 [PerformanceMonitor] doInitialize: Performance monitor initialized with adaptive thresholds Object
 ℹ️ 04:54:05 [CacheManager] initialization: Operation completed in 16ms: initialization Object
 ℹ️ 04:54:05 [CacheManager] initialize: Operation completed successfully: initialize Object
 🌡️ [CACHE] Warm cache hit: vault_registry
 ℹ️ 04:54:05 [IntelligenceService] initialization: Operation completed in 14ms: initialization Object
 ℹ️ 04:54:05 [IntelligenceService] initialize: Operation completed successfully: initialize Object
 ℹ️ 04:54:05 [VaultFileHandler] initialization: Operation completed in 13ms: initialization Object
 ℹ️ 04:54:05 [VaultFileHandler] initialize: Operation completed successfully: initialize Object
 ℹ️ 04:54:05 [FileAnalysisService] initialization: Operation completed in 13ms: initialization Object
 ℹ️ 04:54:05 [FileAnalysisService] initialize: Operation completed successfully: initialize Object
 ℹ️ 04:54:05 [StreamingFileProcessor] initialization: Operation completed in 13ms: initialization Object
 ℹ️ 04:54:05 [StreamingFileProcessor] initialize: Operation completed successfully: initialize Object
 ℹ️ 04:54:05 [FileProcessingQueue] initialization: Operation completed in 13ms: initialization Object
 ℹ️ 04:54:05 [FileProcessingQueue] initialize: Operation completed successfully: initialize Object
 ℹ️ 04:54:05 [BatchFileProcessingService] initialization: Operation completed in 13ms: initialization Object
 ℹ️ 04:54:05 [BatchFileProcessingService] initialize: Operation completed successfully: initialize Object
 ℹ️ 04:54:05 [SmartInstructionService] initialization: Operation completed in 12ms: initialization Object
 ℹ️ 04:54:05 [SmartInstructionService] initialize: Operation completed successfully: initialize Object
 ℹ️ 04:54:05 [AskAINavigationService] initialization: Operation completed in 10ms: initialization Object
 ℹ️ 04:54:05 [AskAINavigationService] initialize: Operation completed successfully: initialize Object
 ℹ️ 04:54:05 [PerformanceMonitor] initialization: Operation completed in 17ms: initialization Object
 ℹ️ 04:54:05 [PerformanceMonitor] initialize: Operation completed successfully: initialize Object
 getVaultRegistry: ✅ Cache hit - returning cached registry
 ℹ️ 04:54:05 [ContextVaultService] loadVaults: Operation completed successfully: loadVaults 
 ℹ️ 04:54:05 [ContextVaultService] loadVaults: Operation completed in 16ms: loadVaults Object
 ℹ️ 04:54:05 [ContextVaultService] doInitialize: Context vault service initialized successfully Object
 ℹ️ 04:54:05 [ContextVaultService] initialization: Operation completed in 16ms: initialization Object
 ℹ️ 04:54:05 [ContextVaultService] initialize: Operation completed successfully: initialize Object
 Auto-loading models with saved API key...
 Navigation history updated: Object
 ℹ️ 04:54:05 [PerformanceMonitor] startMonitoring: Performance monitoring started Object
 🌡️ [CACHE] Warm cache hit: vault_cards
 getVaultCardsWithBackgroundRefresh: ✅ Cache hit - showing cached cards immediately
 refreshVaultCardsInBackground: 🔄 Starting background refresh...
 HomePage: ✅ Showing cached data immediately
 🔥 [CACHE] Hot cache hit: vault_cards
 getVaultCards: ✅ Cache hit - returning cached cards
 ℹ️ 04:54:05 [SharedDropboxService] getVaultRoot: Operation completed successfully: getVaultRoot 
 ℹ️ 04:54:05 [SharedDropboxService] getVaultRoot: Operation completed in 27ms: getVaultRoot Object
 Loaded manifest from primary URL: /models-manifest.json
 Using existing models or fetching from OpenRouter...
 ℹ️ 04:54:06 [SharedDropboxService] getVaultRoot: Operation completed successfully: getVaultRoot 
 ℹ️ 04:54:06 [SharedDropboxService] getVaultRoot: Operation completed in 3ms: getVaultRoot Object
 ℹ️ 04:54:06 [SharedDropboxService] loadFiles: Operation completed successfully: loadFiles 
 ℹ️ 04:54:06 [SharedDropboxService] loadFiles: Operation completed in 8ms: loadFiles Object
 ℹ️ 04:54:06 [SharedDropboxService] doInitialize: Shared dropbox initialized successfully Object
 ℹ️ 04:54:06 [SharedDropboxService] initialization: Operation completed in 91ms: initialization Object
 ℹ️ 04:54:06 [SharedDropboxService] initialize: Operation completed successfully: initialize Object
 Loaded manifest from primary URL: /models-manifest.json
 Using model manifest v2025.07.16
 Loaded 318 models
 Latest models found: Array(16)
 🔍 Checking local models...
 🔥 [CACHE] Hot cache hit: vault_cards
 getVaultCards: ✅ Cache hit - returning cached cards
 refreshVaultCardsInBackground: ✅ Background refresh completed
 ℹ️ 04:54:06 [LocalModelService] checkOllama: Ollama connected successfully Object
 ℹ️ 04:54:06 [LocalModelService] checkOllama: Operation completed successfully: checkOllama 
 ℹ️ 04:54:06 [LocalModelService] checkOllama: Operation completed in 56ms: checkOllama Object
 ℹ️ 04:54:06 [LocalModelService] checkLMStudio: LM Studio connected successfully {modelCount: 2, models: Array(2)}
 ℹ️ 04:54:06 [LocalModelService] checkLMStudio: Operation completed successfully: checkLMStudio 
 ℹ️ 04:54:06 [LocalModelService] checkLMStudio: Operation completed in 565ms: checkLMStudio {duration: 565}
 ℹ️ 04:54:06 [LocalModelService] checkLMStudio: LM Studio connected successfully {modelCount: 2, models: Array(2)}
 ℹ️ 04:54:06 [LocalModelService] checkLMStudio: Operation completed successfully: checkLMStudio 
 ℹ️ 04:54:06 [LocalModelService] checkLMStudio: Operation completed in 6ms: checkLMStudio {duration: 6}
 ℹ️ 04:54:06 [LocalModelService] checkOllama: Ollama connected successfully {modelCount: 4, models: Array(4)}
 ℹ️ 04:54:06 [LocalModelService] checkOllama: Operation completed successfully: checkOllama 
 ℹ️ 04:54:06 [LocalModelService] checkOllama: Operation completed in 9ms: checkOllama {duration: 9}
 📊 Provider Status: {ollama: {…}, lmstudio: {…}}
 🤖 All Local Models: (6) [{…}, {…}, {…}, {…}, {…}, {…}]
 ✅ Local model check complete. Found 6 local models. Total external models: 318
 Rendering right column, viewMode.currentMode: explorer
 Master content length: 0
 Master loading: false
 Navigation history updated: {currentIndex: 1, historyLength: 2, currentPath: '/files'}
 FilesPage mounted, loading file tree...
 Loading file tree for context: undefined
 getFileTree: Loading vault registry...
 getVaultRegistry: Checking cache first...
 🔥 [CACHE] Hot cache hit: vault_registry
 Selected context changed to: shared dropbox reloading file tree...
 Shared dropbox selected, switching to Explorer mode
 Loading file tree for context: undefined
 getFileTree: Loading vault registry...
 getVaultRegistry: Checking cache first...
 🔥 [CACHE] Hot cache hit: vault_registry
 File tree updated: 0 items
 Expanded folders: []
 Selected file: null
 Selected folder: null
 Current view mode: explorer
 getVaultRegistry: ✅ Cache hit - returning cached registry
 getVaultRegistry: ✅ Cache hit - returning cached registry
 getFileTree: Registry loaded: {version: '1.0', vaultRoot: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3', vaults: Array(2), lastScan: '2025-08-15T11:42:11.157Z', preferences: {…}}
 getFileTree: Registry loaded: {version: '1.0', vaultRoot: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3', vaults: Array(2), lastScan: '2025-08-15T11:42:11.157Z', preferences: {…}}
 Rendering right column, viewMode.currentMode: explorer
 Master content length: 0
 Master loading: false
 File tree updated: 0 items
 Expanded folders: []
 Selected file: null
 Selected folder: null
 Current view mode: explorer
 Raw file tree from vaultUIManager: (3) [{…}, {…}, {…}]
 Processing file tree with 3 root nodes
 Processing nodes at level:  nodes: 3
 Processing node: 📦 Shared Dropbox type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox nodes: 1
 Processing node: 唐詩300首.md type: file path: C:\Users\<USER>\Documents\Post-Kernel-Test3\shared-dropbox\唐詩300首.md
 Found file: 唐詩300首.md at: C:\Users\<USER>\Documents\Post-Kernel-Test3\shared-dropbox\唐詩300首.md
 Processing node: Personal Vault type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault nodes: 1
 Processing node: getting-started type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started nodes: 4
 Processing node: master.md type: file path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
 Found first master.md at: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
 Processing node: artifacts type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts nodes: 0
 Processing node: documents type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents nodes: 0
 Processing node: images type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images nodes: 0
 Processing node: Work Vault type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault nodes: 1
 Processing node: project type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project nodes: 4
 Processing node: master.md type: file path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\master.md
 Found file: master.md at: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\master.md
 Processing node: artifacts type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts nodes: 0
 Processing node: documents type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents nodes: 0
 Processing node: images type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images nodes: 0
 Setting expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Setting selected file and switching to master mode: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
 Raw file tree from vaultUIManager: (3) [{…}, {…}, {…}]
 Processing file tree with 3 root nodes
 Processing nodes at level:  nodes: 3
 Processing node: 📦 Shared Dropbox type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox nodes: 1
 Processing node: 唐詩300首.md type: file path: C:\Users\<USER>\Documents\Post-Kernel-Test3\shared-dropbox\唐詩300首.md
 Found file: 唐詩300首.md at: C:\Users\<USER>\Documents\Post-Kernel-Test3\shared-dropbox\唐詩300首.md
 Processing node: Personal Vault type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault nodes: 1
 Processing node: getting-started type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started nodes: 4
 Processing node: master.md type: file path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
 Found first master.md at: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
 Processing node: artifacts type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts nodes: 0
 Processing node: documents type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents nodes: 0
 Processing node: images type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images nodes: 0
 Processing node: Work Vault type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault nodes: 1
 Processing node: project type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project nodes: 4
 Processing node: master.md type: file path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\master.md
 Found file: master.md at: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\master.md
 Processing node: artifacts type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts nodes: 0
 Processing node: documents type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents nodes: 0
 Processing node: images type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images
 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images
 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images nodes: 0
 Setting expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Setting selected file and switching to master mode: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
 Rendering right column, viewMode.currentMode: explorer
 Master content length: 0
 Master loading: false
 File tree updated: 3 items
 Expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Selected file: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
 Selected folder: null
 Current view mode: explorer
 Auto-selecting first context folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
 Loading files from folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Rendering right column, viewMode.currentMode: explorer
 Master content length: 0
 Master loading: false
 Folder files loaded successfully: 4 items
 File tree updated: 3 items
 Expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Selected file: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
 Selected folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
 Current view mode: explorer
 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Rendering right column, viewMode.currentMode: explorer
 Master content length: 0
 Master loading: false
 Switching view mode from explorer to master
 Switching view mode from master to master
 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Rendering right column, viewMode.currentMode: master
 Master content length: 0
 Master loading: true
 File tree updated: 3 items
 Expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Selected file: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
 Selected folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
 Current view mode: master
 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Rendering right column, viewMode.currentMode: master
 Master content length: 1094
 Master loading: false
 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Rendering right column, viewMode.currentMode: master
 Master content length: 1094
 Master loading: false
 Loading files from folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Rendering right column, viewMode.currentMode: explorer
 Master content length: 1094
 Master loading: false
 File tree updated: 3 items
 Expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Selected file: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
 Selected folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
 Current view mode: explorer
 Folder files loaded successfully: 11 items
 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Rendering right column, viewMode.currentMode: explorer
 Master content length: 1094
 Master loading: false
 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Rendering right column, viewMode.currentMode: explorer
 Master content length: 1094
 Master loading: false
 FileViewerService: Opening file: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…ents\\22年China SEO Report_February for Vendor.pptx', fileName: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…ents\\22年China SEO Report_February for Vendor.pptx', fileType: undefined, isEditMode: false}
 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Rendering right column, viewMode.currentMode: explorer
 Master content length: 1094
 Master loading: false
 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…ents\\22年China SEO Report_February for Vendor.pptx', fileContentLength: 0, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
 [LABELS] 🎨 SmartLabelingInterface render called
 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
 [LABELS] 🎨 getDisplayIdeas called
 [LABELS] 🎨 labelState.available_ideas.length: 0
 [LABELS] 🎨 labelState.show_all_ideas: false
 [LABELS] 🎨 labelState.processing_status: idle
 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
 [Data-Pipeline] [Detect] filePath= C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx fileName= C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
 [Data-Pipeline] [Detect] detected type= powerpoint requiresProcessing= true
 DocumentViewer: Loading file content for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx Type: powerpoint
 [Data-Pipeline] [Display] start load method for powerpoint
 [ANNOTATIONS] 🔄 File path changed, loading annotations: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
 [ANNOTATIONS] 📖 Loading annotations for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/22年China SEO Report_February for Vendor.pptx
 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/22年China SEO Report_February for Vendor.pptx found at index: 70
 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
 [ANNOTATIONS] 📖 Loading annotations for: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…ents\\22年China SEO Report_February for Vendor.pptx', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started'}
 [ANNOTATIONS] 🔍 Component state updated: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…ents\\22年China SEO Report_February for Vendor.pptx', fileName: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…ents\\22年China SEO Report_February for Vendor.pptx', annotationsCount: 0, currentPageIndex: 0, isProcessing: false, …}
 [ANNOTATIONS] 🔍 OVERLAY: useEffect triggered {isOpen: true, filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…ents\\22年China SEO Report_February for Vendor.pptx', fileName: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…ents\\22年China SEO Report_February for Vendor.pptx'}
 🔄 [OVERLAY] File opened: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx Type: powerpoint
FilePageOverlay.tsx:548 [LABELS] 🔄 OVERLAY: File opened, SmartLabelingInterface will manage intelligence state
FilePageOverlay.tsx:552 [LABELS] 🔄 OVERLAY: Loading states reset, intelligence state preserved
FilesPage.tsx:256 File tree updated: 3 items
FilesPage.tsx:257 Expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:258 Selected file: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
FilesPage.tsx:259 Selected folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
FilesPage.tsx:260 Current view mode: explorer
FilePageOverlay.tsx:81 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…ents\\22年China SEO Report_February for Vendor.pptx', fileContentLength: 0, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:786 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:787 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:760 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:761 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:762 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:763 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:777 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
annotationStorageService.ts:71 [ANNOTATIONS] 📖 No existing intelligence data found
IntelligenceHub.tsx:157 [ANNOTATIONS] 📖 ✅ Loaded 0 annotations
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…ents\\22年China SEO Report_February for Vendor.pptx', fileContentLength: 0, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:786 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:787 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:760 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:761 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:762 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:763 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:777 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
DocumentViewer.tsx:93 [Data-Pipeline] [Display] text read success= true length= 1860971
FilePageOverlay.tsx:598 Content loaded: 1860971 characters
DocumentViewer.tsx:98 DocumentViewer: Content loaded successfully, length: 1860971
FilePageOverlay.tsx:81 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
PowerPointRenderer.tsx:113 Could not find icon undefined
log @ @fortawesome_react-fontawesome.js?v=2eafb94b:4005
FontAwesomeIcon @ @fortawesome_react-fontawesome.js?v=2eafb94b:4082
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateForwardRef @ react-dom_client.js?v=2eafb94b:6461
beginWork @ react-dom_client.js?v=2eafb94b:7864
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=2eafb94b:11623
performWorkUntilDeadline @ react-dom_client.js?v=2eafb94b:36
<FontAwesomeIcon>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
PowerPointRenderer @ PowerPointRenderer.tsx:113
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=2eafb94b:11623
performWorkUntilDeadline @ react-dom_client.js?v=2eafb94b:36
<PowerPointRenderer>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
render @ index.tsx:325
FileTypeRenderer @ index.tsx:390
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=2eafb94b:11623
performWorkUntilDeadline @ react-dom_client.js?v=2eafb94b:36
<FileTypeRenderer>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
renderContent @ DocumentViewer.tsx:402
DocumentViewer @ DocumentViewer.tsx:542
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=2eafb94b:11623
performWorkUntilDeadline @ react-dom_client.js?v=2eafb94b:36
<DocumentViewer>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
FilePageOverlay @ FilePageOverlay.tsx:591
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performSyncWorkOnRoot @ react-dom_client.js?v=2eafb94b:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=2eafb94b:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=2eafb94b:11558
(anonymous) @ react-dom_client.js?v=2eafb94b:11649
<FilePageOverlay>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
FilesPage @ FilesPage.tsx:1458
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performSyncWorkOnRoot @ react-dom_client.js?v=2eafb94b:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=2eafb94b:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=2eafb94b:11558
(anonymous) @ react-dom_client.js?v=2eafb94b:11649
<FilesPage>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
App @ App.tsx:200
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performSyncWorkOnRoot @ react-dom_client.js?v=2eafb94b:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=2eafb94b:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=2eafb94b:11558
(anonymous) @ react-dom_client.js?v=2eafb94b:11649
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…ents\\22年China SEO Report_February for Vendor.pptx', fileContentLength: 1860971, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:786 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:787 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:760 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:761 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:762 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:763 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:777 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:55 [LABELS] 🔄 SmartLabelingInterface: useEffect triggered
SmartLabelingInterface.tsx:56 [LABELS] 🔄 SmartLabelingInterface: filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
SmartLabelingInterface.tsx:57 [LABELS] 🔄 SmartLabelingInterface: fileContent length: 1860971
SmartLabelingInterface.tsx:58 [LABELS] 🔄 SmartLabelingInterface: onLabelsChanged callback: true
SmartLabelingInterface.tsx:59 [LABELS] 🔄 SmartLabelingInterface: Starting intelligence loading process (persisted first)
SmartLabelingInterface.tsx:147 [LABELS] 🔍 KERNEL-STATUS: Checking kernel availability
SmartLabelingInterface.tsx:206 [LABELS] 🔍 KERNEL-STATUS: Checking if kernel has intelligence for file
SmartLabelingInterface.tsx:207 [LABELS] 🔍 KERNEL-STATUS: filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
SmartLabelingInterface.tsx:341 [LABELS] 🔄 SmartLabelingInterface: loadOrProcessFileIntelligence called {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…ents\\22年China SEO Report_February for Vendor.pptx', fileContentLength: 1860971}
SmartLabelingInterface.tsx:348 [LABELS] 🔄 KERNEL: Querying kernel for existing intelligence
SmartLabelingInterface.tsx:302 [LABELS] 🔍 KERNEL: Querying for existing intelligence: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
SmartLabelingInterface.tsx:220 [LABELS] 🔍 KERNEL-STATUS: Kernel response: {success: true, hasData: false, ideasCount: 0, hasIntelligence: false}
SmartLabelingInterface.tsx:162 [LABELS] 🔍 KERNEL-STATUS: Kernel status: {available: true, hasAnalyze: true, hasGet: true, hasIntelligence: false, electronAPI: true, …}
SmartLabelingInterface.tsx:171 [LABELS] 🔍 KERNEL-STATUS: Kernel available but no existing intelligence found
SmartLabelingInterface.tsx:193 [LABELS] 🔍 KERNEL-STATUS: Kernel available but no existing intelligence found
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/22年China SEO Report_February for Vendor.pptx
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/22年China SEO Report_February for Vendor.pptx found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
SmartLabelingInterface.tsx:313 [LABELS] 🔍 KERNEL: Using contextPath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
SmartLabelingInterface.tsx:322 [LABELS] 🔍 KERNEL: ℹ️ Kernel returned no intelligence: no data
SmartLabelingInterface.tsx:378 [LABELS] 🔄 SmartLabelingInterface: Setting idle state - no existing intelligence
SmartLabelingInterface.tsx:382 [LABELS] 🏷️ SmartLabelingInterface: Notifying overlay with empty ideas array (no existing intelligence)
SmartLabelingInterface.tsx:392 [LABELS] ℹ️ SmartLabelingInterface: No stored intelligence found. Waiting for user action to analyze...
SmartLabelingInterface.tsx:384 [LABELS] 🏷️ Calling onLabelsChanged with empty array
SmartLabelingInterface.tsx:386 [LABELS] 🏷️ onLabelsChanged with empty array completed
FilesPage.tsx:1215 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
FilesPage.tsx:1216 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1215 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
FilesPage.tsx:1216 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1215 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
FilesPage.tsx:1216 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1215 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
FilesPage.tsx:1216 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1215 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
FilesPage.tsx:1216 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1215 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
FilesPage.tsx:1216 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1215 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
FilesPage.tsx:1216 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1215 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
FilesPage.tsx:1216 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1215 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
FilesPage.tsx:1216 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1215 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
FilesPage.tsx:1216 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1215 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
FilesPage.tsx:1216 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1401 Rendering right column, viewMode.currentMode: explorer
FilesPage.tsx:1402 Master content length: 1094
FilesPage.tsx:1403 Master loading: false
FilePageOverlay.tsx:81 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
PowerPointRenderer.tsx:113 Could not find icon undefined
log @ @fortawesome_react-fontawesome.js?v=2eafb94b:4005
FontAwesomeIcon @ @fortawesome_react-fontawesome.js?v=2eafb94b:4082
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateForwardRef @ react-dom_client.js?v=2eafb94b:6461
beginWork @ react-dom_client.js?v=2eafb94b:7864
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopConcurrentByScheduler @ react-dom_client.js?v=2eafb94b:10864
renderRootConcurrent @ react-dom_client.js?v=2eafb94b:10844
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=2eafb94b:11623
performWorkUntilDeadline @ react-dom_client.js?v=2eafb94b:36
<FontAwesomeIcon>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
PowerPointRenderer @ PowerPointRenderer.tsx:113
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=2eafb94b:11623
performWorkUntilDeadline @ react-dom_client.js?v=2eafb94b:36
<PowerPointRenderer>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
render @ index.tsx:325
FileTypeRenderer @ index.tsx:390
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=2eafb94b:11623
performWorkUntilDeadline @ react-dom_client.js?v=2eafb94b:36
<FileTypeRenderer>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
renderContent @ DocumentViewer.tsx:402
DocumentViewer @ DocumentViewer.tsx:542
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=2eafb94b:11623
performWorkUntilDeadline @ react-dom_client.js?v=2eafb94b:36
<DocumentViewer>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
FilePageOverlay @ FilePageOverlay.tsx:591
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performSyncWorkOnRoot @ react-dom_client.js?v=2eafb94b:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=2eafb94b:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=2eafb94b:11558
(anonymous) @ react-dom_client.js?v=2eafb94b:11649
<FilePageOverlay>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
FilesPage @ FilesPage.tsx:1458
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performSyncWorkOnRoot @ react-dom_client.js?v=2eafb94b:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=2eafb94b:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=2eafb94b:11558
(anonymous) @ react-dom_client.js?v=2eafb94b:11649
<FilesPage>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
App @ App.tsx:200
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performSyncWorkOnRoot @ react-dom_client.js?v=2eafb94b:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=2eafb94b:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=2eafb94b:11558
(anonymous) @ react-dom_client.js?v=2eafb94b:11649
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…ents\\22年China SEO Report_February for Vendor.pptx', fileContentLength: 1860971, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:786 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:787 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:760 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:761 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:762 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:763 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:777 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
FilePageOverlay.tsx:81 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
PowerPointRenderer.tsx:113 Could not find icon undefined
log @ @fortawesome_react-fontawesome.js?v=2eafb94b:4005
FontAwesomeIcon @ @fortawesome_react-fontawesome.js?v=2eafb94b:4082
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateForwardRef @ react-dom_client.js?v=2eafb94b:6461
beginWork @ react-dom_client.js?v=2eafb94b:7864
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=2eafb94b:11623
performWorkUntilDeadline @ react-dom_client.js?v=2eafb94b:36
<FontAwesomeIcon>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
PowerPointRenderer @ PowerPointRenderer.tsx:113
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=2eafb94b:11623
performWorkUntilDeadline @ react-dom_client.js?v=2eafb94b:36
<PowerPointRenderer>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
render @ index.tsx:325
FileTypeRenderer @ index.tsx:390
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=2eafb94b:11623
performWorkUntilDeadline @ react-dom_client.js?v=2eafb94b:36
<FileTypeRenderer>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
renderContent @ DocumentViewer.tsx:402
DocumentViewer @ DocumentViewer.tsx:542
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=2eafb94b:11623
performWorkUntilDeadline @ react-dom_client.js?v=2eafb94b:36
<DocumentViewer>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
FilePageOverlay @ FilePageOverlay.tsx:591
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performSyncWorkOnRoot @ react-dom_client.js?v=2eafb94b:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=2eafb94b:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=2eafb94b:11558
(anonymous) @ react-dom_client.js?v=2eafb94b:11649
<FilePageOverlay>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
FilesPage @ FilesPage.tsx:1458
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performSyncWorkOnRoot @ react-dom_client.js?v=2eafb94b:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=2eafb94b:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=2eafb94b:11558
(anonymous) @ react-dom_client.js?v=2eafb94b:11649
<FilesPage>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=2eafb94b:250
App @ App.tsx:200
react-stack-bottom-frame @ react-dom_client.js?v=2eafb94b:17424
renderWithHooks @ react-dom_client.js?v=2eafb94b:4206
updateFunctionComponent @ react-dom_client.js?v=2eafb94b:6619
beginWork @ react-dom_client.js?v=2eafb94b:7654
runWithFiberInDEV @ react-dom_client.js?v=2eafb94b:1485
performUnitOfWork @ react-dom_client.js?v=2eafb94b:10868
workLoopSync @ react-dom_client.js?v=2eafb94b:10728
renderRootSync @ react-dom_client.js?v=2eafb94b:10711
performWorkOnRoot @ react-dom_client.js?v=2eafb94b:10330
performSyncWorkOnRoot @ react-dom_client.js?v=2eafb94b:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=2eafb94b:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=2eafb94b:11558
(anonymous) @ react-dom_client.js?v=2eafb94b:11649
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…ents\\22年China SEO Report_February for Vendor.pptx', fileContentLength: 1860971, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:786 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:787 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:760 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:761 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:762 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:763 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:777 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
