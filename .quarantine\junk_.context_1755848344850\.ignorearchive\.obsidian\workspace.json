{"main": {"id": "ef04d6f4c011a476", "type": "split", "children": [{"id": "9b89e6569179dad2", "type": "tabs", "children": [{"id": "1f734401a493094b", "type": "leaf", "state": {"type": "markdown", "state": {"file": "modelselector_logic.md", "mode": "preview", "source": false}, "icon": "lucide-file", "title": "modelselector_logic"}}]}], "direction": "vertical"}, "left": {"id": "38b66905aab084ec", "type": "split", "children": [{"id": "bf6e19dd0725fd72", "type": "tabs", "children": [{"id": "300e4f75e84c69c0", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "2aa424861e429cfc", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "fc917d2cb2943ad5", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "555ca4698ee34473", "type": "split", "children": [{"id": "2257780421ccebe4", "type": "tabs", "children": [{"id": "d558a61e9e191c7b", "type": "leaf", "state": {"type": "backlink", "state": {"file": "modelselector_logic.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for modelselector_logic"}}, {"id": "ef0da4e9f0f05703", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "technical-architecture.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from technical-architecture"}}, {"id": "51f4ab6e8182741e", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "dafb6ed8f715eded", "type": "leaf", "state": {"type": "outline", "state": {"file": "technical-architecture.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of technical-architecture"}}]}], "direction": "horizontal", "width": 300}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "d558a61e9e191c7b", "lastOpenFiles": ["development-insights.md", "modelselector_logic.md", "PRD.md", "PRD 1.md", "technical-architecture.md", "README.md"]}