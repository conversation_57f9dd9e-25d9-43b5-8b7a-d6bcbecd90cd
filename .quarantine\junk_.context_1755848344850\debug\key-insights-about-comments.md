Let me review the new PAGE_REFERENCE.md document and then examine the FilePageOverlay.tsx comments to check for any conflicts.

Now let me examine all the comments in FilePageOverlay.tsx to check for conflicts:

## 🔍 **PAGE_REFERENCE.md vs FilePageOverlay.tsx ANALYSIS**

After reviewing both documents, I can identify several **critical conflicts** and **alignment opportunities**:

## 🚨 **MAJOR CONFLICTS IDENTIFIED:**

### **1. File Type Detection Conflict**
**PAGE_REFERENCE.md (Line 57):**
```
File type detection centralized via `FileTypeRenderer/detectFileType`
```

**FilePageOverlay.tsx (Lines 402-486):**
```typescript
// FILE TYPE DETECTION
// Target: Extract to FileTypeRegistry service
const detectFileType = (fileName: string): FileTypeInfo => {
```

**❌ CONFLICT:** FilePageOverlay has its own `detectFileType` function instead of using the centralized `FileTypeRenderer/detectFileType`

### **2. Content Loading Strategy Conflict**
**PAGE_REFERENCE.md (Line 58):**
```
Display content loaded via `vault:readFile`
```

**PAGE_REFERENCE.md (Line 62):**
```
If type requires/benefits from processing (pdf/image/markdown), `DocumentViewer` calls `files:processFile(path, typeHint)`
```

**FilePageOverlay.tsx (Lines 519-565):**
```typescript
// SAFE REFACTOR: Use plugin system for markdown, fallback for others
const loadGenericFileContent = async (typeInfo: FileTypeInfo): Promise<void> => {
  // Uses plugin system for markdown
  // Uses getFileContent for others
```

**❌ CONFLICT:** FilePageOverlay uses mixed loading strategies instead of the clear vault/plugin separation defined in PAGE_REFERENCE.md

### **3. Data Flow Handler Conflict**
**PAGE_REFERENCE.md (Line 73):**
```
FilePageOverlay consumes `onLabelsChanged(allIdeas)`:
- Sets `intelligence.fileIntelligence.key_ideas = allIdeas`
```

**FilePageOverlay.tsx (Line 1034):**
```typescript
onLabelsChanged={handleLabelsChanged}  // Should be hookHandleLabelsChanged!
```

**❌ CONFLICT:** FilePageOverlay is using the wrong handler function (we fixed this earlier but it shows the pattern)

### **4. Component Responsibility Conflict**
**PAGE_REFERENCE.md (Lines 23-25):**
```
- Orchestrates left (DocumentViewer) and right (IntelligenceHub) panels
- Owns page lifecycle and close action
- Consolidates extracted content and plugin metadata into the intelligence layer
```

**FilePageOverlay.tsx (Lines 400-486, 519-565, 587-652):**
```typescript
// FILE TYPE DETECTION - Target: Extract to FileTypeRegistry service
// GENERIC FILE CONTENT LOADING - Target: Extract to DocumentViewer component  
// PDF DOCUMENT LOADING - Target: Extract to usePDFDocument hook
```

**❌ CONFLICT:** FilePageOverlay is doing too much implementation instead of orchestration

## ✅ **ALIGNMENT OPPORTUNITIES:**

### **1. Unified Data Model - ALIGNED**
**PAGE_REFERENCE.md (Lines 39-49):**
```typescript
interface FileIntelligenceData {
  file: { path: string; type: string; content: string };
  intelligence: { keyIdeas: KeyIdea[]; summary?: string; fileIntelligence?: FileIntelligence; };
  ui: { selectedLabels: string[]; currentAnnotation: number };
  metadata?: { lastUpdated?: string; processingConfidence?: number; fileMetadata?: any };
}
```

**FilePageOverlay.tsx (Lines 123-133):**
```typescript
// UNIFIED FILE INTELLIGENCE HOOK
const {
  data: intelligenceData,
  updateFileContent,
  updateIntelligence,
  // ... matches the unified data model
```

**✅ ALIGNED:** Both use the same unified data structure

### **2. Component Hierarchy - ALIGNED**
**PAGE_REFERENCE.md (Lines 29-34):**
```
- FilePageOverlay
  - DocumentViewer (Left panel)
  - IntelligenceHub (Right panel)
```

**FilePageOverlay.tsx (Lines 1006-1037):**
```typescript
<DocumentViewer ... />
<IntelligenceHub ... />
```

**✅ ALIGNED:** Component hierarchy matches exactly

## 🎯 **RECOMMENDED ACTIONS:**

### **Priority 1: Fix File Type Detection**
```typescript
// REMOVE from FilePageOverlay.tsx:
const detectFileType = (fileName: string): FileTypeInfo => { ... }

// USE from FileTypeRenderer:
import { detectFileType } from '../components/FileTypeRenderer';
```

### **Priority 2: Align Content Loading Strategy**
```typescript
// IMPLEMENT PAGE_REFERENCE.md strategy:
// 1. vault:readFile for display content
// 2. files:processFile for intelligence extraction
// 3. Clear separation of concerns
```

### **Priority 3: Remove Implementation Details**
```typescript
// EXTRACT from FilePageOverlay.tsx:
// - loadGenericFileContent -> DocumentViewer
// - loadPDFWithPDFJS -> PDFRenderer  
// - All file type specific logic -> FileTypeRenderer plugins
```

### **Priority 4: Fix Handler Functions**
```typescript
// ENSURE correct handlers are used:
onLabelsChanged={hookHandleLabelsChanged}  // Not handleLabelsChanged
onProcessingComplete={hookHandleProcessingComplete}  // Not handleProcessingComplete
```

## 🚀 **CONCLUSION:**

The **PAGE_REFERENCE.md** provides excellent architectural guidance, but **FilePageOverlay.tsx** has several conflicts that need resolution:

1. **❌ Wrong file type detection** - Should use centralized FileTypeRenderer
2. **❌ Mixed content loading** - Should follow vault/plugin separation  
3. **❌ Too much implementation** - Should focus on orchestration
4. **❌ Wrong handler functions** - Should use hook-based handlers

The **unified data model** and **component hierarchy** are well-aligned, but the **implementation details** need to be extracted to follow the PAGE_REFERENCE.md architecture properly.

Should I implement these alignment fixes to make FilePageOverlay.tsx conform to the PAGE_REFERENCE.md specification? 🎯
