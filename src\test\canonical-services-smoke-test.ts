/**
 * Canonical Services Smoke Test Suite
 * Tests the core functionality of our canonical services after migration
 */

// All imports at the top
import { intelligence } from '../api/UnifiedAPIClient'
import { fileAnalysisService } from '../services/fileAnalysisService'
import { unifiedAnnotationService } from '../services/unifiedAnnotationService'
import { 
  clearVaultIntelligenceState, 
  storeFileIntelligence, 
  getFileIntelligence
} from '../services/deprecatedServicesShim'

// Minimal electron API mock for Node/tsx execution
const memoryStore: Record<string, any> = {}
if (!(global as any).window) {
  ;(global as any).window = {} as any
}
if (!(global as any).window.electronAPI) {
  ;(global as any).window.electronAPI = {
    invoke: async (channel: string, ...args: any[]) => {
      // Handle intelligence read/write used by tests
      if (channel === 'intelligence:write') {
        const [filePath, vaultPath, payload] = args
        const key = `${vaultPath}::${filePath}`
        memoryStore[key] = payload?.json ?? null
        return { success: true }
      }
      if (channel === 'intelligence:read') {
        const [filePath, vaultPath] = args
        const key = `${vaultPath}::${filePath}`
        return { success: true, data: memoryStore[key] ?? null }
      }
      // Default mock response
      return { success: true }
    }
  }
}

// Test configuration
const TEST_VAULT_PATH = '/tmp/test-vault'
const TEST_FILE_PATH = `${TEST_VAULT_PATH}/test-document.md`

async function testIntelligenceCoreService(): Promise<boolean> {
  console.log('🧠 Testing IntelligenceCoreService (via intelligenceClient)')
  
  try {
    // Test write operation
    const testIntelligence = {
      file_type: 'markdown',
      extracted_content: '# Test Document\nThis is a test.',
      key_ideas: ['Testing', 'Validation'],
      metadata: { created_at: new Date().toISOString() }
    }
    
    await intelligence.write('/test/file.md', '/test/vault', { json: testIntelligence })
    console.log('✅ Intelligence write successful')
    
    // Test read operation
    const readResult = await intelligence.read('/test/file.md', '/test/vault')
    if (!readResult?.data) throw new Error('Read result invalid')
    
    console.log('✅ Intelligence read successful')
    return true
  } catch (error) {
    console.error('❌ IntelligenceCoreService test failed:', error)
    return false
  }
}

async function testFileAnalysisService(): Promise<boolean> {
  console.log('📄 Testing FileAnalysisService')
  
  try {
    const testContent = '# Test Document\nThis is a test document for validation.'
    
    // Test document analysis (use valid config fields)
    const analysis = await fileAnalysisService.analyzeDocument(testContent, {
      local_model_preferred: 'ollama:gemma3:latest',
      min_ideas_required: 1
    })
    
    if (!analysis) throw new Error('Analysis result empty')
    console.log('✅ Document analysis successful')
    
    // Validate key ideas array exists
    if (!Array.isArray(analysis.key_ideas)) throw new Error('key_ideas is not an array')
    console.log(`✅ key_ideas extraction present (${analysis.key_ideas.length})`)
    
    return true
  } catch (error) {
    console.error('❌ FileAnalysisService test failed:', error)
    return false
  }
}

async function testAnnotationStorageService(): Promise<boolean> {
  console.log('🏷️ Testing annotationStorageService')
  
  try {
    const testAnnotation = {
      id: 'test-1',
      file_path: TEST_FILE_PATH,
      vault_path: TEST_VAULT_PATH,
      content: 'Test annotation',
      type: 'note',
      created_at: new Date().toISOString()
    }
    
    // Test save (filePath first, then annotation)
    await annotationStorageService.saveAnnotation(TEST_FILE_PATH, testAnnotation as any)
    console.log('✅ Annotation save successful')
    
    // Test retrieve via loadAnnotations and check by id
    const annotations = await annotationStorageService.loadAnnotations(TEST_FILE_PATH)
    const retrieved = annotations.find(a => a.id === 'test-1')
    if (!retrieved) throw new Error('Retrieval failed')
    
    console.log('✅ Annotation retrieval successful')
    
    // NEW: Test updateKeyIdeas method
    const testKeyIdeas = [
      { id: 'idea-1', text: 'Test idea 1', relevance_score: 0.9, user_confirmed: true },
      { id: 'idea-2', text: 'Test idea 2', relevance_score: 0.8, user_confirmed: false }
    ]
    
    const keyIdeasSuccess = await annotationStorageService.updateKeyIdeas(TEST_FILE_PATH, testKeyIdeas, null)
    if (!keyIdeasSuccess) throw new Error('updateKeyIdeas failed')
    console.log('✅ updateKeyIdeas successful')
    
    // Verify key ideas were saved
    const updatedAnnotations = await annotationStorageService.loadAnnotations(TEST_FILE_PATH)
    const intelligenceResult = await intelligence.read(TEST_FILE_PATH, TEST_VAULT_PATH)
    if (!intelligenceResult?.data?.key_ideas || intelligenceResult.data.key_ideas.length !== 2) {
      throw new Error('Key ideas not properly saved')
    }
    console.log('✅ Key ideas verification successful')
    
    return true
  } catch (error) {
    console.error('❌ annotationStorageService test failed:', error)
    return false
  }
}

async function testShimFunctionality(): Promise<boolean> {
  console.log('🔧 Testing Shim Functionality')
  
  try {
    // Test deprecated functions still work
    await clearVaultIntelligenceState('/test/vault')
    console.log('✅ clearVaultIntelligenceState shim working')
    
    await storeFileIntelligence('/test/file.md', { test: 'data' }, { vaultPath: '/test/vault' })
    console.log('✅ storeFileIntelligence shim working')
    
    const data = await getFileIntelligence('/test/file.md', '/test/vault')
    if (!data) throw new Error('Shim getFileIntelligence failed')
    
    console.log('✅ getFileIntelligence shim working')
    return true
  } catch (error) {
    console.error('❌ Shim functionality test failed:', error)
    return false
  }
}

async function testIntegrationFlow(): Promise<boolean> {
  console.log('🔄 Testing Integration Flow')
  
  try {
    // 1. Analyze document
    const analysis = await fileAnalysisService.analyzeDocument('# Test\nContent', {
      local_model_preferred: 'ollama:gemma3:latest',
      min_ideas_required: 1
    })
    
    // 2. Store intelligence
    await intelligence.write('/test/file.md', '/test/vault', { json: analysis })
    
    // 3. Create annotation
    await annotationStorageService.saveAnnotation('/test/file.md', {
      id: 'test-1',
      file_path: '/test/file.md',
      vault_path: '/test/vault',
      content: 'Test note',
      type: 'note',
      created_at: new Date().toISOString()
    } as any)
    
    // 4. Verify everything works
    const retrievedIntelligence = await intelligence.read('/test/file.md', '/test/vault')
    const retrievedAnnotations = await annotationStorageService.loadAnnotations('/test/file.md')
    const retrievedAnnotation = retrievedAnnotations.find(a => a.id === 'test-1')
    
    if (retrievedIntelligence?.data && retrievedAnnotation) {
      console.log('✅ Integration flow successful')
      return true
    }
    
    throw new Error('Integration verification failed')
  } catch (error) {
    console.error('❌ Integration test failed:', error)
    return false
  }
}

// Main test runner
export async function runCanonicalServicesSmokeTest(): Promise<void> {
  console.log(' Starting Canonical Services Smoke Test Suite...')
  
  const results = await Promise.all([
    testIntelligenceCoreService(),
    testFileAnalysisService(),
    testAnnotationStorageService(),
    testShimFunctionality(),
    testIntegrationFlow()
  ])
  
  const passedTests = results.filter(r => r).length
  const totalTests = results.length
  
  console.log(`\n📊 Results: ${passedTests}/${totalTests} tests passed`)
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Canonical services are working correctly.')
  } else {
    console.log('⚠️  Some tests failed. Please review the errors above.')
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runCanonicalServicesSmokeTest().catch(console.error)
}