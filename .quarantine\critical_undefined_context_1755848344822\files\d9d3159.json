{
  file_path: C:\\Users\\<USER>\\Documents\\Test18\\work-vault,
  key_ideas: [
    {
      id: keyword_idea_0,
      text: C:\\Users\\<USER>\\Documents\\Test18\\work-vault/client-project/documents/project-requirements,
      relevance_score: 55,
      intent_types: [
        topic
      ],
      weight: 0.55,
      auto_selected: true,
      user_confirmed: false,
      context: keyword_extraction,
      extracted_from: keyword_fallback
    }
  ],
  weighted_entities: [],
  human_connections: [],
  processing_confidence: 0.525,
  analysis_metadata: {
    model_used: keyword_fallback,
    processing_time_ms: 5,
    content_length: 88,
    fallback_used: true,
    timestamp: 2025-08-04T02:21:21.790Z
  },
  created_at: 2025-08-04T02:21:21.790Z,
  updated_at: 2025-08-04T02:21:21.790Z,
  storage_metadata: {
    file_path: C:\\Users\\<USER>\\Documents\\Test18\\work-vault,
    file_hash: d9d3159,
    stored_at: 2025-08-04T02:21:21.792Z,
    storage_version: 1.0
  }
}