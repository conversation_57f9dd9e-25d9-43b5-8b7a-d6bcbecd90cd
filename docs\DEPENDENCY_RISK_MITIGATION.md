# Dependency risk mitigation (canvas)

- Do not install heavy optional deps by default. Keep app size low.
- In kernel OCR path (electron/core/FileCoreService.ts), `canvas` is imported dynamically in a try/catch. If not present, we log and skip OCR render. No crash.
- Add a feature flag later to allow user to enable OCR rendering if they install `canvas`. Default OFF.

Rationale: meets size constraint and avoids broken builds on platforms without native canvas bindings.

