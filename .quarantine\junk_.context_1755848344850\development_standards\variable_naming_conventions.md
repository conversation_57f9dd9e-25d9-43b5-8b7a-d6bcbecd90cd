# Variable Naming Conventions

**DEVELOPMENT STANDARD RULE 2.1**: Prevent duplicate variables through systematic naming

## Naming Pattern
**Format**: `[context]_[purpose]_[type]` or `[component][Purpose][Type]`

## Examples

### ✅ Good Examples
```typescript
// State variables
const messagePin_state = useState(false)
const vaultSuggestion_modal = useState(false)
const intelligence_extraction_data = useState(null)

// Component props
interface MessageBubble_props {
  message_content: string
  pin_handler: () => void
}

// Service methods
const contextVault_service = {
  createVault_method: () => {},
  deleteVault_method: () => {}
}

// Database operations
const messagePin_database_operation = () => {}
const vaultAssignment_database_query = () => {}

// UI state
const chatInput_focus_state = useState(false)
const sidebarCollapse_animation_state = useState('closed')
```

### ❌ Bad Examples (Causes Duplicates)
```typescript
// Too generic - causes conflicts
const state = useState(false)  // Which state?
const modal = useState(false)  // Which modal?
const data = useState(null)    // What data?

// Unclear purpose
const handler = () => {}       // Handles what?
const service = {}             // What service?
const operation = () => {}     // What operation?

// Missing context
const isOpen = useState(false) // What is open?
const loading = useState(false) // What is loading?
```

## Context Categories

### Component Context
- `messagePin_` - Message pinning related
- `vaultSuggestion_` - Vault suggestion modal
- `chatInput_` - Chat input area
- `sidebar_` - Sidebar navigation
- `iconBar_` - Icon bar navigation
- `fileTree_` - File tree display
- `intelligence_` - Intelligence extraction

### Purpose Categories
- `state` - Component state
- `handler` - Event handlers
- `service` - Service methods
- `data` - Data objects
- `config` - Configuration
- `cache` - Cached values
- `animation` - Animation states
- `validation` - Validation logic

### Type Categories
- `boolean` - Boolean values
- `string` - String values
- `number` - Numeric values
- `array` - Array collections
- `object` - Object structures
- `function` - Function references
- `promise` - Async operations
- `ref` - React refs

## Specific Patterns

### React Hooks
```typescript
// State hooks
const [messagePin_visible_state, setMessagePin_visible_state] = useState(false)
const [vaultSuggestion_loading_state, setVaultSuggestion_loading_state] = useState(false)

// Effect hooks
useEffect(() => {
  // intelligence_extraction_effect
}, [intelligence_extraction_data])

// Ref hooks
const chatInput_element_ref = useRef<HTMLInputElement>(null)
const vaultModal_container_ref = useRef<HTMLDivElement>(null)
```

### Event Handlers
```typescript
// Click handlers
const messagePin_click_handler = () => {}
const vaultSuggestion_submit_handler = () => {}
const chatInput_focus_handler = () => {}

// Change handlers
const contextVault_selection_change_handler = () => {}
const fileTree_expansion_change_handler = () => {}
```

### Service Methods
```typescript
// Database services
const messagePin_database_service = {
  create_method: () => {},
  update_method: () => {},
  delete_method: () => {}
}

// Intelligence services
const intelligence_extraction_service = {
  extractEntities_method: () => {},
  suggestVaults_method: () => {},
  generateSummary_method: () => {}
}
```

### Interface Definitions
```typescript
// Component interfaces
interface MessageBubble_component_props {
  message_content_data: string
  pin_action_handler: () => void
  intelligence_extraction_data?: IntelligenceData
}

// Service interfaces
interface ContextVault_service_interface {
  createVault_method: (data: VaultData) => Promise<void>
  suggestVault_method: (content: string) => VaultSuggestion[]
}
```

## Common Mistake Prevention

### Duplicate Prevention Checklist
- [ ] Does the variable name include context?
- [ ] Does the variable name describe its purpose?
- [ ] Does the variable name indicate its type?
- [ ] Would this name conflict with existing variables?
- [ ] Is the name descriptive enough for future developers?

### Search Before Naming
Before creating a new variable, search the codebase for:
- Similar variable names
- Existing patterns in the same context
- Related functionality that might share naming

### Refactoring Guidelines
When encountering generic names:
1. Identify the context where it's used
2. Determine the specific purpose
3. Add appropriate type indication
4. Update all references consistently
5. Document the change in bug log if fixing a duplicate issue

## Enforcement

### Manual Checks
- Code review checklist includes variable naming
- Search for generic terms like `state`, `data`, `handler`
- Verify new variables follow the pattern

### Automated Checks (Future)
- ESLint rules for variable naming patterns
- Pre-commit hooks to detect generic names
- Automated suggestions for better names

## Benefits

### Code Quality
- Eliminates variable name conflicts
- Improves code readability
- Makes debugging easier
- Reduces cognitive load

### Development Efficiency
- Faster code navigation
- Better IDE autocomplete
- Easier refactoring
- Reduced merge conflicts

### Team Collaboration
- Consistent naming across developers
- Self-documenting code
- Easier code reviews
- Knowledge transfer facilitation
