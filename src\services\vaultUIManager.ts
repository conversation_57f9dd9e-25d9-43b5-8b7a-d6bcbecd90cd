import { VaultRegistry, ContextVault, ContextFolder, ContextVaultCard, FileTreeNode } from '../types'
import { faFolder, faFile, faFileText, faCube, faImage } from '@fortawesome/free-solid-svg-icons'
import { cacheManager } from './cacheManager'

/**
 * VaultUIManager Service
 * Handles scanning vaults, loading context data, and providing UI data structure
 * ✅ V02 COMPLIANT: Uses PathResolver for all path operations per architecture guidelines
 * 🚀 OPTIMIZED: Added caching and performance improvements for faster home page loading
 */
export class VaultUIManager {

  /**
   * V02 COMPLIANT: Use path:join IPC for all path operations
   */
  private async joinPath(...parts: string[]): Promise<string> {
    if (parts.length === 0) return ''
    if (parts.length === 1) return parts[0]

    try {
      const result = await window.electronAPI.path.join(...parts)
      return result.success && result.path ? result.path : parts.join('/')
    } catch (error) {
      console.warn('[VaultUIManager] Path join failed, using fallback:', error)
      return parts.join('/')
    }
  }

  constructor() {
    try {
      if (typeof window !== 'undefined' && (window as any).electronAPI?.events?.on) {
        (window as any).electronAPI.events.on('vault:rootChanged', async () => {
          try {
            await this.clearCaches()
            console.log('[VaultUIManager] Cleared caches on vault:rootChanged')
          } catch (e) {
            console.warn('[VaultUIManager] Failed clearing caches on vault:rootChanged', (e as Error)?.message)
          }
        })
      }
    } catch {}
  }

  /**
   * Generate a stable ID based on the context path
   * This ensures the same context always gets the same ID across scans
   */
  private generateStableId(contextPath: string): string {
    // Create a more robust hash of the context path
    let hash = 0;
    const str = contextPath.toLowerCase().replace(/[\/\\]/g, '/'); // Normalize path separators

    // Use a better hashing algorithm to reduce collisions
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Add timestamp and random component to ensure uniqueness
    const timestamp = Date.now().toString(36);
    const randomComponent = Math.random().toString(36).substr(2, 6);
    const hashComponent = Math.abs(hash).toString(36).padStart(8, '0').substring(0, 8);

    return `${hashComponent}_${timestamp}_${randomComponent}`;
  }

  /**
   * Clear all caches to ensure fresh data
   */
  async clearCaches(): Promise<void> {
    try {
      console.log('🔍 [VAULT-SETTING] === CLEAR CACHES START ===')
      
      // Clear vault registry cache specifically
      await this.clearVaultRegistryCache()
      
      // Clear other caches
      await cacheManager.remove('vault_scan_result')
      await cacheManager.remove('context_scan_result')
      
      console.log('🔍 [VAULT-SETTING] ✅ All caches cleared successfully')
      console.log('🔍 [VAULT-SETTING] === CLEAR CACHES END ===')
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error clearing caches:', error)
    }
  }
  
  /**
   * Clear vault registry cache specifically
   */
  async clearVaultRegistryCache(): Promise<void> {
    try {
      console.log('🔍 [VAULT-SETTING] Clearing vault registry cache...')
      await cacheManager.remove('vault_registry')
      console.log('🔍 [VAULT-SETTING] ✅ Vault registry cache cleared')
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error clearing vault registry cache:', error)
    }
  }

  /**
   * Get vault registry from storage with caching
   */
  async getVaultRegistry(): Promise<VaultRegistry | null> {
    try {
      console.log('🔍 [VAULT-SETTING] === GET VAULT REGISTRY START ===')
      console.log('🔍 [VAULT-SETTING] Checking cache first...')
      
      // Check cache first for faster loading
      const cacheKey = 'vault_registry'
      const cachedRegistry = await cacheManager.get<VaultRegistry>(cacheKey)
      
      if (cachedRegistry) {
        console.log('🔍 [VAULT-SETTING] ✅ Cache hit - returning cached registry')
        console.log('🔍 [VAULT-SETTING] Cached registry structure:', {
          vaultRoot: cachedRegistry.vaultRoot,
          vaultsCount: cachedRegistry.vaults?.length || 0,
          contextsCount: cachedRegistry.vaults?.reduce((sum, v) => sum + (v.contexts?.length || 0), 0) || 0
        })
        
        // SECURITY: Validate cached registry paths still exist
        console.log('🔍 [VAULT-SETTING] Validating cached registry paths...')
        const isCachedRegistryValid = await this.validateCachedRegistry(cachedRegistry)
        
        if (!isCachedRegistryValid) {
          console.log('🔍 [VAULT-SETTING] ⚠️ Cached registry contains invalid paths, clearing cache...')
          await cacheManager.remove(cacheKey)
          console.log('🔍 [VAULT-SETTING] 💾 Cache cleared, will load fresh registry')
        } else {
          console.log('🔍 [VAULT-SETTING] ✅ Cached registry paths are valid')
          console.log('🔍 [VAULT-SETTING] === GET VAULT REGISTRY END (CACHE) ===')
          return cachedRegistry
        }
      }

      console.log('🔍 [VAULT-SETTING] ❌ Cache miss or invalid - checking electronAPI availability...')
      // Check if electronAPI is available
      if (!window.electronAPI?.vault?.getVaultRegistry) {
        console.warn('🔍 [VAULT-SETTING] Vault API not available - running in development mode')
        console.log('🔍 [VAULT-SETTING] === GET VAULT REGISTRY END (NO API) ===')
        return null
      }

      console.log('🔍 [VAULT-SETTING] Calling electronAPI.vault.getVaultRegistry()...')
      const registry = await window.electronAPI.vault.getVaultRegistry()
      console.log('🔍 [VAULT-SETTING] Result from electronAPI:', registry)
      
      if (registry) {
        console.log('🔍 [VAULT-SETTING] Registry loaded successfully, analyzing structure:')
        console.log('🔍 [VAULT-SETTING] - Vault root:', registry.vaultRoot)
        console.log('🔍 [VAULT-SETTING] - Vaults count:', registry.vaults?.length || 0)
        
        if (registry.vaults && Array.isArray(registry.vaults)) {
          registry.vaults.forEach((vault, index) => {
            console.log(`🔍 [VAULT-SETTING] - Vault ${index + 1}:`, {
              name: vault.name,
              path: vault.path,
              pathType: typeof vault.path,
              contextsCount: vault.contexts?.length || 0
            })
          })
        }
        
        // Cache the registry for future use (5 minute TTL for fresh data)
        await cacheManager.set(cacheKey, registry)
        console.log('🔍 [VAULT-SETTING] 💾 Cached registry for future use')
      } else {
        console.log('🔍 [VAULT-SETTING] ⚠️ No registry returned from electronAPI')
        console.log('🔍 [VAULT-SETTING] Attempting to initialize vault system...')
        
        // Try to initialize the vault system
        const initializedRegistry = await this.initializeVaultSystem()
        if (initializedRegistry) {
          console.log('🔍 [VAULT-SETTING] ✅ Vault system initialized successfully')
          
          // Cache the initialized registry
          await cacheManager.set(cacheKey, initializedRegistry)
          console.log('🔍 [VAULT-SETTING] 💾 Cached initialized registry')
          
          console.log('🔍 [VAULT-SETTING] === GET VAULT REGISTRY END (INITIALIZED) ===')
          return initializedRegistry
        } else {
          console.log('🔍 [VAULT-SETTING] ❌ Failed to initialize vault system')
        }
      }
      
      console.log('🔍 [VAULT-SETTING] === GET VAULT REGISTRY END ===')
      return registry
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error loading vault registry:', error)
      console.log('🔍 [VAULT-SETTING] === GET VAULT REGISTRY END (ERROR) ===')
      return null
    }
  }
  
  /**
   * Validate that cached registry paths still exist
   */
  private async validateCachedRegistry(registry: VaultRegistry): Promise<boolean> {
    try {
      console.log('🔍 [VAULT-SETTING] === VALIDATE CACHED REGISTRY START ===')
      
      if (!registry.vaultRoot) {
        console.log('🔍 [VAULT-SETTING] ❌ Cached registry has no vault root')
        return false
      }
      
      // Check if vault root exists
      if (window.electronAPI?.vault?.pathExists) {
        const rootExists = await window.electronAPI.vault.pathExists(registry.vaultRoot)
        if (!rootExists.exists) {
          console.log('🔍 [VAULT-SETTING] ❌ Cached vault root does not exist:', registry.vaultRoot)
          return false
        }
        console.log('🔍 [VAULT-SETTING] ✅ Cached vault root exists:', registry.vaultRoot)
      }
      
      // Check if vaults exist
      if (registry.vaults && Array.isArray(registry.vaults)) {
        for (const vault of registry.vaults) {
          if (vault.path && window.electronAPI?.vault?.pathExists) {
            const vaultExists = await window.electronAPI.vault.pathExists(vault.path)
            if (!vaultExists.exists) {
              console.log('🔍 [VAULT-SETTING] ❌ Cached vault path does not exist:', vault.path)
              return false
            }
            console.log('🔍 [VAULT-SETTING] ✅ Cached vault path exists:', vault.path)
          }
        }
      }
      
      console.log('🔍 [VAULT-SETTING] ✅ All cached registry paths are valid')
      console.log('🔍 [VAULT-SETTING] === VALIDATE CACHED REGISTRY END ===')
      return true
      
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error validating cached registry:', error)
      console.log('🔍 [VAULT-SETTING] === VALIDATE CACHED REGISTRY END (ERROR) ===')
      return false
    }
  }

  /**
   * Scan all vaults and return updated registry with caching
   */
  async scanVaults(): Promise<VaultRegistry | null> {
    try {
      console.log('🔍 [VAULT-SETTING] === SCAN VAULTS START ===')
      
      // Get current registry (this will auto-initialize if needed)
      const registry = await this.getVaultRegistry()
      if (!registry) {
        console.log('🔍 [VAULT-SETTING] ⚠️ No registry found and initialization failed, cannot scan vaults')
        console.log('🔍 [VAULT-SETTING] === SCAN VAULTS END (NO REGISTRY) ===')
        return null
      }

      console.log('🔍 [VAULT-SETTING] Registry loaded, starting vault scan process')
      console.log('🔍 [VAULT-SETTING] Total vaults to scan:', registry.vaults?.length || 0)
      
      // Discover new vaults that might not be in the registry
      console.log('🔍 [VAULT-SETTING] Discovering new vaults...')
      const newVaults = await this.discoverNewVaults(registry.vaultRoot)
      console.log('🔍 [VAULT-SETTING] New vaults discovered:', newVaults.length)
      
      // Merge new vaults with existing ones
      const allVaults = [...(registry.vaults || []), ...newVaults]
      console.log('🔍 [VAULT-SETTING] Total vaults to scan (including new):', allVaults.length)
      
      // Clear caches to ensure fresh data
      await this.clearCaches()
      console.log('🔍 [VAULT-SETTING] Caches cleared for fresh scan')

      // Scan each vault (including newly discovered ones)
      const updatedVaults: ContextVault[] = []
      if (allVaults && Array.isArray(allVaults)) {
        for (let i = 0; i < allVaults.length; i++) {
          const vault = allVaults[i]
          console.log(`🔍 [VAULT-SETTING] --- Scanning Vault ${i + 1}/${allVaults.length} ---`)
          console.log(`🔍 [VAULT-SETTING] Vault details:`, {
            name: vault.name,
            path: vault.path,
            pathType: typeof vault.path,
            contextsCount: vault.contexts?.length || 0,
            isNew: newVaults.some(nv => nv.path === vault.path)
          })
          
          const updatedVault = await this.scanVault(vault)
          if (updatedVault) {
            updatedVaults.push(updatedVault)
            console.log(`🔍 [VAULT-SETTING] ✅ Vault ${vault.name} scanned successfully`)
          } else {
            console.log(`🔍 [VAULT-SETTING] ❌ Vault ${vault.name} scan failed or returned null`)
          }
        }
      }

      console.log('🔍 [VAULT-SETTING] Vault scanning complete')
      console.log('🔍 [VAULT-SETTING] Successfully scanned vaults:', updatedVaults.length)
      console.log('🔍 [VAULT-SETTING] Failed vaults:', (registry.vaults?.length || 0) - updatedVaults.length)

      // Create updated registry
      const updatedRegistry: VaultRegistry = {
        ...registry,
        vaults: updatedVaults,
        lastScan: new Date().toISOString()
      }

      console.log('🔍 [VAULT-SETTING] Updated registry created with scan timestamp')
      console.log('🔍 [VAULT-SETTING] === SCAN VAULTS END ===')
      
      return updatedRegistry
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error scanning vaults:', error)
      console.log('🔍 [VAULT-SETTING] === SCAN VAULTS END (ERROR) ===')
      return null
    }
  }

  /**
   * Discover new vaults in the vault root directory
   */
  private async discoverNewVaults(vaultRoot: string): Promise<ContextVault[]> {
    try {
      console.log('🔍 [VAULT-SETTING] === DISCOVER NEW VAULTS START ===')
      console.log('🔍 [VAULT-SETTING] Scanning vault root for new vaults:', vaultRoot)
      console.log('🔍 [VAULT-SETTING] Vault root type:', typeof vaultRoot)
      console.log('🔍 [VAULT-SETTING] Vault root length:', vaultRoot?.length)

      // Validate vault root path before proceeding
      if (!vaultRoot || typeof vaultRoot !== 'string' || vaultRoot.trim().length === 0) {
        console.error('🔍 [VAULT-SETTING] ❌ Invalid vault root path:', vaultRoot)
        return []
      }

      // Read the vault root directory
      const dirResult = await window.electronAPI.vault.readDirectory(vaultRoot)
      if (!dirResult.success) {
        console.error('🔍 [VAULT-SETTING] ❌ Failed to read vault root directory:', dirResult.error)
        return []
      }

      // Find potential vault directories (exclude system folders and files)
      const potentialVaults = dirResult.items.filter(item => 
        item.isDirectory && 
        !item.name.startsWith('.') && 
        !item.name.startsWith('_') &&
        !item.name.includes('ChatLo') // Exclude system folders
      )

      console.log('🔍 [VAULT-SETTING] Found potential vault directories:', potentialVaults.map(v => v.name))
      
      const newVaults: ContextVault[] = []
      
      for (const item of potentialVaults) {
        try {
          const vaultPath = await this.joinPath(vaultRoot, item.name)
          console.log('🔍 [VAULT-SETTING] Checking potential vault:', vaultPath)
          
          // Check if this directory contains vault-like structure
          const vaultDirResult = await window.electronAPI.vault.readDirectory(vaultPath)
          if (vaultDirResult.success) {
            // Look for context folders (personal-vault, work-vault, etc.)
            const hasContextFolders = vaultDirResult.items.some(subItem => 
              subItem.isDirectory && 
              (subItem.name.includes('vault') || subItem.name.includes('context'))
            )
            
            if (hasContextFolders) {
              console.log('🔍 [VAULT-SETTING] ✅ Found new vault with context structure:', item.name)
              
              const newVault: ContextVault = {
                id: this.generateStableId(vaultPath),
                name: item.name,
                path: vaultPath,
                type: 'personal', // Default type
                contexts: [],
                lastScan: new Date().toISOString()
              }
              
              newVaults.push(newVault)
            }
          }
        } catch (error) {
          console.warn('🔍 [VAULT-SETTING] ⚠️ Error checking potential vault:', item.name, error)
        }
      }
      
      console.log('🔍 [VAULT-SETTING] Discovered new vaults:', newVaults.length)
      console.log('🔍 [VAULT-SETTING] === DISCOVER NEW VAULTS END ===')
      
      return newVaults
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error discovering new vaults:', error)
      return []
    }
  }

  /**
   * Scan a single vault for updated context data
   */
  private async scanVault(vault: ContextVault): Promise<ContextVault | null> {
    try {
      console.log(`🔍 [VAULT-SETTING] === SCAN VAULT START ===`)
      console.log(`🔍 [VAULT-SETTING] Vault name: ${vault.name}`)
      console.log(`🔍 [VAULT-SETTING] Vault path: ${vault.path}`)
      console.log(`🔍 [VAULT-SETTING] Vault path type: ${typeof vault.path}`)
      console.log(`🔍 [VAULT-SETTING] Vault path length: ${vault.path?.length || 'null/undefined'}`)
      
      // Check if vault path exists
      console.log(`🔍 [VAULT-SETTING] Calling pathExists for: ${vault.path}`)
      const pathCheck = await window.electronAPI.vault.pathExists(vault.path)
      console.log(`🔍 [VAULT-SETTING] PathExists result:`, pathCheck)
      
      if (!pathCheck.exists) {
        console.warn(`🚨 [VAULT-SETTING] Vault path does not exist: ${vault.path}`)
        console.warn(`🚨 [VAULT-SETTING] PathExists error: ${pathCheck.error || 'No error message'}`)
        console.warn(`🚨 [VAULT-SETTING] This suggests a path resolution or security validation issue`)
        return null
      }

      console.log(`✅ [VAULT-SETTING] Vault path exists, proceeding with directory scan`)
      
      // Scan vault directory for contexts
      const dirResult = await window.electronAPI.vault.readDirectory(vault.path)
      if (!dirResult.success) {
        console.error(`❌ [VAULT-SETTING] Failed to read vault directory: ${dirResult.error}`)
        return vault
      }

      // Find context folders (exclude system folders)
      const contextFolders = dirResult.items.filter(item => 
        item.isDirectory && !item.name.startsWith('.') && !item.name.startsWith('_')
      )
      
      console.log(`🔍 [VAULT-SETTING] Found ${contextFolders.length} context folders:`, 
        contextFolders.map(f => ({ name: f.name, path: f.path })))

      // Scan each context folder
      const updatedContexts: ContextFolder[] = []
      for (const folder of contextFolders) {
        const context = await this.scanContext(folder.path, vault.color)
        if (context) {
          updatedContexts.push(context)
        }
      }

      console.log(`✅ [VAULT-SETTING] Successfully scanned ${updatedContexts.length} contexts`)
      console.log(`🔍 [VAULT-SETTING] === SCAN VAULT END ===`)

      return {
        ...vault,
        contexts: updatedContexts,
        lastAccessed: new Date().toISOString()
      }
    } catch (error) {
      console.error(`💥 [VAULT-SETTING] Error scanning vault ${vault.name}:`, error)
      return vault
    }
  }

  /**
   * Scan a context folder for updated data
   */
  private async scanContext(contextPath: string, defaultColor: string): Promise<ContextFolder | null> {
    try {
      // Read context metadata
      const metadataPath = await this.joinPath(contextPath, '.intelligence', 'metadata.json')
      const metadataResult = await window.electronAPI.vault.readFile(metadataPath)
      
      let metadata: any = {}
      if (metadataResult.success && metadataResult.content) {
        try {
          metadata = JSON.parse(metadataResult.content)
          console.log(`✅ [VAULT-SCAN] Valid metadata loaded from ${metadataPath}`)
        } catch (e) {
          console.warn(`❌ [VAULT-SCAN] Invalid metadata JSON in ${metadataPath}:`, e)
          console.warn(`📄 [VAULT-SCAN] Content preview:`, metadataResult.content.substring(0, 200))
          console.warn(`💡 [VAULT-SCAN] This file may need to be regenerated with proper JSON format`)
          // Continue with empty metadata - the context will still be scanned
        }
      } else {
        console.log(`ℹ️ [VAULT-SCAN] No metadata file found at ${metadataPath}`)
      }

      // Scan files in context
      const dirResult = await window.electronAPI.vault.readDirectory(contextPath)
      if (!dirResult.success) {
        return null
      }

      // Count files (exclude system folders)
      const files = dirResult.items.filter(item => 
        !item.name.startsWith('.') && !item.name.startsWith('_')
      )
      const fileCount = files.length

      // Check for master.md
      const masterPath = await this.joinPath(contextPath, 'master.md')
      const masterExists = await window.electronAPI.vault.pathExists(masterPath)
      
      let masterDoc = {
        exists: false,
        path: 'master.md',
        preview: '',
        wordCount: 0,
        lastUpdated: new Date().toISOString()
      }

      if (masterExists.exists) {
        const masterResult = await window.electronAPI.vault.readFile(masterPath)
        if (masterResult.success && masterResult.content) {
          const content = masterResult.content
          masterDoc = {
            exists: true,
            path: 'master.md',
            preview: content.substring(0, 100).replace(/\n/g, ' ') + (content.length > 100 ? '...' : ''),
            wordCount: content.split(/\s+/).length,
            lastUpdated: new Date().toISOString()
          }
        }
      }

      // Determine context status
      let status: 'empty' | 'active' | 'growing' | 'archived' = 'empty'
      if (fileCount > 10) status = 'growing'
      else if (fileCount > 1) status = 'active'

      // Calculate total size
      let totalSize = 0
      for (const file of files) {
        if (file.size) totalSize += file.size
      }

      const contextName = metadata.name || contextPath.split(/[\/\\]/).pop() || 'Unknown Context'
      
      return {
        id: metadata.id || this.generateStableId(contextPath),
        name: contextName,
        path: contextPath,
        description: metadata.description || `Context vault for ${contextName}`,
        color: metadata.color || defaultColor,
        icon: metadata.icon || 'fa-folder',
        status,
        stats: {
          fileCount,
          conversationCount: 0, // TODO: Count linked conversations
          lastModified: new Date().toISOString(),
          sizeBytes: totalSize
        },
        masterDoc,
        aiInsights: {
          suggestedActions: this.generateSuggestions(status, fileCount),
          contextType: metadata.contextType || 'project',
          readinessScore: Math.min(fileCount / 10, 1)
        }
      }
    } catch (error) {
      console.error(`Error scanning context ${contextPath}:`, error)
      return null
    }
  }

  /**
   * Get vault cards with optimized loading
   */
  async getVaultCards(): Promise<ContextVaultCard[]> {
    try {
      // Check cache first for instant loading
      const cardsCacheKey = 'vault_cards'
      const cachedCards = await cacheManager.get<ContextVaultCard[]>(cardsCacheKey)
      
      if (cachedCards) {
        console.log('getVaultCards: ✅ Cache hit - returning cached cards')
        return cachedCards
      }

      console.log('getVaultCards: ❌ Cache miss - scanning vaults...')
      const registry = await this.scanVaults()
      if (!registry) return []

      // Ensure vaults is an array
      if (!registry.vaults || !Array.isArray(registry.vaults)) {
        console.warn('Registry vaults is not an array in getVaultCards:', registry.vaults)
        return []
      }

      const cards: ContextVaultCard[] = []

      for (const vault of registry.vaults) {
        // Ensure contexts is an array
        if (!vault.contexts || !Array.isArray(vault.contexts)) {
          console.warn('Vault contexts is not an array:', vault.contexts)
          continue
        }

        for (const context of vault.contexts) {
          cards.push(this.contextToCard(context))
        }
      }

      // Cache the cards for future use
      await cacheManager.set(cardsCacheKey, cards)
      console.log('getVaultCards: 💾 Cached cards for future use')

      return cards
    } catch (error) {
      console.error('Error getting vault cards:', error)
      return []
    }
  }

  /**
   * Get vault cards with background refresh (for progressive loading)
   */
  async getVaultCardsWithBackgroundRefresh(): Promise<{
    cards: ContextVaultCard[]
    isFromCache: boolean
    backgroundRefreshPromise?: Promise<void>
  }> {
    try {
      // Try to get cached cards first for instant display
      const cardsCacheKey = 'vault_cards'
      const cachedCards = await cacheManager.get<ContextVaultCard[]>(cardsCacheKey)
      
      if (cachedCards) {
        console.log('getVaultCardsWithBackgroundRefresh: ✅ Cache hit - showing cached cards immediately')
        
        // Start background refresh in parallel
        const backgroundRefreshPromise = this.refreshVaultCardsInBackground()
        
        return {
          cards: cachedCards,
          isFromCache: true,
          backgroundRefreshPromise
        }
      }

      // No cache, do full scan
      console.log('getVaultCardsWithBackgroundRefresh: ❌ No cache - doing full scan...')
      const cards = await this.getVaultCards()
      
      return {
        cards,
        isFromCache: false
      }
    } catch (error) {
      console.error('Error in getVaultCardsWithBackgroundRefresh:', error)
      return {
        cards: [],
        isFromCache: false
      }
    }
  }

  /**
   * Refresh vault cards in background without blocking UI
   */
  private async refreshVaultCardsInBackground(): Promise<void> {
    try {
      console.log('refreshVaultCardsInBackground: 🔄 Starting background refresh...')
      
      // Use setTimeout to defer the heavy operation
      setTimeout(async () => {
        try {
          await this.getVaultCards() // This will update the cache
          console.log('refreshVaultCardsInBackground: ✅ Background refresh completed')
        } catch (error) {
          console.warn('refreshVaultCardsInBackground: ⚠️ Background refresh failed:', error)
        }
      }, 100) // Small delay to ensure UI is responsive
      
    } catch (error) {
      console.error('Error starting background refresh:', error)
    }
  }

  /**
   * Convert context folders to vault cards for UI (one card per context)
   */
  private contextToCard(context: ContextFolder): ContextVaultCard {
    return {
      id: context.id,
      name: context.name,
      description: context.description,
      color: context.color,
      icon: context.icon,
      status: context.status,
      fileCount: context.stats.fileCount,
      conversationCount: context.stats.conversationCount,
      lastActivity: this.formatLastActivity(context.stats.lastModified),
      masterPreview: context.masterDoc.preview,
      recentFiles: [], // TODO: Get recent files
      suggestedActions: context.aiInsights.suggestedActions,
      contextType: context.aiInsights.contextType,
      readinessScore: context.aiInsights.readinessScore,
      quickActions: this.getQuickActions(context.status, context.stats.fileCount),
      path: context.path
    }
  }

  /**
   * Convert vault to vault card (one card per vault, files go to /vault/documents)
   */
  private vaultToCard(vault: ContextVault): ContextVaultCard {
    // Aggregate stats from all contexts in the vault
    let totalFileCount = 0
    let totalConversationCount = 0
    let lastModified = vault.created

    if (vault.contexts && Array.isArray(vault.contexts)) {
      for (const context of vault.contexts) {
        totalFileCount += context.stats.fileCount
        totalConversationCount += context.stats.conversationCount
        if (context.stats.lastModified > lastModified) {
          lastModified = context.stats.lastModified
        }
      }
    }

    // Use vault properties, with documents folder as the target
    return {
      id: vault.id,
      name: vault.name,
      description: `Drop files to ${vault.name}/documents`,
      path: vault.path,
      color: vault.color,
      icon: vault.icon,
      status: totalFileCount > 0 ? 'active' : 'empty',
      fileCount: totalFileCount,
      conversationCount: totalConversationCount,
      lastActivity: this.formatLastActivity(lastModified),
      masterPreview: `Files will be saved to ${vault.name}/documents folder`,
      recentFiles: [], // TODO: Get recent files from documents folder
      suggestedActions: ['Drop files here', 'Start a conversation', 'Organize documents'],
      contextType: 'vault',
      readinessScore: Math.min(totalFileCount / 10, 1),
      quickActions: {
        primary: { label: 'Drop Files', action: 'drop' },
        secondary: { label: 'Browse', action: 'browse' }
      }
    }
  }

  /**
   * Get file tree for Files page
   */
  async getFileTree(contextId?: string): Promise<FileTreeNode[]> {
    try {
      console.log('getFileTree: Loading vault registry...')
      const registry = await this.getVaultRegistry()
      console.log('getFileTree: Registry loaded:', registry)
      if (!registry) {
        console.log('getFileTree: No registry found, returning empty array')
        return []
      }

      // If contextId specified, return tree for that context
      if (contextId) {
        return await this.getContextFileTree(contextId, registry)
      }

      // Return tree for all vaults
      const tree: FileTreeNode[] = []

      // Add shared dropbox as first item
      const sharedDropboxNode = await this.buildSharedDropboxTree(registry.vaultRoot)
      if (sharedDropboxNode) {
        tree.push(sharedDropboxNode)
      }

      // Ensure vaults is an array
      if (!registry.vaults || !Array.isArray(registry.vaults)) {
        console.warn('Registry vaults is not an array in getFileTree:', registry.vaults)
        return tree // Return tree with shared dropbox even if no vaults
      }

      for (const vault of registry.vaults) {
        const vaultNode: FileTreeNode = {
          type: 'folder',
          name: vault.name,
          path: vault.path,
          icon: faFolder,
          color: vault.color,
          children: []
        }

        // Ensure contexts is an array
        if (vault.contexts && Array.isArray(vault.contexts)) {
          for (const context of vault.contexts) {
            const contextNode = await this.buildContextTree(context)
            if (contextNode) {
              vaultNode.children!.push(contextNode)
            }
          }
        }

        tree.push(vaultNode)
      }

      return tree
    } catch (error) {
      console.error('Error getting file tree:', error)
      return []
    }
  }

  /**
   * Build shared dropbox tree
   */
  private async buildSharedDropboxTree(vaultRoot: string): Promise<FileTreeNode | null> {
    try {
      const sharedPath = `${vaultRoot}/shared-dropbox`

      // Check if shared dropbox exists
      const pathCheck = await window.electronAPI.vault.pathExists(sharedPath)
      if (!pathCheck.exists) {
        console.log('Shared dropbox does not exist at:', sharedPath)
        return null
      }

      // Read shared dropbox directory
      const dirResult = await window.electronAPI.vault.readDirectory(sharedPath)
      if (!dirResult.success) {
        console.error('Failed to read shared dropbox directory:', dirResult.error)
        return null
      }

      const sharedNode: FileTreeNode = {
        type: 'folder',
        name: '📦 Shared Dropbox',
        path: sharedPath,
        icon: faFolder,
        color: '#FFA500', // Orange color for shared dropbox
        children: []
      }

      // Add files from shared dropbox (exclude metadata files)
      for (const item of dirResult.items) {
        if (item.name.startsWith('.')) continue // Skip metadata files

        const fileNode: FileTreeNode = {
          type: item.isDirectory ? 'folder' : 'file',
          name: item.name,
          path: item.path,
          icon: item.isDirectory ? faFolder : faFile,
          size: item.size,
          modified: item.modified
        }

        // If it's a directory, recursively build its tree
        if (item.isDirectory) {
          const subDirResult = await window.electronAPI.vault.readDirectory(item.path)
          if (subDirResult.success) {
            fileNode.children = []
            for (const subItem of subDirResult.items) {
              fileNode.children.push({
                type: subItem.isDirectory ? 'folder' : 'file',
                name: subItem.name,
                path: subItem.path,
                icon: subItem.isDirectory ? faFolder : faFile,
                size: subItem.size,
                modified: subItem.modified
              })
            }
          }
        }

        sharedNode.children!.push(fileNode)
      }

      return sharedNode
    } catch (error) {
      console.error('Error building shared dropbox tree:', error)
      return null
    }
  }

  /**
   * Build file tree for a context
   */
  private async buildContextTree(context: ContextFolder): Promise<FileTreeNode | null> {
    try {
      const dirResult = await window.electronAPI.vault.readDirectory(context.path)
      if (!dirResult.success) return null

      const contextNode: FileTreeNode = {
        type: 'folder',
        name: context.name,
        path: context.path,
        icon: faFolder,
        color: context.color,
        children: []
      }

      // Add master.md first if it exists
      if (context.masterDoc.exists) {
        contextNode.children!.push({
          type: 'file',
          name: 'master.md',
          path: await this.joinPath(context.path, 'master.md'),
          icon: faFileText,
          color: 'text-primary'
        })
      }

      // Add other files and folders
      for (const item of dirResult.items) {
        if (item.name.startsWith('.') || item.name === 'master.md') continue

        const node: FileTreeNode = {
          type: item.isDirectory ? 'folder' : 'file',
          name: item.name,
          path: item.path,
          icon: this.getFileIcon(item.name, item.isDirectory),
          color: this.getFileColor(item.name, item.isDirectory),
          size: item.size,
          modified: item.modified
        }

        if (item.isDirectory) {
          // TODO: Recursively build folder contents if needed
          node.children = []
        }

        contextNode.children!.push(node)
      }

      return contextNode
    } catch (error) {
      console.error(`Error building context tree for ${context.name}:`, error)
      return null
    }
  }

  /**
   * Get context file tree for specific context
   */
  async getContextFileTree(contextId: string, registry?: VaultRegistry): Promise<FileTreeNode[]> {
    // Load registry if not provided
    if (!registry) {
      registry = await this.getVaultRegistry() || undefined
      if (!registry) return []
    }

    // Handle shared dropbox case (empty contextId)
    if (!contextId || contextId === '') {
      const sharedDropboxNode = await this.buildSharedDropboxTree(registry.vaultRoot)
      return sharedDropboxNode ? [sharedDropboxNode] : []
    }

    // Find the context
    let targetContext: ContextFolder | null = null

    // Ensure vaults is an array
    if (registry.vaults && Array.isArray(registry.vaults)) {
      for (const vault of registry.vaults) {
        // Ensure contexts is an array
        if (vault.contexts && Array.isArray(vault.contexts)) {
          const context = vault.contexts.find(c => c.id === contextId)
          if (context) {
            targetContext = context
            break
          }
        }
      }
    }

    if (!targetContext) return []

    const contextTree = await this.buildContextTree(targetContext)
    return contextTree ? [contextTree] : []
  }

  /**
   * Generate suggestions based on context state
   */
  private generateSuggestions(status: string, _fileCount: number): string[] {
    if (status === 'empty') {
      return [
        '📁 Drop files here to get started',
        '💬 Start a conversation about your goals',
        '🎯 Tell me what you\'re working on'
      ]
    } else if (status === 'active') {
      return [
        '📋 Organize your files into folders',
        '🤖 Ask AI to analyze your content',
        '📝 Update your master document'
      ]
    } else {
      return [
        '🔍 Search across all your files',
        '📊 Generate project insights',
        '🎯 Plan your next steps'
      ]
    }
  }

  /**
   * Get quick actions based on context state
   */
  private getQuickActions(status: string, _fileCount: number): { primary: { label: string; action: string }; secondary: { label: string; action: string } } {
    if (status === 'empty') {
      return {
        primary: { label: 'Add Files', action: 'open-file-picker' },
        secondary: { label: 'Start Chat', action: 'open-chat-with-context' }
      }
    } else {
      return {
        primary: { label: 'Browse Files', action: 'open-files-page' },
        secondary: { label: 'Continue Chat', action: 'open-chat-with-context' }
      }
    }
  }

  /**
   * Format last activity timestamp
   */
  private formatLastActivity(timestamp: string): string {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)

    if (diffHours < 1) return 'Just now'
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
    return date.toLocaleDateString()
  }

  /**
   * Get appropriate icon for file/folder
   */
  private getFileIcon(name: string, isDirectory: boolean) {
    if (isDirectory) {
      if (name === 'documents') return faFileText
      if (name === 'images') return faImage
      if (name === 'artifacts') return faCube
      return faFolder
    }

    const ext = name.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'md':
      case 'txt':
        return faFileText
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return faImage
      default:
        return faFile
    }
  }

  /**
   * Get appropriate color for file/folder
   */
  private getFileColor(name: string, isDirectory: boolean): string {
    if (isDirectory) {
      if (name === 'documents') return 'text-blue-400'
      if (name === 'images') return 'text-green-400'
      if (name === 'artifacts') return 'text-purple-400'
      return 'text-supplement1'
    }

    const ext = name.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'md':
        return 'text-primary'
      case 'txt':
        return 'text-supplement1'
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'text-green-400'
      default:
        return 'text-supplement1'
    }
  }

  /**
   * Initialize vault system if no registry exists
   */
  async initializeVaultSystem(): Promise<VaultRegistry | null> {
    try {
      console.log('🔍 [VAULT-SETTING] === INITIALIZE VAULT SYSTEM START ===')
      
      // Check if we already have a registry
      const existingRegistry = await this.getVaultRegistry()
      if (existingRegistry) {
        console.log('🔍 [VAULT-SETTING] ✅ Vault registry already exists, no initialization needed')
        return existingRegistry
      }
      
      console.log('🔍 [VAULT-SETTING] ⚠️ No vault registry found, initializing new vault system...')
      
      // Check if electronAPI is available
      if (!window.electronAPI?.vault?.initializeVaultRoot) {
        console.warn('🔍 [VAULT-SETTING] Vault API not available - cannot initialize vault system')
        return null
      }
      
      // Get the default vault root path
      const defaultVaultRoot = await this.getDefaultVaultRoot()
      console.log('🔍 [VAULT-SETTING] Default vault root:', defaultVaultRoot)
      
      // Initialize vault root with default template
      const initResult = await window.electronAPI.vault.initializeVaultRoot(defaultVaultRoot, 'default')

      // Handle both APIResponse envelope and direct response for backward compatibility
      const result = initResult.data || initResult
      if (!result.success) {
        console.error('🔍 [VAULT-SETTING] ❌ Failed to initialize vault root:', result.error)
        return null
      }
      
      console.log('🔍 [VAULT-SETTING] ✅ Vault root initialized successfully')
      console.log('🔍 [VAULT-SETTING] Created vaults:', result.vaults?.length || 0)

      // Create a basic registry structure
      const newRegistry: VaultRegistry = {
        version: '1.0',
        vaultRoot: defaultVaultRoot,
        vaults: result.vaults || [],
        lastScan: new Date().toISOString(),
        preferences: {}
      }
      
      // Save the new registry
      if (window.electronAPI.vault.saveVaultRegistry) {
        const saveResult = await window.electronAPI.vault.saveVaultRegistry(newRegistry)
        if (saveResult.success) {
          console.log('🔍 [VAULT-SETTING] ✅ New vault registry saved successfully')
        } else {
          console.warn('🔍 [VAULT-SETTING] ⚠️ Failed to save vault registry:', saveResult.error)
        }
      }
      
      console.log('🔍 [VAULT-SETTING] === INITIALIZE VAULT SYSTEM END ===')
      return newRegistry
      
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error initializing vault system:', error)
      return null
    }
  }
  
  /**
   * Get default vault root path
   */
  private async getDefaultVaultRoot(): Promise<string> {
    try {
      // Try to get from settings first
      if (window.electronAPI?.settings?.get) {
        const savedRoot = await window.electronAPI.settings.get('vault-root-path')
        if (savedRoot) {
          console.log('🔍 [VAULT-SETTING] Using saved vault root from settings:', savedRoot)
          return savedRoot
        }
      }
      
      // Fallback to default path
      const defaultPath = 'C:\\Users\\<USER>\\Documents\\ChatLo_Vaults'
      console.log('🔍 [VAULT-SETTING] Using default vault root path:', defaultPath)
      return defaultPath
      
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] Error getting default vault root:', error)
      // Last resort fallback
      return 'C:\\Users\\<USER>\\Documents\\ChatLo_Vaults'
    }
  }
}

export const vaultUIManager = new VaultUIManager()
