<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatLo Design System v1</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        // ChatLo Brand Colors
                        primary: "#8AB0BB",      // Teal - Main brand color
                        secondary: "#FF8383",    // Coral - Accent/heart color
                        tertiary: "#1B3E68",     // Navy - Deep accent
                        supplement1: "#D5D8E0",  // Light gray - Text/borders
                        supplement2: "#89AFBA",  // Muted teal - Secondary elements
                        
                        // Extended palette for UI components
                        'chatlo-teal': {
                            50: '#f0f9fa',
                            100: '#daf0f3',
                            200: '#b8e1e7',
                            300: '#8AB0BB', // Primary
                            400: '#6b9aa8',
                            500: '#4f7c8a',
                            600: '#3d5f6b',
                            700: '#2d464f',
                            800: '#1e2f35',
                            900: '#0f171a'
                        },
                        'chatlo-coral': {
                            50: '#fff5f5',
                            100: '#ffe3e3',
                            200: '#ffc9c9',
                            300: '#FF8383', // Secondary
                            400: '#ff5555',
                            500: '#e53e3e',
                            600: '#c53030',
                            700: '#9c2626',
                            800: '#742a2a',
                            900: '#4a1414'
                        },
                        'chatlo-navy': {
                            50: '#f7f8fa',
                            100: '#eef1f5',
                            200: '#dde3eb',
                            300: '#c4cdd9',
                            400: '#a6b3c4',
                            500: '#8694a8',
                            600: '#6b7a8f',
                            700: '#556275',
                            800: '#3d4a5c',
                            900: '#1B3E68'  // Tertiary
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'fade-in-up': 'fadeInUp 0.3s ease-out',
                        'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeInUp: {
                            '0%': {
                                opacity: '0',
                                transform: 'translateY(10px)'
                            },
                            '100%': {
                                opacity: '1',
                                transform: 'translateY(0)'
                            }
                        }
                    },
                    backdropBlur: {
                        xs: '2px',
                    }
                }
            }
        };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    
    <style>
        ::-webkit-scrollbar { display: none; }
        body { font-family: 'Inter', sans-serif; }
        .component-preview {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        }
    </style>
</head>

<body class="bg-gray-900 text-white min-h-screen">
    <!-- Header -->
    <header class="bg-gray-800 border-b border-tertiary p-6">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-3xl font-bold text-primary mb-2">ChatLo Design System v1</h1>
            <p class="text-supplement1">Comprehensive UI component library for ChatLo application</p>
        </div>
    </header>

    <div class="max-w-7xl mx-auto p-6 space-y-12">
        
        <!-- Color Palette -->
        <section>
            <h2 class="text-2xl font-semibold mb-6 text-supplement1">Color Palette</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                
                <!-- Primary Colors -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-4 text-supplement1">Primary Colors</h3>
                    <div class="space-y-3">
                        <div class="flex items-center gap-3">
                            <div class="w-12 h-12 rounded-lg bg-primary"></div>
                            <div>
                                <div class="font-medium text-primary">Primary</div>
                                <div class="text-sm text-gray-400">#8AB0BB</div>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-12 h-12 rounded-lg bg-secondary"></div>
                            <div>
                                <div class="font-medium text-secondary">Secondary</div>
                                <div class="text-sm text-gray-400">#FF8383</div>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-12 h-12 rounded-lg bg-tertiary"></div>
                            <div>
                                <div class="font-medium text-supplement1">Tertiary</div>
                                <div class="text-sm text-gray-400">#1B3E68</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Support Colors -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-4 text-supplement1">Support Colors</h3>
                    <div class="space-y-3">
                        <div class="flex items-center gap-3">
                            <div class="w-12 h-12 rounded-lg bg-supplement1"></div>
                            <div>
                                <div class="font-medium text-supplement1">Supplement 1</div>
                                <div class="text-sm text-gray-400">#D5D8E0</div>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-12 h-12 rounded-lg bg-supplement2"></div>
                            <div>
                                <div class="font-medium text-supplement1">Supplement 2</div>
                                <div class="text-sm text-gray-400">#89AFBA</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gray Scale -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-4 text-supplement1">Gray Scale</h3>
                    <div class="space-y-2">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 rounded bg-gray-900"></div>
                            <span class="text-sm">Gray 900</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 rounded bg-gray-800"></div>
                            <span class="text-sm">Gray 800</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 rounded bg-gray-700"></div>
                            <span class="text-sm">Gray 700</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 rounded bg-gray-600"></div>
                            <span class="text-sm">Gray 600</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Typography -->
        <section>
            <h2 class="text-2xl font-semibold mb-6 text-supplement1">Typography</h2>
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="space-y-4">
                    <div>
                        <h1 class="text-4xl font-bold text-supplement1">Heading 1 - Inter Bold</h1>
                        <code class="text-sm text-gray-400">text-4xl font-bold</code>
                    </div>
                    <div>
                        <h2 class="text-3xl font-semibold text-supplement1">Heading 2 - Inter Semibold</h2>
                        <code class="text-sm text-gray-400">text-3xl font-semibold</code>
                    </div>
                    <div>
                        <h3 class="text-2xl font-medium text-supplement1">Heading 3 - Inter Medium</h3>
                        <code class="text-sm text-gray-400">text-2xl font-medium</code>
                    </div>
                    <div>
                        <p class="text-base text-supplement1">Body text - Inter Regular</p>
                        <code class="text-sm text-gray-400">text-base</code>
                    </div>
                    <div>
                        <p class="text-sm text-gray-400">Small text - Inter Regular</p>
                        <code class="text-sm text-gray-400">text-sm text-gray-400</code>
                    </div>
                </div>
            </div>
        </section>

        <!-- Buttons -->
        <section>
            <h2 class="text-2xl font-semibold mb-6 text-supplement1">Buttons (u1-button)</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                
                <!-- Primary Buttons -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-4 text-supplement1">Primary Buttons</h3>
                    <div class="space-y-4">
                        <!-- u1-button-primary -->
                        <button class="u1-button-primary bg-primary hover:bg-primary/80 text-gray-900 font-medium py-3 px-6 rounded-lg transition-colors flex items-center gap-2">
                            <i class="fa-solid fa-plus"></i>
                            Primary Button
                        </button>
                        
                        <!-- u1-button-secondary -->
                        <button class="u1-button-secondary bg-secondary hover:bg-secondary/80 text-white font-medium py-3 px-6 rounded-lg transition-colors">
                            Secondary Button
                        </button>
                        
                        <!-- u1-button-outline -->
                        <button class="u1-button-outline border border-primary text-primary hover:bg-primary hover:text-gray-900 font-medium py-3 px-6 rounded-lg transition-colors">
                            Outline Button
                        </button>
                    </div>
                </div>

                <!-- Icon Buttons -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-4 text-supplement1">Icon Buttons</h3>
                    <div class="space-y-4">
                        <!-- u1-button-icon -->
                        <button class="u1-button-icon w-10 h-10 bg-primary hover:bg-primary/80 text-gray-900 rounded-lg transition-colors flex items-center justify-center">
                            <i class="fa-solid fa-paper-plane"></i>
                        </button>
                        
                        <!-- u1-button-ghost -->
                        <button class="u1-button-ghost hover:bg-gray-700 text-supplement1 p-2 rounded-lg transition-colors">
                            <i class="fa-solid fa-gear"></i>
                        </button>
                        
                        <!-- u1-button-nav -->
                        <button class="u1-button-nav w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                            <i class="fa-solid fa-home"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Input Fields -->
        <section>
            <h2 class="text-2xl font-semibold mb-6 text-supplement1">Input Fields (u1-input)</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                <!-- Text Inputs -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-4 text-supplement1">Text Inputs</h3>
                    <div class="space-y-4">
                        <!-- u1-input-field -->
                        <input type="text" placeholder="Default input field"
                               class="u1-input-field bg-gray-900 border border-gray-700 rounded-lg px-4 py-3 text-sm placeholder-gray-500 focus:ring-2 focus:ring-primary focus:border-transparent outline-none w-full">

                        <!-- u1-input-search -->
                        <div class="relative">
                            <i class="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" placeholder="Search..."
                                   class="u1-input-search bg-gray-900 border border-gray-700 rounded-lg pl-10 pr-4 py-3 text-sm placeholder-gray-500 focus:ring-2 focus:ring-primary focus:border-transparent outline-none w-full">
                        </div>

                        <!-- u1-textarea -->
                        <textarea placeholder="Message input area..." rows="3"
                                  class="u1-textarea bg-gray-900 border border-gray-700 rounded-lg px-4 py-3 text-sm placeholder-gray-500 focus:ring-2 focus:ring-primary focus:border-transparent outline-none w-full resize-none"></textarea>
                    </div>
                </div>

                <!-- Chat Input -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-4 text-supplement1">Chat Input</h3>
                    <!-- u1-chat-input -->
                    <div class="u1-chat-input flex gap-3 items-center">
                        <button class="p-2 text-gray-400 hover:text-supplement1 transition-colors">
                            <i class="fa-solid fa-paperclip"></i>
                        </button>
                        <div class="flex-1 bg-gray-900 rounded-2xl border border-tertiary">
                            <textarea placeholder="Type your message..."
                                      class="w-full bg-transparent px-4 py-3 resize-none focus:outline-none text-supplement1 placeholder-gray-400"
                                      rows="1"></textarea>
                        </div>
                        <button class="p-2 text-gray-400 hover:text-supplement1 transition-colors">
                            <i class="fa-solid fa-sliders"></i>
                        </button>
                        <button class="bg-primary hover:bg-primary/80 text-gray-900 w-10 h-10 rounded-xl transition-colors flex items-center justify-center">
                            <i class="fa-solid fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Cards & Containers -->
        <section>
            <h2 class="text-2xl font-semibold mb-6 text-supplement1">Cards & Containers (u1-card)</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

                <!-- Basic Card -->
                <div class="u1-card bg-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-2 text-supplement1">Basic Card</h3>
                    <p class="text-gray-400 text-sm">Standard card component with rounded corners and padding.</p>
                </div>

                <!-- Chat Bubble User -->
                <div class="u1-chat-bubble-user bg-primary rounded-2xl rounded-tr-md p-4 max-w-lg ml-auto">
                    <p class="text-gray-900">User message bubble with primary color background.</p>
                </div>

                <!-- Chat Bubble Assistant -->
                <div class="u1-chat-bubble-assistant bg-gray-800 rounded-2xl rounded-tl-md p-4 max-w-lg">
                    <p class="text-supplement1">Assistant message bubble with dark background.</p>
                </div>

                <!-- Sidebar Item -->
                <div class="u1-sidebar-item flex gap-3 items-center w-full px-6 py-3 text-sm font-medium hover:bg-gray-700 focus:bg-gray-700 transition-colors rounded-lg cursor-pointer">
                    <i class="fa-solid fa-message-circle text-gray-400"></i>
                    <div class="flex-1 min-w-0">
                        <div class="text-sm font-medium truncate text-supplement1">Conversation Title</div>
                        <div class="text-xs text-gray-500 truncate">Last message preview...</div>
                    </div>
                </div>

                <!-- Status Card -->
                <div class="u1-status-card bg-gray-700/50 rounded-lg p-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <span class="text-sm font-medium text-supplement1">Private Mode</span>
                            <i class="fa-solid fa-info-circle text-gray-400 text-xs"></i>
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="text-xs text-secondary font-medium">ON</span>
                            <button class="relative inline-flex h-6 w-11 items-center rounded-full bg-secondary transition-colors">
                                <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6"></span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Artifact Card -->
                <div class="u1-artifact-card bg-gray-700/50 rounded-lg p-4">
                    <div class="flex items-center gap-2 mb-3">
                        <i class="fa-solid fa-code text-supplement2"></i>
                        <h3 class="font-medium text-supplement1">Code Artifact</h3>
                    </div>
                    <div class="bg-gray-900 rounded p-3 text-sm font-mono">
                        <div class="text-supplement2">const example = () => {</div>
                        <div class="text-gray-400 ml-4">return "Hello World";</div>
                        <div class="text-supplement2">};</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Navigation -->
        <section>
            <h2 class="text-2xl font-semibold mb-6 text-supplement1">Navigation (u1-nav)</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

                <!-- VSCode Style Icon Bar -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-4 text-supplement1">Icon Bar</h3>
                    <div class="u1-nav-iconbar w-12 bg-gray-900 rounded-lg flex flex-col items-center py-2 gap-1">
                        <button class="u1-nav-icon w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative bg-primary/20 border-l-2 border-primary">
                            <i class="fa-solid fa-comment text-primary"></i>
                        </button>
                        <button class="u1-nav-icon w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1">
                            <i class="fa-solid fa-clock-rotate-left"></i>
                        </button>
                        <button class="u1-nav-icon w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1">
                            <i class="fa-solid fa-folder-tree"></i>
                        </button>
                    </div>
                </div>

                <!-- Tab Navigation -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-4 text-supplement1">Tab Navigation</h3>
                    <div class="u1-nav-tabs flex gap-2 border-b border-tertiary/50">
                        <button class="u1-tab-active bg-primary/20 text-primary border border-primary/30 px-3 py-2 rounded-t-lg text-sm font-medium">
                            Active Tab
                        </button>
                        <button class="u1-tab-inactive text-gray-400 hover:text-supplement1 px-3 py-2 rounded-t-lg text-sm font-medium transition-colors">
                            Inactive Tab
                        </button>
                        <button class="u1-tab-inactive text-gray-400 hover:text-supplement1 px-3 py-2 rounded-t-lg text-sm font-medium transition-colors">
                            Another Tab
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Badges & Labels -->
        <section>
            <h2 class="text-2xl font-semibold mb-6 text-supplement1">Badges & Labels (u1-badge)</h2>
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex flex-wrap gap-4">
                    <!-- Status Badges -->
                    <div class="u1-badge-primary bg-primary/20 text-primary border border-primary/30 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                        <i class="fa-solid fa-code text-xs"></i>
                        Code <span class="bg-primary/30 px-1 rounded">3</span>
                    </div>

                    <div class="u1-badge-secondary bg-secondary/20 text-secondary border border-secondary/30 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                        <i class="fa-solid fa-heart text-xs"></i>
                        Favorite
                    </div>

                    <div class="u1-badge-success bg-green-500/20 text-green-400 border border-green-500/30 px-2 py-1 rounded-full text-xs font-medium">
                        Online
                    </div>

                    <div class="u1-badge-warning bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 px-2 py-1 rounded-full text-xs font-medium">
                        Warning
                    </div>

                    <div class="u1-badge-error bg-red-500/20 text-red-400 border border-red-500/30 px-2 py-1 rounded-full text-xs font-medium">
                        Error
                    </div>
                </div>
            </div>
        </section>

        <!-- Usage Guidelines -->
        <section>
            <h2 class="text-2xl font-semibold mb-6 text-supplement1">Usage Guidelines</h2>
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="space-y-4">
                    <div>
                        <h3 class="text-lg font-medium text-supplement1 mb-2">Component Naming Convention</h3>
                        <p class="text-gray-400 mb-2">All components use the <code class="bg-gray-700 px-2 py-1 rounded text-primary">u1-</code> prefix:</p>
                        <ul class="text-gray-400 space-y-1 ml-4">
                            <li>• <code class="text-primary">u1-button-primary</code> - Primary action buttons</li>
                            <li>• <code class="text-primary">u1-input-field</code> - Standard input fields</li>
                            <li>• <code class="text-primary">u1-card</code> - Container components</li>
                            <li>• <code class="text-primary">u1-nav-icon</code> - Navigation elements</li>
                            <li>• <code class="text-primary">u1-badge-primary</code> - Status indicators</li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="text-lg font-medium text-supplement1 mb-2">Color Usage</h3>
                        <ul class="text-gray-400 space-y-1 ml-4">
                            <li>• <span class="text-primary">Primary (#8AB0BB)</span> - Main actions, active states, brand elements</li>
                            <li>• <span class="text-secondary">Secondary (#FF8383)</span> - Accent elements, hearts, warnings</li>
                            <li>• <span class="text-supplement1">Supplement1 (#D5D8E0)</span> - Primary text, borders</li>
                            <li>• <span class="text-supplement2">Supplement2 (#89AFBA)</span> - Secondary text, muted elements</li>
                            <li>• <span style="color: #1B3E68">Tertiary (#1B3E68)</span> - Deep accents, borders</li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="text-lg font-medium text-supplement1 mb-2">Animation & Transitions</h3>
                        <ul class="text-gray-400 space-y-1 ml-4">
                            <li>• Use <code class="text-primary">transition-colors</code> for hover states</li>
                            <li>• Chat bubbles use <code class="text-primary">animate-fade-in-up</code></li>
                            <li>• Loading states use <code class="text-primary">animate-pulse-slow</code></li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

    </div>
</body>
</html>
