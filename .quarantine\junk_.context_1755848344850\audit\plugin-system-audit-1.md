I'll scan the relevant parts of the codebase in parallel to understand the current plugin structure, locate the renderer registry, the electron file processor types, and how `useFileIntelligence` should receive file content from `DocumentViewer`. Then I'll propose concrete fixes if I find gaps.
Ran tool
Ran tool
Ran tool
Ran tool
Ran tool

Okay — I reviewed the Markdown plugin path end-to-end across the renderer and Electron sides, and traced how `FilePageOverlay.tsx`, `FileViewerService.ts`, `DocumentViewer.tsx`, and `IntelligenceHub.tsx` orchestrate. Aha~ the main architectural gaps are around duplicated file-type detection, split extraction flows, and incomplete wiring of plugin outputs into intelligence.

### What’s solid
- Electron-side `MarkdownPlugin` extracts frontmatter and basic markdown metrics safely with size limits.
- Renderer has a FileTypeRenderer plugin registry and a dedicated `MarkdownRenderer` for display.
- `DocumentViewer` exposes hooks (`onContentLoad`, `onContentExtracted`) that can be used to pipe content to the intelligence layer.

### Loopholes and risks

- Duplicate and inconsistent file-type detection
  - Same logic exists in three places with differing unions and no ‘mermaid’ in some.
  - Import collisions: `DocumentViewer` imports `detectFileType` from `./FileTypeRenderer` but also redeclares a local `detectFileType`, shadowing the import.
```5:10:src/components/DocumentViewer.tsx
import { FileTypeRenderer, detectFileType, isMermaidContent } from './FileTypeRenderer' // YOLO: Added plugin system
```
```46:75:src/components/DocumentViewer.tsx
// Comprehensive file type detection
const detectFileType = (fileName: string): FileTypeInfo => {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''
  // ... no mermaid case here ...
}
```
  - `FilePageOverlay` has its own `detectFileType` and its own `isMermaidContent` too.

- Renderer plugins don’t drive extraction; extraction is split and incomplete
  - `DocumentViewer` uses `processFile` only for PDFs and images; markdown is “direct-read”, so Electron `MarkdownPlugin` metadata/frontmatter never reaches intelligence.
  - `FileTypeRenderer` registry currently governs rendering only; there’s no consistent renderer-side “extract” path that bridges to Electron file processors for metadata.

- Intelligence ingestion is not wired to the loader
  - `FilePageOverlay` logs `onContentLoad` and `onContentExtracted` but doesn’t call `updateFileContent` or propagate plugin metadata into `useFileIntelligence`. This prevents consolidated intel regardless of which plugin produced it.

- Two annotation persistence paths
  - `IntelligenceHub` uses `annotationStorageService`.
  - `FilePageOverlay.handleLabelsChanged` merges annotations via `window.electronAPI.invoke('intelligence:listSessions', ...)`.
  - This split can race and diverge data.

- Markdown rendering security and duplication
  - Two separate lightweight regex renderers (`FileTypeRenderer/MarkdownRenderer.tsx` and `artifacts/viewers/MarkdownArtifactViewer.tsx`) using `dangerouslySetInnerHTML` without sanitization; risk of local XSS if rendered content contains raw HTML.
  - Styling/transform rules diverge across both.

- Inconsistent file type models and lack of a single source of truth
  - `FileViewerService` uses `'unknown'`; `DocumentViewer` uses `'unsupported'`; `FilePageOverlay` includes `'mermaid'`. Violates “Interface Consistency” rule and complicates plugin matching.

- Close behavior inconsistencies
  - `IntelligenceHub` uses `window.history.back()` while `FilePageOverlay` uses `onClose()` → possible UX drift.

### Recommendations (high impact, minimal blast radius)

- Single-source file type detection and model
  - Create `src/types/fileTypes.ts` with a single `FileType` union and `FileTypeInfo` interface (include ‘mermaid’).
  - Move detection into `src/services/fileTypeRegistry.ts` (or reuse your `FileTypeRenderer` registry as source-of-truth for types + extension mapping).
  - Replace duplicate implementations in `FileViewerService`, `FilePageOverlay`, and `DocumentViewer` with the shared service. Remove local `detectFileType` shadow.

- Unify extraction via the plugin pathway and consolidate into FilePageOverlay
  - For all types whose Electron file processor plugin exists (pdf, image, markdown), call `window.electronAPI.files.processFile(filePath, ext)` in `DocumentViewer.extractContentForAI` to return processed text + metadata (frontmatter, stats).
  - Pass the result through `onContentExtracted` and then, in `FilePageOverlay`, call:
    - `updateFileContent(extractedText)`
    - `updateIntelligence({ fileMetadata: { ...from plugin metadata... } })`
  - For text/code fallback, still use direct read but normalize metadata structure for intelligence.

- Renderer plugin API parity
  - Extend the renderer plugin registry to allow an optional `extract` bridge that:
    - For file types with Electron processors, delegates to `electronAPI.files.processFile`.
    - For pure text types, returns content.
  - Then `DocumentViewer.extractContentForAI` becomes “call plugin.extract(filePath, content)” rather than switch over file types.

- Annotations: choose one persistence path
  - Prefer `annotationStorageService` everywhere.
  - In `FilePageOverlay.handleLabelsChanged`, drop the `intelligence:listSessions` merge and use the same service as `IntelligenceHub`. This removes race and duplication.

- Markdown rendering hardening and dedupe
  - Keep a single `MarkdownRenderer` component (the one under `FileTypeRenderer`) and have `MarkdownArtifactViewer` import and reuse it.
  - Before regex transforms, escape raw HTML or run a minimal local sanitizer. Keep it offline/local to respect local-first.

- Close behavior consistency
  - Route all close actions through `FilePageOverlay.onClose()` and `fileViewerService.closeFile()`. Replace `window.history.back()` in `IntelligenceHub`.

- Registry visibility for future external plugins
  - Add a simple query API: `fileTypeRegistry.listPlugins()` and surface it in dev logs to ensure plugins are registered at runtime.
  - On Electron side, expose `files.getFileProcessorPlugins()` (already present) to reconcile renderer-visible types with Electron processor availability.

### Small correctness fixes to queue
- In `DocumentViewer.tsx`, remove the local `detectFileType` or rename it and use the centralized one; also actually use the imported `isMermaidContent` when content is markdown to flip to `mermaid`.
- In `FileViewerService.detectFileType`, include `'mermaid'` when content qualifies, or better: accept `fileType` injected from the central detector.
- In `FilePageOverlay`, wire:
  - `onContentLoad={(c) => updateFileContent(c)}`
  - `onContentExtracted={(t, meta) => { updateFileContent(t); updateIntelligence({ fileMetadata: meta }); }}`

### Why this matters
- Ensures any new/external plugin automatically flows into intelligence via one consolidated pathway (your requirement).
- Removes duplication that risks drift and bugs (especially ‘mermaid’ and unknown/unsupported states).
- Preserves local-first and avoids extra dependencies while tightening security and consistency.

Status update:
- I scanned the Electron markdown plugin, the renderer plugin registry, and the three focus components in parallel.
- I identified duplicated type detection, missing wiring from plugin output to intelligence, split annotation persistence, and markdown sanitization gaps.
- I proposed targeted refactors and exact integration points without altering UI.

- Unify file-type detection and types; remove duplicates and add ‘mermaid’.
- Route all extraction through the plugin/electron pathway; in `FilePageOverlay`, update `useFileIntelligence` with extracted text and metadata.
- Use one annotation storage, one markdown renderer with basic sanitization.
- Make close behavior consistent via `onClose`.