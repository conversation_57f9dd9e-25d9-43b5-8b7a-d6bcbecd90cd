import { unifiedAnnotationService } from './unifiedAnnotationService';
import { SmartAnnotationNote } from '../types/fileIntelligenceTypes';

export interface ChatAnnotationData {
  selectedText: string;
  chatMessageId: string;
  conversationId: string;
  userInput?: string;
  filePath?: string;
}

export class ChatAnnotationService {
  private static instance: ChatAnnotationService;
  private listeners: Set<(data: ChatAnnotationData) => void> = new Set();

  static getInstance(): ChatAnnotationService {
    if (!ChatAnnotationService.instance) {
      ChatAnnotationService.instance = new ChatAnnotationService();
    }
    return ChatAnnotationService.instance;
  }

  /**
   * Add selected chat content to annotations
   * If filePath is provided, adds to that file's annotations
   * If no filePath, creates a special "chat-notes" annotation
   * @param data - The chat annotation data
   * @param currentContextId - The currently selected context ID (optional)
   */
  async addChatContentToAnnotation(data: ChatAnnotationData, currentContextId?: string | null): Promise<boolean> {
    try {
      console.log('[CHAT-ANNOTATION] 📝 Adding chat content to annotation:', {
        selectedTextLength: data.selectedText.length,
        chatMessageId: data.chatMessageId,
        conversationId: data.conversationId,
        hasUserInput: !!data.userInput,
        filePath: data.filePath
      });

      // Determine the target for the annotation
      const targetPath = data.filePath || `chat-notes/${data.conversationId}`;
      
      // Create the annotation content
      let annotationContent = `**Chat Content:**\n${data.selectedText}`;
      
      if (data.userInput) {
        annotationContent += `\n\n**User Note:**\n${data.userInput}`;
      }

      // Add metadata about the source
      annotationContent += `\n\n---\n*Source: Chat message ${data.chatMessageId} in conversation ${data.conversationId}*`;

      const newNote: SmartAnnotationNote = {
        id: `chat_note_${Date.now()}`,
        type: 'user',
        content: annotationContent,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        note_number: 1, // Will be updated by the service
        has_ai_response: false,
        metadata: {
          source: 'chat',
          chatMessageId: data.chatMessageId,
          conversationId: data.conversationId,
          originalText: data.selectedText,
          userInput: data.userInput
        }
      };

      // Save the annotation with current context
      const success = await annotationStorageService.saveAnnotation(targetPath, newNote, currentContextId);
      
      if (success) {
        console.log('[CHAT-ANNOTATION] ✅ Successfully added chat content to annotation');
        
        // Notify listeners
        this.notifyListeners(data);
        
        return true;
      } else {
        console.error('[CHAT-ANNOTATION] ❌ Failed to save annotation');
        return false;
      }
    } catch (error) {
      console.error('[CHAT-ANNOTATION] ❌ Error adding chat content to annotation:', error);
      return false;
    }
  }

  /**
   * Subscribe to chat annotation events
   */
  subscribe(listener: (data: ChatAnnotationData) => void): () => void {
    this.listeners.add(listener);
    
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Notify all listeners of new chat annotations
   */
  private notifyListeners(data: ChatAnnotationData): void {
    this.listeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('[CHAT-ANNOTATION] ❌ Error in listener:', error);
      }
    });
  }

  /**
   * Get all chat annotations for a specific conversation
   */
  async getChatAnnotations(conversationId: string): Promise<SmartAnnotationNote[]> {
    try {
      const targetPath = `chat-notes/${conversationId}`;
      return await annotationStorageService.loadAnnotations(targetPath);
    } catch (error) {
      console.error('[CHAT-ANNOTATION] ❌ Error loading chat annotations:', error);
      return [];
    }
  }

  /**
   * Get all chat annotations across all conversations
   */
  async getAllChatAnnotations(): Promise<SmartAnnotationNote[]> {
    try {
      // This would require a more sophisticated search through all annotations
      // For now, return empty array - can be enhanced later
      return [];
    } catch (error) {
      console.error('[CHAT-ANNOTATION] ❌ Error loading all chat annotations:', error);
      return [];
    }
  }
}

export const chatAnnotationService = ChatAnnotationService.getInstance();
