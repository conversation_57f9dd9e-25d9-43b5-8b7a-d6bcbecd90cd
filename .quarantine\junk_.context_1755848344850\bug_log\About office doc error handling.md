Okay, let me analyze your main `DocumentViewer.tsx` component and trace the data flow to identify what's blocking the Office plugin presentation.
Ran tool
Ran tool
Ran tool
Ran tool
Ran tool
Now I can provide you with a complete **Data Flow Story** to nail down which service, variable, or logic is blocking the Office plugin viewer:

## 📊 **Data Flow Story: Office Document Presentation Failure**

### **The Journey of an Office Document** 

By considering the complete data flow from file selection to presentation, here's the story:

### **Stage 1: File Detection & Type Assignment** ✅
```typescript
// src/services/fileTypeRegistry.ts (Lines 188-216)
case 'excel': return { requiresProcessing: true, extractionMethod: 'plugin-based' }
case 'word': return { requiresProcessing: true, extractionMethod: 'plugin-based' }  
case 'powerpoint': return { requiresProcessing: true, extractionMethod: 'plugin-based' }
```
**Status**: Working correctly - Office files are properly detected

### **Stage 2: Content Loading Pipeline** ⚠️ **CRITICAL ISSUE HERE**
```typescript
// src/components/DocumentViewer.tsx (Lines 76-93)
const shouldReadBase64 = ['pdf', 'image', 'excel', 'word', 'powerpoint'].includes(fileType.type)
if (shouldReadBase64 && canReadBase64) {
  const result = await window.electronAPI.vault.readFileBase64(filePath)
  if (result?.success && result.contentBase64) {
    const base64 = result.contentBase64 as string
    setContent(base64) // 🚨 RAW BASE64 PASSED TO RENDERER
  }
}
```

**🔥 ROOT CAUSE IDENTIFIED**: The DocumentViewer loads Office files as **raw base64 strings** and passes them directly to the renderer plugins without any processing.

### **Stage 3: Renderer Plugin Processing** ❌ **FAILURE POINT**
```typescript
// src/components/FileTypeRenderer/ExcelRenderer.tsx (Lines 25-46)
const looksBase64 = /^[A-Za-z0-9+/=\r\n]+$/.test(content) && content.length % 4 === 0;
if (looksBase64) {
  let bin = atob(sample.replace(/\r|\n/g, ''));
  if (bin && header.includes('PK')) {
    setIsBinary(true); // 🚨 BINARY DETECTED - NO PRESENTATION
    setProcessedContent(''); // 🚨 CONTENT CLEARED
    return;
  }
}
```

**The Problem**: All Office renderers (Excel, Word, PowerPoint) detect the base64 content as binary ZIP files (Office docs are ZIP containers) and immediately:
1. Set `isBinary = true`
2. Clear `processedContent = ''`
3. Show "Preview not available" message

### **Stage 4: Missing Content Extraction** ❌ **BROKEN PIPELINE**
```typescript
// src/components/DocumentViewer.tsx (Lines 130-154)
const shouldUseKernel = requiresElectronProcessing(fileType.type) || fileType.requiresProcessing
if (shouldUseKernel && window.electronAPI?.files?.processFile) {
  const result = await window.electronAPI.files.processFile(filePath)
  // 🚨 EXTRACTED TEXT EXISTS BUT NOT USED FOR DISPLAY
  if (result?.success && artifacts?.text) {
    extractedText = artifacts.text // Only used for AI, not viewer
  }
}
```

**The Gap**: The electron-side file processors (WordPlugin, ExcelPlugin, PowerPointPlugin) successfully extract text content, but this extracted text is **only used for AI processing** (`onContentExtracted`) and **NOT for viewer presentation**.

## 🎯 **Precise Blocking Variables & Logic**

### **1. Binary Detection Logic** (Primary Blocker)
**File**: `src/components/FileTypeRenderer/ExcelRenderer.tsx` Lines 25-49
**Variable**: `isBinary` state
**Logic**: `if (header.includes('PK') || nonPrintable > bin.length * 0.05) { setIsBinary(true) }`

### **2. Content Flow Disconnect** (Secondary Blocker)  
**File**: `src/components/DocumentViewer.tsx` Lines 76-118
**Variable**: `content` (contains raw base64)
**Logic**: Base64 content passed to renderer instead of extracted text

### **3. Presentation Logic** (Tertiary Blocker)
**File**: `src/components/FileTypeRenderer/ExcelRenderer.tsx` Lines 143-154
**Logic**: `{!isBinary && processedContent ? <preview> : <"Preview not available">}`

## 🔧 **The Fix Strategy**

The solution requires **connecting the extraction pipeline to the display pipeline**:

1. **Modify DocumentViewer.tsx**: When `extractContentForAI` succeeds, also update the `content` state with extracted text instead of raw base64
2. **Update Office Renderers**: Use extracted text content instead of detecting binary and stopping
3. **Fallback Chain**: Raw base64 → Electron extraction → Text presentation → Binary warning

The Office plugins are working perfectly in the electron layer - the issue is that the **viewer presentation layer ignores the successfully extracted text** and tries to display raw binary content instead.