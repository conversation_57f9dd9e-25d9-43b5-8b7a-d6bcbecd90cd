client:733 [vite] connecting...
client:826 [vite] connected.
react-dom_client.js?v=c55d7f12:17987 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
ServiceLogger.ts:135 ℹ️ 17:10:25 [OpenRouterService] doInitialize: OpenRouter service initialized {baseUrl: undefined, hasApiKey: false}
ServiceLogger.ts:135 ℹ️ 17:10:25 [LocalModelService] doInitialize: Local model service initialized {ollamaUrl: undefined, lmStudioUrl: undefined}
performanceMonitor.ts:385 🖥️ [SYSTEM] Using default hardware profile: 16GB RAM, mid-range tier
performanceMonitor.ts:420 ⚙️ [ADAPTIVE] Set thresholds for mid-range: JS heap 512MB, RAM 80.0%
cacheManager.ts:182 🔥 [CACHE] Loaded cache from storage: 3 warm, 0 cold
ServiceLogger.ts:135 ℹ️ 17:10:25 [CacheManager] doInitialize: Cache manager initialized {config: {…}, hotCacheEntries: 0}
vaultUIManager.ts:76 getVaultRegistry: Checking cache first...
ServiceLogger.ts:135 ℹ️ 17:10:25 [IntelligenceService] doInitialize: Intelligence service initialized {processingVersion: undefined}
ServiceLogger.ts:135 ℹ️ 17:10:25 [VaultFileHandler] VaultFileHandler initialized with streaming support 
ServiceLogger.ts:135 ℹ️ 17:10:25 [FileAnalysisService] doInitialize: File Analysis Service initialized 
ServiceLogger.ts:135 ℹ️ 17:10:25 [StreamingFileProcessor] doInitialize: Streaming file processor initialized {config: undefined}
ServiceLogger.ts:135 ℹ️ 17:10:25 [FileProcessingQueue] doInitialize: File processing queue initialized 
ServiceLogger.ts:135 ℹ️ 17:10:25 [BatchFileProcessingService] doInitialize: Batch File Processing Service initialized 
ServiceLogger.ts:135 ℹ️ 17:10:25 [SmartInstructionService] doInitialize: Smart Instruction service initialized with LLM integration {processingVersion: undefined}
ServiceLogger.ts:135 ℹ️ 17:10:25 [DocumentIntelligenceService] Document Intelligence Service initialized 
index.tsx:58 [FileTypeRenderer] Registered plugin for type: pdf
index.tsx:58 [FileTypeRenderer] Registered plugin for type: markdown
index.tsx:58 [FileTypeRenderer] Registered plugin for type: mermaid
index.tsx:58 [FileTypeRenderer] Registered plugin for type: text
index.tsx:58 [FileTypeRenderer] Registered plugin for type: code
index.tsx:58 [FileTypeRenderer] Registered plugin for type: image
index.tsx:58 [FileTypeRenderer] Registered plugin for type: unsupported
ServiceLogger.ts:135 ℹ️ 17:10:25 [AskAINavigationService] Ask AI Navigation Service initialized 
main.tsx:9 Window electronAPI available: true
main.tsx:11 ElectronAPI methods: (11) ['invoke', 'db', 'settings', 'files', 'shell', 'vault', 'path', 'updater', 'plugins', 'events', 'windowControls']
ServiceLogger.ts:135 ℹ️ 17:10:25 [OpenRouterService] initialization: Operation completed in 28ms: initialization {duration: 28}
ServiceLogger.ts:135 ℹ️ 17:10:25 [OpenRouterService] initialize: Operation completed successfully: initialize {initializationTime: 28}
ServiceLogger.ts:135 ℹ️ 17:10:25 [LocalModelService] initialization: Operation completed in 28ms: initialization {duration: 28}
ServiceLogger.ts:135 ℹ️ 17:10:25 [LocalModelService] initialize: Operation completed successfully: initialize {initializationTime: 28}
performanceMonitor.ts:420 ⚙️ [ADAPTIVE] Set thresholds for undefined: JS heap 512MB, RAM 85.0%
ServiceLogger.ts:135 ℹ️ 17:10:25 [PerformanceMonitor] doInitialize: Performance monitor initialized with adaptive thresholds {systemProfile: null, jsHeapThreshold: '512MB', systemMemoryThreshold: '85.0%', cpuThreshold: 50, processingBudgets: {…}, …}
ServiceLogger.ts:135 ℹ️ 17:10:25 [CacheManager] initialization: Operation completed in 13ms: initialization {duration: 13}
ServiceLogger.ts:135 ℹ️ 17:10:25 [CacheManager] initialize: Operation completed successfully: initialize {initializationTime: 13}
cacheManager.ts:231 🌡️ [CACHE] Warm cache hit: vault_registry
ServiceLogger.ts:135 ℹ️ 17:10:25 [IntelligenceService] initialization: Operation completed in 13ms: initialization {duration: 13}
ServiceLogger.ts:135 ℹ️ 17:10:25 [IntelligenceService] initialize: Operation completed successfully: initialize {initializationTime: 13}
ServiceLogger.ts:135 ℹ️ 17:10:25 [VaultFileHandler] initialization: Operation completed in 12ms: initialization {duration: 12}
ServiceLogger.ts:135 ℹ️ 17:10:25 [VaultFileHandler] initialize: Operation completed successfully: initialize {initializationTime: 12}
ServiceLogger.ts:135 ℹ️ 17:10:25 [FileAnalysisService] initialization: Operation completed in 11ms: initialization {duration: 11}
ServiceLogger.ts:135 ℹ️ 17:10:25 [FileAnalysisService] initialize: Operation completed successfully: initialize {initializationTime: 11}
ServiceLogger.ts:135 ℹ️ 17:10:25 [StreamingFileProcessor] initialization: Operation completed in 11ms: initialization {duration: 11}
ServiceLogger.ts:135 ℹ️ 17:10:25 [StreamingFileProcessor] initialize: Operation completed successfully: initialize {initializationTime: 11}
ServiceLogger.ts:135 ℹ️ 17:10:25 [FileProcessingQueue] initialization: Operation completed in 11ms: initialization {duration: 11}
ServiceLogger.ts:135 ℹ️ 17:10:25 [FileProcessingQueue] initialize: Operation completed successfully: initialize {initializationTime: 11}
ServiceLogger.ts:135 ℹ️ 17:10:25 [BatchFileProcessingService] initialization: Operation completed in 11ms: initialization {duration: 11}
ServiceLogger.ts:135 ℹ️ 17:10:25 [BatchFileProcessingService] initialize: Operation completed successfully: initialize {initializationTime: 11}
ServiceLogger.ts:135 ℹ️ 17:10:25 [SmartInstructionService] initialization: Operation completed in 11ms: initialization {duration: 11}
ServiceLogger.ts:135 ℹ️ 17:10:25 [SmartInstructionService] initialize: Operation completed successfully: initialize {initializationTime: 11}
ServiceLogger.ts:135 ℹ️ 17:10:25 [DocumentIntelligenceService] initialization: Operation completed in 10ms: initialization {duration: 10}
ServiceLogger.ts:135 ℹ️ 17:10:25 [DocumentIntelligenceService] initialize: Operation completed successfully: initialize {initializationTime: 10}
ServiceLogger.ts:135 ℹ️ 17:10:25 [AskAINavigationService] initialization: Operation completed in 8ms: initialization {duration: 8}
ServiceLogger.ts:135 ℹ️ 17:10:25 [AskAINavigationService] initialize: Operation completed successfully: initialize {initializationTime: 8}
ServiceLogger.ts:135 ℹ️ 17:10:25 [PerformanceMonitor] initialization: Operation completed in 17ms: initialization {duration: 17}
ServiceLogger.ts:135 ℹ️ 17:10:25 [PerformanceMonitor] initialize: Operation completed successfully: initialize {initializationTime: 17}
vaultUIManager.ts:83 getVaultRegistry: ✅ Cache hit - returning cached registry
ServiceLogger.ts:135 ℹ️ 17:10:25 [ContextVaultService] loadVaults: Operation completed successfully: loadVaults 
ServiceLogger.ts:135 ℹ️ 17:10:25 [ContextVaultService] loadVaults: Operation completed in 15ms: loadVaults {duration: 15}
ServiceLogger.ts:135 ℹ️ 17:10:25 [ContextVaultService] doInitialize: Context vault service initialized successfully {vaultCount: 2, contextCount: 2, selectedContextId: '000hzs2u6'}
ServiceLogger.ts:135 ℹ️ 17:10:25 [ContextVaultService] initialization: Operation completed in 17ms: initialization {duration: 17}
ServiceLogger.ts:135 ℹ️ 17:10:25 [ContextVaultService] initialize: Operation completed successfully: initialize {initializationTime: 17}
 Rendering right column, viewMode.currentMode: explorer
 Master content length: 0
 Master loading: false
 Hub open: false
 Auto-loading models with saved API key...
 Navigation history updated: {currentIndex: 0, historyLength: 1, currentPath: '/files'}
 FilesPage mounted, loading file tree...
 Loading file tree for context: undefined
 getFileTree: Loading vault registry...
 getVaultRegistry: Checking cache first...
 🔥 [CACHE] Hot cache hit: vault_registry
 Selected context changed to: shared dropbox reloading file tree...
 Shared dropbox selected, switching to Explorer mode
 Loading file tree for context: undefined
 getFileTree: Loading vault registry...
 getVaultRegistry: Checking cache first...
 🔥 [CACHE] Hot cache hit: vault_registry
 File tree updated: 0 items
 Expanded folders: []
 Selected file: null
 Selected folder: null
 Current view mode: explorer
 ℹ️ 17:10:25 [PerformanceMonitor] startMonitoring: Performance monitoring started {interval: '5000ms'}
 Rendering right column, viewMode.currentMode: explorer
 Master content length: 0
 Master loading: false
 Hub open: false
 File tree updated: 0 items
 Expanded folders: []
 Selected file: null
 Selected folder: null
 Current view mode: explorer
 getVaultRegistry: ✅ Cache hit - returning cached registry
 getVaultRegistry: ✅ Cache hit - returning cached registry
 getFileTree: Registry loaded: {version: '1.0', vaultRoot: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3', vaults: Array(2), lastScan: '2025-08-12T05:42:58.931Z', preferences: {…}}
 getFileTree: Registry loaded: {version: '1.0', vaultRoot: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3', vaults: Array(2), lastScan: '2025-08-12T05:42:58.931Z', preferences: {…}}
ServiceLogger.ts:135 ℹ️ 17:10:26 [SharedDropboxService] getVaultRoot: Operation completed successfully: getVaultRoot 
ServiceLogger.ts:135 ℹ️ 17:10:26 [SharedDropboxService] getVaultRoot: Operation completed in 23ms: getVaultRoot {duration: 23}
modelUpdateLogic.ts:215 Loaded manifest from primary URL: /models-manifest.json
index.ts:1181 Using existing models or fetching from OpenRouter...
ServiceLogger.ts:135 ℹ️ 17:10:26 [SharedDropboxService] getVaultRoot: Operation completed successfully: getVaultRoot 
ServiceLogger.ts:135 ℹ️ 17:10:26 [SharedDropboxService] getVaultRoot: Operation completed in 4ms: getVaultRoot {duration: 4}
FilesPage.tsx:436 Raw file tree from vaultUIManager: (3) [{…}, {…}, {…}]
FilesPage.tsx:441 Processing file tree with 3 root nodes
FilesPage.tsx:447 Processing nodes at level:  nodes: 3
FilesPage.tsx:449 Processing node: 📦 Shared Dropbox type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox nodes: 0
FilesPage.tsx:449 Processing node: Personal Vault type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault nodes: 1
FilesPage.tsx:449 Processing node: getting-started type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started nodes: 4
FilesPage.tsx:449 Processing node: master.md type: file path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
FilesPage.tsx:461 Found first master.md at: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
FilesPage.tsx:449 Processing node: artifacts type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts nodes: 0
FilesPage.tsx:449 Processing node: documents type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents nodes: 0
FilesPage.tsx:449 Processing node: images type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images nodes: 0
FilesPage.tsx:449 Processing node: Work Vault type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault nodes: 1
FilesPage.tsx:449 Processing node: project type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project nodes: 4
FilesPage.tsx:449 Processing node: master.md type: file path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\master.md
FilesPage.tsx:463 Found file: master.md at: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\master.md
FilesPage.tsx:449 Processing node: artifacts type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts nodes: 0
FilesPage.tsx:449 Processing node: documents type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents nodes: 0
FilesPage.tsx:449 Processing node: images type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images nodes: 0
FilesPage.tsx:471 Setting expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:475 Setting selected file and switching to master mode: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
FilesPage.tsx:436 Raw file tree from vaultUIManager: (3) [{…}, {…}, {…}]
FilesPage.tsx:441 Processing file tree with 3 root nodes
FilesPage.tsx:447 Processing nodes at level:  nodes: 3
FilesPage.tsx:449 Processing node: 📦 Shared Dropbox type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox nodes: 0
FilesPage.tsx:449 Processing node: Personal Vault type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault nodes: 1
FilesPage.tsx:449 Processing node: getting-started type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started nodes: 4
FilesPage.tsx:449 Processing node: master.md type: file path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
FilesPage.tsx:461 Found first master.md at: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
FilesPage.tsx:449 Processing node: artifacts type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts nodes: 0
FilesPage.tsx:449 Processing node: documents type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents nodes: 0
FilesPage.tsx:449 Processing node: images type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images nodes: 0
FilesPage.tsx:449 Processing node: Work Vault type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault nodes: 1
FilesPage.tsx:449 Processing node: project type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project nodes: 4
FilesPage.tsx:449 Processing node: master.md type: file path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\master.md
FilesPage.tsx:463 Found file: master.md at: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\master.md
FilesPage.tsx:449 Processing node: artifacts type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts nodes: 0
FilesPage.tsx:449 Processing node: documents type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents nodes: 0
FilesPage.tsx:449 Processing node: images type: folder path: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images
FilesPage.tsx:453 Added to expanded paths: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images
FilesPage.tsx:447 Processing nodes at level: C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images nodes: 0
FilesPage.tsx:471 Setting expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:475 Setting selected file and switching to master mode: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
ServiceLogger.ts:135 ℹ️ 17:10:26 [SharedDropboxService] loadFiles: Operation completed successfully: loadFiles 
ServiceLogger.ts:135 ℹ️ 17:10:26 [SharedDropboxService] loadFiles: Operation completed in 13ms: loadFiles {duration: 13}
ServiceLogger.ts:135 ℹ️ 17:10:26 [SharedDropboxService] doInitialize: Shared dropbox initialized successfully {sharedPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\shared-dropbox'}
ServiceLogger.ts:135 ℹ️ 17:10:26 [SharedDropboxService] initialization: Operation completed in 116ms: initialization {duration: 116}
ServiceLogger.ts:135 ℹ️ 17:10:26 [SharedDropboxService] initialize: Operation completed successfully: initialize {initializationTime: 116}
FilesPage.tsx:1427 Rendering right column, viewMode.currentMode: explorer
FilesPage.tsx:1428 Master content length: 0
FilesPage.tsx:1429 Master loading: false
FilesPage.tsx:1430 Hub open: false
FilesPage.tsx:270 File tree updated: 3 items
FilesPage.tsx:271 Expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:272 Selected file: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
FilesPage.tsx:273 Selected folder: null
FilesPage.tsx:274 Current view mode: explorer
FilesPage.tsx:297 Auto-selecting first context folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
FilesPage.tsx:642 Loading files from folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
modelUpdateLogic.ts:215 Loaded manifest from primary URL: /models-manifest.json
modelUpdateLogic.ts:293 Using model manifest v2025.07.16
index.ts:1187 Loaded 318 models
index.ts:1229 Latest models found: (16) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
FilesPage.tsx:1241 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1427 Rendering right column, viewMode.currentMode: explorer
FilesPage.tsx:1428 Master content length: 0
FilesPage.tsx:1429 Master loading: false
FilesPage.tsx:1430 Hub open: false
FilesPage.tsx:270 File tree updated: 3 items
FilesPage.tsx:271 Expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:272 Selected file: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
FilesPage.tsx:273 Selected folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
FilesPage.tsx:274 Current view mode: explorer
index.ts:1304 🔍 Checking local models...
FilesPage.tsx:667 Folder files loaded successfully: 4 items
FilesPage.tsx:1241 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1427 Rendering right column, viewMode.currentMode: explorer
FilesPage.tsx:1428 Master content length: 0
FilesPage.tsx:1429 Master loading: false
FilesPage.tsx:1430 Hub open: false
FilesPage.tsx:481 Switching view mode from explorer to master
FilesPage.tsx:481 Switching view mode from master to master
FilesPage.tsx:1241 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1427 Rendering right column, viewMode.currentMode: master
FilesPage.tsx:1428 Master content length: 0
FilesPage.tsx:1429 Master loading: true
FilesPage.tsx:1430 Hub open: false
FilesPage.tsx:270 File tree updated: 3 items
FilesPage.tsx:271 Expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:272 Selected file: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
FilesPage.tsx:273 Selected folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
FilesPage.tsx:274 Current view mode: master
FilesPage.tsx:1241 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1427 Rendering right column, viewMode.currentMode: master
FilesPage.tsx:1428 Master content length: 1094
FilesPage.tsx:1429 Master loading: false
FilesPage.tsx:1430 Hub open: false
FilesPage.tsx:1241 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1427 Rendering right column, viewMode.currentMode: master
FilesPage.tsx:1428 Master content length: 1094
FilesPage.tsx:1429 Master loading: false
FilesPage.tsx:1430 Hub open: false
ServiceLogger.ts:135 ℹ️ 17:10:26 [LocalModelService] checkOllama: Ollama connected successfully {modelCount: 4, models: Array(4)}
ServiceLogger.ts:135 ℹ️ 17:10:26 [LocalModelService] checkOllama: Operation completed successfully: checkOllama 
ServiceLogger.ts:135 ℹ️ 17:10:26 [LocalModelService] checkOllama: Operation completed in 159ms: checkOllama {duration: 159}
FilesPage.tsx:642 Loading files from folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
FilesPage.tsx:1241 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1427 Rendering right column, viewMode.currentMode: explorer
FilesPage.tsx:1428 Master content length: 1094
FilesPage.tsx:1429 Master loading: false
FilesPage.tsx:1430 Hub open: false
FilesPage.tsx:270 File tree updated: 3 items
FilesPage.tsx:271 Expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:272 Selected file: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\master.md
FilesPage.tsx:273 Selected folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
FilesPage.tsx:274 Current view mode: explorer
FilesPage.tsx:667 Folder files loaded successfully: 3 items
FilesPage.tsx:1241 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1427 Rendering right column, viewMode.currentMode: explorer
FilesPage.tsx:1428 Master content length: 1094
FilesPage.tsx:1429 Master loading: false
FilesPage.tsx:1430 Hub open: false
FilesPage.tsx:1241 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1427 Rendering right column, viewMode.currentMode: explorer
FilesPage.tsx:1428 Master content length: 1094
FilesPage.tsx:1429 Master loading: false
FilesPage.tsx:1430 Hub open: false
localModelService.ts:137  GET http://localhost:1234/v1/models net::ERR_CONNECTION_REFUSED
(anonymous) @ localModelService.ts:137
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
checkLMStudio @ localModelService.ts:132
getProviderStatus @ localModelService.ts:199
checkLocalModels @ index.ts:1307
initializeApp @ index.ts:1375
await in initializeApp
(anonymous) @ main.tsx:18
ServiceLogger.ts:141 ❌ 17:10:28 [LocalModelService] checkLMStudio: Operation failed: checkLMStudio ServiceError: Failed to fetch
    at ServiceError.fromError (ServiceError.ts:114:12)
    at wrapServiceOperation (ServiceError.ts:194:39)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:261:22)
    at async LocalModelService.executeOperation (BaseService.ts:213:12)
    at async LocalModelService.checkLMStudio (localModelService.ts:132:20)
    at async Promise.all (:5173/index 1)
    at async LocalModelService.getProviderStatus (localModelService.ts:197:44)
    at async Object.checkLocalModels (index.ts:1307:30)
    at async initializeApp (index.ts:1375:5)
outputToConsole @ ServiceLogger.ts:141
log @ ServiceLogger.ts:112
error @ ServiceLogger.ts:63
operationFailure @ ServiceLogger.ts:84
(anonymous) @ BaseService.ts:219
await in (anonymous)
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
checkLMStudio @ localModelService.ts:132
getProviderStatus @ localModelService.ts:199
checkLocalModels @ index.ts:1307
initializeApp @ index.ts:1375
await in initializeApp
(anonymous) @ main.tsx:18
ServiceLogger.ts:143 ServiceError: Failed to fetch
    at ServiceError.fromError (ServiceError.ts:114:12)
    at wrapServiceOperation (ServiceError.ts:194:39)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:261:22)
    at async LocalModelService.executeOperation (BaseService.ts:213:12)
    at async LocalModelService.checkLMStudio (localModelService.ts:132:20)
    at async Promise.all (:5173/index 1)
    at async LocalModelService.getProviderStatus (localModelService.ts:197:44)
    at async Object.checkLocalModels (index.ts:1307:30)
    at async initializeApp (index.ts:1375:5)
outputToConsole @ ServiceLogger.ts:143
log @ ServiceLogger.ts:112
error @ ServiceLogger.ts:63
operationFailure @ ServiceLogger.ts:84
(anonymous) @ BaseService.ts:219
await in (anonymous)
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
checkLMStudio @ localModelService.ts:132
getProviderStatus @ localModelService.ts:199
checkLocalModels @ index.ts:1307
initializeApp @ index.ts:1375
await in initializeApp
(anonymous) @ main.tsx:18
ServiceLogger.ts:135 ℹ️ 17:10:28 [LocalModelService] checkLMStudio: Operation completed in 2317ms: checkLMStudio {duration: 2317}
ServiceLogger.ts:138 ⚠️ 17:10:28 [LocalModelService] checkLMStudio: LM Studio not available ServiceError: Failed to fetch
    at ServiceError.fromError (ServiceError.ts:114:12)
    at wrapServiceOperation (ServiceError.ts:194:39)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:261:22)
    at async LocalModelService.executeOperation (BaseService.ts:213:12)
    at async LocalModelService.checkLMStudio (localModelService.ts:132:20)
    at async Promise.all (:5173/index 1)
    at async LocalModelService.getProviderStatus (localModelService.ts:197:44)
    at async Object.checkLocalModels (index.ts:1307:30)
    at async initializeApp (index.ts:1375:5)
outputToConsole @ ServiceLogger.ts:138
log @ ServiceLogger.ts:112
warn @ ServiceLogger.ts:56
checkLMStudio @ localModelService.ts:175
await in checkLMStudio
getProviderStatus @ localModelService.ts:199
checkLocalModels @ index.ts:1307
initializeApp @ index.ts:1375
await in initializeApp
(anonymous) @ main.tsx:18
ServiceLogger.ts:135 ℹ️ 17:10:28 [LocalModelService] checkOllama: Ollama connected successfully {modelCount: 4, models: Array(4)}
ServiceLogger.ts:135 ℹ️ 17:10:28 [LocalModelService] checkOllama: Operation completed successfully: checkOllama 
ServiceLogger.ts:135 ℹ️ 17:10:28 [LocalModelService] checkOllama: Operation completed in 8ms: checkOllama {duration: 8}
localModelService.ts:137  GET http://localhost:1234/v1/models net::ERR_CONNECTION_REFUSED
(anonymous) @ localModelService.ts:137
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
checkLMStudio @ localModelService.ts:132
getAllLocalModels @ localModelService.ts:186
checkLocalModels @ index.ts:1308
await in checkLocalModels
initializeApp @ index.ts:1375
await in initializeApp
(anonymous) @ main.tsx:18
ServiceLogger.ts:141 ❌ 17:10:28 [LocalModelService] checkLMStudio: Operation failed: checkLMStudio ServiceError: Failed to fetch
    at ServiceError.fromError (ServiceError.ts:114:12)
    at wrapServiceOperation (ServiceError.ts:194:39)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:261:22)
    at async LocalModelService.executeOperation (BaseService.ts:213:12)
    at async LocalModelService.checkLMStudio (localModelService.ts:132:20)
    at async Promise.all (:5173/index 1)
    at async LocalModelService.getAllLocalModels (localModelService.ts:184:44)
    at async Object.checkLocalModels (index.ts:1308:25)
    at async initializeApp (index.ts:1375:5)
outputToConsole @ ServiceLogger.ts:141
log @ ServiceLogger.ts:112
error @ ServiceLogger.ts:63
operationFailure @ ServiceLogger.ts:84
(anonymous) @ BaseService.ts:219
await in (anonymous)
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
checkLMStudio @ localModelService.ts:132
getAllLocalModels @ localModelService.ts:186
checkLocalModels @ index.ts:1308
await in checkLocalModels
initializeApp @ index.ts:1375
await in initializeApp
(anonymous) @ main.tsx:18
ServiceLogger.ts:143 ServiceError: Failed to fetch
    at ServiceError.fromError (ServiceError.ts:114:12)
    at wrapServiceOperation (ServiceError.ts:194:39)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:261:22)
    at async LocalModelService.executeOperation (BaseService.ts:213:12)
    at async LocalModelService.checkLMStudio (localModelService.ts:132:20)
    at async Promise.all (:5173/index 1)
    at async LocalModelService.getAllLocalModels (localModelService.ts:184:44)
    at async Object.checkLocalModels (index.ts:1308:25)
    at async initializeApp (index.ts:1375:5)
outputToConsole @ ServiceLogger.ts:143
log @ ServiceLogger.ts:112
error @ ServiceLogger.ts:63
operationFailure @ ServiceLogger.ts:84
(anonymous) @ BaseService.ts:219
await in (anonymous)
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
checkLMStudio @ localModelService.ts:132
getAllLocalModels @ localModelService.ts:186
checkLocalModels @ index.ts:1308
await in checkLocalModels
initializeApp @ index.ts:1375
await in initializeApp
(anonymous) @ main.tsx:18
ServiceLogger.ts:135 ℹ️ 17:10:28 [LocalModelService] checkLMStudio: Operation completed in 291ms: checkLMStudio {duration: 291}
ServiceLogger.ts:138 ⚠️ 17:10:28 [LocalModelService] checkLMStudio: LM Studio not available ServiceError: Failed to fetch
    at ServiceError.fromError (ServiceError.ts:114:12)
    at wrapServiceOperation (ServiceError.ts:194:39)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:261:22)
    at async LocalModelService.executeOperation (BaseService.ts:213:12)
    at async LocalModelService.checkLMStudio (localModelService.ts:132:20)
    at async Promise.all (:5173/index 1)
    at async LocalModelService.getAllLocalModels (localModelService.ts:184:44)
    at async Object.checkLocalModels (index.ts:1308:25)
    at async initializeApp (index.ts:1375:5)
outputToConsole @ ServiceLogger.ts:138
log @ ServiceLogger.ts:112
warn @ ServiceLogger.ts:56
checkLMStudio @ localModelService.ts:175
await in checkLMStudio
getAllLocalModels @ localModelService.ts:186
checkLocalModels @ index.ts:1308
await in checkLocalModels
initializeApp @ index.ts:1375
await in initializeApp
(anonymous) @ main.tsx:18
index.ts:1310 📊 Provider Status: {ollama: {…}, lmstudio: {…}}
index.ts:1314 🤖 All Local Models: (4) [{…}, {…}, {…}, {…}]
index.ts:1324 ✅ Local model check complete. Found 4 local models. Total external models: 318
FilesPage.tsx:1241 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1427 Rendering right column, viewMode.currentMode: explorer
FilesPage.tsx:1428 Master content length: 1094
FilesPage.tsx:1429 Master loading: false
FilesPage.tsx:1430 Hub open: false
FileViewerService.ts:43 FileViewerService: Opening file: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileName: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileType: undefined, isEditMode: false}
FilesPage.tsx:1241 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1427 Rendering right column, viewMode.currentMode: explorer
FilesPage.tsx:1428 Master content length: 1094
FilesPage.tsx:1429 Master loading: false
FilesPage.tsx:1430 Hub open: false
FilePageOverlay.tsx:105 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 0, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
DocumentViewer.tsx:165 DocumentViewer: Loading file content for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md Type: markdown
IntelligenceHub.tsx:80 [ANNOTATIONS] 📖 Loading annotations for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:19 [ANNOTATIONS] 📖 Loading annotations for: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started'}
FilePageOverlay.tsx:990 [ANNOTATIONS] 🔍 OVERLAY: useEffect triggered {isOpen: true, filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileName: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md'}
FilePageOverlay.tsx:999 🔄 [OVERLAY] Loading file content for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md Type: markdown
FilePageOverlay.tsx:704 Loading file content for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md Type: markdown
FilePageOverlay.tsx:1004 [LABELS] 🔄 OVERLAY: File opened, SmartLabelingInterface will manage intelligence state
FilePageOverlay.tsx:1008 [LABELS] 🔄 OVERLAY: Loading states reset, intelligence state preserved
FilePageOverlay.tsx:1011 [ANNOTATIONS] 🚀 OVERLAY: About to load annotations for file: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:19 [ANNOTATIONS] 📖 Loading annotations for: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started'}
FilesPage.tsx:270 File tree updated: 3 items
FilesPage.tsx:271 Expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:272 Selected file: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
FilesPage.tsx:273 Selected folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
FilesPage.tsx:274 Current view mode: explorer
FilePageOverlay.tsx:105 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 0, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
DocumentViewer.tsx:170 DocumentViewer: File read result: {success: true, content: '# ChatLo Design System Index\n\n## Overview\nThis doc…d maintain backward compatibility where possible\n'}
FilePageOverlay.tsx:1057 Content loaded: 6004 characters
DocumentViewer.tsx:175 DocumentViewer: Content loaded successfully, length: 6004
FilePageOverlay.tsx:575 File getFileContent result: success 8016
FilePageOverlay.tsx:596 🔄 [OVERLAY] Set fileContent for text file: 6004 characters
annotationStorageService.ts:27 [ANNOTATIONS] 🔍 DEBUG: Intelligence data structure: {hasKeyIdeas: true, keyIdeasCount: 1, hasSmartAnnotations: true, smartAnnotationsCount: 1, keys: Array(16)}
annotationStorageService.ts:67 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:89 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
annotationStorageService.ts:27 [ANNOTATIONS] 🔍 DEBUG: Intelligence data structure: {hasKeyIdeas: true, keyIdeasCount: 1, hasSmartAnnotations: true, smartAnnotationsCount: 1, keys: Array(16)}
annotationStorageService.ts:67 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
FilePageOverlay.tsx:105 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:55 [LABELS] 🔄 SmartLabelingInterface: useEffect triggered
SmartLabelingInterface.tsx:56 [LABELS] 🔄 SmartLabelingInterface: filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
SmartLabelingInterface.tsx:57 [LABELS] 🔄 SmartLabelingInterface: fileContent length: 6004
SmartLabelingInterface.tsx:58 [LABELS] 🔄 SmartLabelingInterface: onLabelsChanged callback: true
SmartLabelingInterface.tsx:59 [LABELS] 🔄 SmartLabelingInterface: Starting intelligence loading process (persisted first)
SmartLabelingInterface.tsx:146 [LABELS] 🔍 KERNEL-STATUS: Checking kernel availability
SmartLabelingInterface.tsx:188 [LABELS] 🔍 KERNEL-STATUS: Checking if kernel has intelligence for file
SmartLabelingInterface.tsx:189 [LABELS] 🔍 KERNEL-STATUS: filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
SmartLabelingInterface.tsx:323 [LABELS] 🔄 SmartLabelingInterface: loadOrProcessFileIntelligence called {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004}
SmartLabelingInterface.tsx:330 [LABELS] 🔄 KERNEL: Querying kernel for existing intelligence
SmartLabelingInterface.tsx:284 [LABELS] 🔍 KERNEL: Querying for existing intelligence: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
SmartLabelingInterface.tsx:202 [LABELS] 🔍 KERNEL-STATUS: Kernel response: {success: true, hasData: true, ideasCount: 1, hasIntelligence: true}
SmartLabelingInterface.tsx:162 [LABELS] 🔍 KERNEL-STATUS: Kernel status: {available: true, hasAnalyze: true, hasGet: true, hasIntelligence: true, electronAPI: true, …}
SmartLabelingInterface.tsx:173 [LABELS] 🔍 KERNEL-STATUS: Kernel available and has existing intelligence
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
SmartLabelingInterface.tsx:295 [LABELS] 🔍 KERNEL: Using contextPath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
SmartLabelingInterface.tsx:301 [LABELS] 🔍 KERNEL: ✅ Kernel returned intelligence with 1 ideas
SmartLabelingInterface.tsx:334 [LABELS] ✅ KERNEL: Found existing intelligence with 1 ideas
SmartLabelingInterface.tsx:351 [FILE-INTEL] Loaded existing intelligence from kernel: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', ideasCount: 1, entitiesCount: 0}
FilePageOverlay.tsx:321 [LABELS] 🎯 OVERLAY: handleLabelsChanged called with 1 ideas
FilePageOverlay.tsx:322 [LABELS] 🎯 OVERLAY: Received ideas: [{…}]
FilePageOverlay.tsx:329 [LABELS] 🎯 OVERLAY: Current fileIntelligence state: null
FilePageOverlay.tsx:348 [LABELS] 🎯 OVERLAY: Setting fileIntelligence with 1 ideas
FilePageOverlay.tsx:365 [LABELS] 🎯 OVERLAY: ✅ Updated fileIntelligence from SmartLabelingInterface: {totalIdeas: 1, selectedIdeas: 1, fileIntelligenceSet: true, hasIdeas: true}
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
FilesPage.tsx:1241 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1241 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
FilesPage.tsx:1242 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
FilesPage.tsx:1427 Rendering right column, viewMode.currentMode: explorer
FilesPage.tsx:1428 Master content length: 1094
FilesPage.tsx:1429 Master loading: false
FilesPage.tsx:1430 Hub open: false
FilePageOverlay.tsx:409 ✅ [INTELLIGENCE] Updated UI state (persistence handled by SmartLabelingInterface)
FilePageOverlay.tsx:105 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
FilePageOverlay.tsx:105 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:454 [LABELS] 🤖 processFileIntelligence: Starting local LLM analysis
SmartLabelingInterface.tsx:455 [LABELS] 🤖 filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
SmartLabelingInterface.tsx:456 [LABELS] 🤖 fileContent length: 6004
SmartLabelingInterface.tsx:469 [LABELS] 🤖 LOCAL-LLM: Checking for available local models
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
ServiceLogger.ts:135 ℹ️ 17:10:32 [LocalModelService] checkOllama: Ollama connected successfully {modelCount: 4, models: Array(4)}
ServiceLogger.ts:135 ℹ️ 17:10:32 [LocalModelService] checkOllama: Operation completed successfully: checkOllama 
ServiceLogger.ts:135 ℹ️ 17:10:32 [LocalModelService] checkOllama: Operation completed in 13ms: checkOllama {duration: 13}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
localModelService.ts:137  GET http://localhost:1234/v1/models net::ERR_CONNECTION_REFUSED
(anonymous) @ localModelService.ts:137
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
checkLMStudio @ localModelService.ts:132
getProviderStatus @ localModelService.ts:199
processFileIntelligence @ SmartLabelingInterface.tsx:471
onClick @ SmartLabelingInterface.tsx:781
executeDispatch @ react-dom_client.js?v=c55d7f12:11736
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
processDispatchQueue @ react-dom_client.js?v=c55d7f12:11772
(anonymous) @ react-dom_client.js?v=c55d7f12:12182
batchedUpdates$1 @ react-dom_client.js?v=c55d7f12:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=c55d7f12:11877
dispatchEvent @ react-dom_client.js?v=c55d7f12:14792
dispatchDiscreteEvent @ react-dom_client.js?v=c55d7f12:14773
<button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
SmartLabelingInterface @ SmartLabelingInterface.tsx:780
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=c55d7f12:11623
performWorkUntilDeadline @ react-dom_client.js?v=c55d7f12:36
<SmartLabelingInterface>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
IntelligenceHub @ IntelligenceHub.tsx:389
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<IntelligenceHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilePageOverlay @ FilePageOverlay.tsx:1074
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<FilePageOverlay>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilesPage @ FilesPage.tsx:1506
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<FilesPage>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
App @ App.tsx:195
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=c55d7f12:11623
performWorkUntilDeadline @ react-dom_client.js?v=c55d7f12:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
(anonymous) @ main.tsx:23
ServiceLogger.ts:141 ❌ 17:10:35 [LocalModelService] checkLMStudio: Operation failed: checkLMStudio ServiceError: Failed to fetch
    at ServiceError.fromError (ServiceError.ts:114:12)
    at wrapServiceOperation (ServiceError.ts:194:39)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:261:22)
    at async LocalModelService.executeOperation (BaseService.ts:213:12)
    at async LocalModelService.checkLMStudio (localModelService.ts:132:20)
    at async Promise.all (:5173/index 1)
    at async LocalModelService.getProviderStatus (localModelService.ts:197:44)
    at async processFileIntelligence (SmartLabelingInterface.tsx:471:30)
outputToConsole @ ServiceLogger.ts:141
log @ ServiceLogger.ts:112
error @ ServiceLogger.ts:63
operationFailure @ ServiceLogger.ts:84
(anonymous) @ BaseService.ts:219
await in (anonymous)
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
checkLMStudio @ localModelService.ts:132
getProviderStatus @ localModelService.ts:199
processFileIntelligence @ SmartLabelingInterface.tsx:471
onClick @ SmartLabelingInterface.tsx:781
executeDispatch @ react-dom_client.js?v=c55d7f12:11736
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
processDispatchQueue @ react-dom_client.js?v=c55d7f12:11772
(anonymous) @ react-dom_client.js?v=c55d7f12:12182
batchedUpdates$1 @ react-dom_client.js?v=c55d7f12:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=c55d7f12:11877
dispatchEvent @ react-dom_client.js?v=c55d7f12:14792
dispatchDiscreteEvent @ react-dom_client.js?v=c55d7f12:14773
<button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
SmartLabelingInterface @ SmartLabelingInterface.tsx:780
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=c55d7f12:11623
performWorkUntilDeadline @ react-dom_client.js?v=c55d7f12:36
<SmartLabelingInterface>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
IntelligenceHub @ IntelligenceHub.tsx:389
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<IntelligenceHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilePageOverlay @ FilePageOverlay.tsx:1074
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<FilePageOverlay>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilesPage @ FilesPage.tsx:1506
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<FilesPage>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
App @ App.tsx:195
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=c55d7f12:11623
performWorkUntilDeadline @ react-dom_client.js?v=c55d7f12:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
(anonymous) @ main.tsx:23
ServiceLogger.ts:143 ServiceError: Failed to fetch
    at ServiceError.fromError (ServiceError.ts:114:12)
    at wrapServiceOperation (ServiceError.ts:194:39)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:261:22)
    at async LocalModelService.executeOperation (BaseService.ts:213:12)
    at async LocalModelService.checkLMStudio (localModelService.ts:132:20)
    at async Promise.all (:5173/index 1)
    at async LocalModelService.getProviderStatus (localModelService.ts:197:44)
    at async processFileIntelligence (SmartLabelingInterface.tsx:471:30)
outputToConsole @ ServiceLogger.ts:143
log @ ServiceLogger.ts:112
error @ ServiceLogger.ts:63
operationFailure @ ServiceLogger.ts:84
(anonymous) @ BaseService.ts:219
await in (anonymous)
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
checkLMStudio @ localModelService.ts:132
getProviderStatus @ localModelService.ts:199
processFileIntelligence @ SmartLabelingInterface.tsx:471
onClick @ SmartLabelingInterface.tsx:781
executeDispatch @ react-dom_client.js?v=c55d7f12:11736
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
processDispatchQueue @ react-dom_client.js?v=c55d7f12:11772
(anonymous) @ react-dom_client.js?v=c55d7f12:12182
batchedUpdates$1 @ react-dom_client.js?v=c55d7f12:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=c55d7f12:11877
dispatchEvent @ react-dom_client.js?v=c55d7f12:14792
dispatchDiscreteEvent @ react-dom_client.js?v=c55d7f12:14773
<button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
SmartLabelingInterface @ SmartLabelingInterface.tsx:780
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=c55d7f12:11623
performWorkUntilDeadline @ react-dom_client.js?v=c55d7f12:36
<SmartLabelingInterface>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
IntelligenceHub @ IntelligenceHub.tsx:389
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<IntelligenceHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilePageOverlay @ FilePageOverlay.tsx:1074
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<FilePageOverlay>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilesPage @ FilesPage.tsx:1506
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<FilesPage>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
App @ App.tsx:195
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=c55d7f12:11623
performWorkUntilDeadline @ react-dom_client.js?v=c55d7f12:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
(anonymous) @ main.tsx:23
ServiceLogger.ts:135 ℹ️ 17:10:35 [LocalModelService] checkLMStudio: Operation completed in 2332ms: checkLMStudio {duration: 2332}
ServiceLogger.ts:138 ⚠️ 17:10:35 [LocalModelService] checkLMStudio: LM Studio not available ServiceError: Failed to fetch
    at ServiceError.fromError (ServiceError.ts:114:12)
    at wrapServiceOperation (ServiceError.ts:194:39)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:261:22)
    at async LocalModelService.executeOperation (BaseService.ts:213:12)
    at async LocalModelService.checkLMStudio (localModelService.ts:132:20)
    at async Promise.all (:5173/index 1)
    at async LocalModelService.getProviderStatus (localModelService.ts:197:44)
    at async processFileIntelligence (SmartLabelingInterface.tsx:471:30)
outputToConsole @ ServiceLogger.ts:138
log @ ServiceLogger.ts:112
warn @ ServiceLogger.ts:56
checkLMStudio @ localModelService.ts:175
await in checkLMStudio
getProviderStatus @ localModelService.ts:199
processFileIntelligence @ SmartLabelingInterface.tsx:471
onClick @ SmartLabelingInterface.tsx:781
executeDispatch @ react-dom_client.js?v=c55d7f12:11736
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
processDispatchQueue @ react-dom_client.js?v=c55d7f12:11772
(anonymous) @ react-dom_client.js?v=c55d7f12:12182
batchedUpdates$1 @ react-dom_client.js?v=c55d7f12:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=c55d7f12:11877
dispatchEvent @ react-dom_client.js?v=c55d7f12:14792
dispatchDiscreteEvent @ react-dom_client.js?v=c55d7f12:14773
<button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
SmartLabelingInterface @ SmartLabelingInterface.tsx:780
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=c55d7f12:11623
performWorkUntilDeadline @ react-dom_client.js?v=c55d7f12:36
<SmartLabelingInterface>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
IntelligenceHub @ IntelligenceHub.tsx:389
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<IntelligenceHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilePageOverlay @ FilePageOverlay.tsx:1074
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<FilePageOverlay>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilesPage @ FilesPage.tsx:1506
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<FilesPage>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
App @ App.tsx:195
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=c55d7f12:11623
performWorkUntilDeadline @ react-dom_client.js?v=c55d7f12:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
(anonymous) @ main.tsx:23
SmartLabelingInterface.tsx:472 [LABELS] 🤖 Provider Status: {ollama: {…}, lmstudio: {…}}
SmartLabelingInterface.tsx:497 [LABELS] 🤖 LOCAL-LLM: Using model: gemma3:4b
SmartLabelingInterface.tsx:503 [LABELS] 🤖 📡 Sending request to local model: ollama:gemma3:4b
SmartLabelingInterface.tsx:504 [LABELS] 🤖 📡 Prompt preview: You are an expert document analyzer. Extract key ideas and labels from the following document content.

REQUIREMENTS:
- Extract at least 1 meaningful label/key idea (minimum requirement)
- Focus on the most important topics, themes, and concepts
- Each label should be 1-4 words maximum
- Labels shou...
ServiceLogger.ts:135 ℹ️ 17:10:35 [LocalModelService] sendOllamaMessage: Sending message to Ollama {model: 'gemma3', messageCount: 1, streaming: false}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
ServiceLogger.ts:135 ℹ️ 17:10:40 [LocalModelService] sendOllamaMessage: Ollama response received {model: 'gemma3', responseLength: 2}
ServiceLogger.ts:135 ℹ️ 17:10:40 [LocalModelService] sendMessage: Operation completed successfully: sendMessage {modelId: 'ollama:gemma3:4b', messageCount: 1, hasChunkCallback: false}
ServiceLogger.ts:135 ℹ️ 17:10:40 [LocalModelService] sendMessage: Operation completed in 5017ms: sendMessage {duration: 5017}
SmartLabelingInterface.tsx:519 [LABELS] 🤖 📡 Raw response received: {}...
SmartLabelingInterface.tsx:970 [LABELS] 🤖 📝 Parsing LLM response...
SmartLabelingInterface.tsx:989 [LABELS] 🤖 📝 Attempting to parse JSON: {}...
SmartLabelingInterface.tsx:1013 [LABELS] 🤖 📝 Using fallback text extraction...
SmartLabelingInterface.tsx:1086 [LABELS] 🤖 📝 ✅ Fallback extraction created 1 labels
SmartLabelingInterface.tsx:531 [LABELS] 🤖 LOCAL-LLM: ✅ Analysis successful, extracted 1 ideas
FilePageOverlay.tsx:321 [LABELS] 🎯 OVERLAY: handleLabelsChanged called with 1 ideas
FilePageOverlay.tsx:322 [LABELS] 🎯 OVERLAY: Received ideas: [{…}]
FilePageOverlay.tsx:329 [LABELS] 🎯 OVERLAY: Current fileIntelligence state: exists
FilePageOverlay.tsx:348 [LABELS] 🎯 OVERLAY: Setting fileIntelligence with 1 ideas
FilePageOverlay.tsx:365 [LABELS] 🎯 OVERLAY: ✅ Updated fileIntelligence from SmartLabelingInterface: {totalIdeas: 1, selectedIdeas: 1, fileIntelligenceSet: true, hasIdeas: true}
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
SmartLabelingInterface.tsx:260 [LABELS] 💾 Persisting analysis results: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', ideasCount: 1}
FilePageOverlay.tsx:105 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
FilePageOverlay.tsx:409 ✅ [INTELLIGENCE] Updated UI state (persistence handled by SmartLabelingInterface)
SmartLabelingInterface.tsx:270 [LABELS] ✅ Analysis results persisted successfully
FilePageOverlay.tsx:419 🎯 [OVERLAY] Processing complete signal received: {success: true, file_path: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', processing_time_ms: 7355, ideas_extracted: 1, entities_found: 0, …}
SmartLabelingInterface.tsx:560 [LABELS] 🤖 LOCAL-LLM: Analysis completed successfully: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', ideasCount: 1, modelUsed: 'gemma3:4b', processingTime: '7355ms'}
FilePageOverlay.tsx:105 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
FilePageOverlay.tsx:273 Uncaught (in promise) ReferenceError: fileIntelligence is not defined
    at handleAddManualLabel (FilePageOverlay.tsx:273:29)
    at executeDispatch (react-dom_client.js?v=c55d7f12:11736:11)
    at runWithFiberInDEV (react-dom_client.js?v=c55d7f12:1485:72)
    at processDispatchQueue (react-dom_client.js?v=c55d7f12:11772:37)
    at react-dom_client.js?v=c55d7f12:12182:11
    at batchedUpdates$1 (react-dom_client.js?v=c55d7f12:2628:42)
    at dispatchEventForPluginEventSystem (react-dom_client.js?v=c55d7f12:11877:9)
    at dispatchEvent (react-dom_client.js?v=c55d7f12:14792:13)
    at dispatchDiscreteEvent (react-dom_client.js?v=c55d7f12:14773:62)
handleAddManualLabel @ FilePageOverlay.tsx:273
executeDispatch @ react-dom_client.js?v=c55d7f12:11736
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
processDispatchQueue @ react-dom_client.js?v=c55d7f12:11772
(anonymous) @ react-dom_client.js?v=c55d7f12:12182
batchedUpdates$1 @ react-dom_client.js?v=c55d7f12:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=c55d7f12:11877
dispatchEvent @ react-dom_client.js?v=c55d7f12:14792
dispatchDiscreteEvent @ react-dom_client.js?v=c55d7f12:14773
<button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
IntelligenceHub @ IntelligenceHub.tsx:399
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<IntelligenceHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilePageOverlay @ FilePageOverlay.tsx:1074
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<FilePageOverlay>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilesPage @ FilesPage.tsx:1506
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<FilesPage>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
App @ App.tsx:195
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=c55d7f12:11623
performWorkUntilDeadline @ react-dom_client.js?v=c55d7f12:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
(anonymous) @ main.tsx:23
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:689 [LABELS] 🏷️ Custom annotation added, sending 2 ideas to overlay
SmartLabelingInterface.tsx:690 [LABELS] 🏷️ All ideas with custom: (2) [{…}, {…}]
FilePageOverlay.tsx:321 [LABELS] 🎯 OVERLAY: handleLabelsChanged called with 2 ideas
FilePageOverlay.tsx:322 [LABELS] 🎯 OVERLAY: Received ideas: (2) [{…}, {…}]
FilePageOverlay.tsx:329 [LABELS] 🎯 OVERLAY: Current fileIntelligence state: exists
FilePageOverlay.tsx:348 [LABELS] 🎯 OVERLAY: Setting fileIntelligence with 2 ideas
FilePageOverlay.tsx:365 [LABELS] 🎯 OVERLAY: ✅ Updated fileIntelligence from SmartLabelingInterface: {totalIdeas: 2, selectedIdeas: 2, fileIntelligenceSet: true, hasIdeas: true}
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
FilePageOverlay.tsx:105 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 2, selected_idea_ids_count: 2, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 2
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 1, total: 2}
FilePageOverlay.tsx:409 ✅ [INTELLIGENCE] Updated UI state (persistence handled by SmartLabelingInterface)
FilePageOverlay.tsx:105 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 2, selected_idea_ids_count: 2, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 2
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 1, total: 2}
IntelligenceHub.tsx:319 [ANNOTATIONS] 📝 Adding new user note
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:85 [ANNOTATIONS] 💾 Saving annotation: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', annotationId: 'note_1755018653160'}
annotationStorageService.ts:127 [ANNOTATIONS] 💾 ✅ Annotation saved successfully
IntelligenceHub.tsx:80 [ANNOTATIONS] 📖 Loading annotations for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:19 [ANNOTATIONS] 📖 Loading annotations for: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started'}
annotationStorageService.ts:27 [ANNOTATIONS] 🔍 DEBUG: Intelligence data structure: {hasKeyIdeas: true, keyIdeasCount: 1, hasSmartAnnotations: true, smartAnnotationsCount: 0, keys: Array(16)}
annotationStorageService.ts:39 [ANNOTATIONS] 📖 🔄 Creating and auto-saving first annotation from 1 labels
annotationStorageService.ts:58 [ANNOTATIONS] 📖 ✅ Auto-saved first annotation to storage
annotationStorageService.ts:64 [ANNOTATIONS] 📖 ✅ Created and auto-saved default annotation
annotationStorageService.ts:67 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:89 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:339 [ANNOTATIONS] 📝 ✅ New user note added
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 2, selected_idea_ids_count: 2, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 2
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 1, total: 2}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 2, selected_idea_ids_count: 2, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 2
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 1, total: 2}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 2, selected_idea_ids_count: 2, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 2
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 1, total: 2}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 2, selected_idea_ids_count: 2, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 2
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 1, total: 2}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 2, selected_idea_ids_count: 2, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 2
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 1, total: 2}
IntelligenceHub.tsx:210 [ANNOTATIONS] ✏️ Saving edited annotation: default_labels_summary
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:85 [ANNOTATIONS] 💾 Saving annotation: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', annotationId: 'default_labels_summary'}
annotationStorageService.ts:127 [ANNOTATIONS] 💾 ✅ Annotation saved successfully
IntelligenceHub.tsx:80 [ANNOTATIONS] 📖 Loading annotations for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\design_system_index.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/design_system_index.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:19 [ANNOTATIONS] 📖 Loading annotations for: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started'}
annotationStorageService.ts:27 [ANNOTATIONS] 🔍 DEBUG: Intelligence data structure: {hasKeyIdeas: true, keyIdeasCount: 1, hasSmartAnnotations: true, smartAnnotationsCount: 1, keys: Array(16)}
annotationStorageService.ts:67 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:89 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:225 [ANNOTATIONS] ✏️ ✅ Annotation updated successfully
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…\\getting-started\\documents\\design_system_index.md', fileContentLength: 6004, hasOnLabelsChanged: true, hasOnProcessingComplete: true}fileContentLength: 6004filePath: "C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\design_system_index.md"hasOnLabelsChanged: truehasOnProcessingComplete: true[[Prototype]]: Object
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 2, selected_idea_ids_count: 2, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 2
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 1, total: 2}
 FileViewerService: Closing file
 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Rendering right column, viewMode.currentMode: explorer
 Master content length: 1094
 Master loading: false
 Hub open: false
 FileViewerService: Opening file: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileName: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileType: undefined, isEditMode: false}
 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Rendering right column, viewMode.currentMode: explorer
 Master content length: 1094
 Master loading: false
 Hub open: false
 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 0, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
 [LABELS] 🎨 SmartLabelingInterface render called
 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
 [LABELS] 🎨 getDisplayIdeas called
 [LABELS] 🎨 labelState.available_ideas.length: 0
 [LABELS] 🎨 labelState.show_all_ideas: false
 [LABELS] 🎨 labelState.processing_status: idle
 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
 DocumentViewer: Loading file content for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md Type: markdown
 [ANNOTATIONS] 📖 Loading annotations for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
 [ANNOTATIONS] 📖 Loading annotations for: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started'}
 [ANNOTATIONS] 🔍 OVERLAY: useEffect triggered {isOpen: true, filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileName: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md'}
 🔄 [OVERLAY] Loading file content for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md Type: markdown
 Loading file content for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md Type: markdown
 [LABELS] 🔄 OVERLAY: File opened, SmartLabelingInterface will manage intelligence state
 [LABELS] 🔄 OVERLAY: Loading states reset, intelligence state preserved
 [ANNOTATIONS] 🚀 OVERLAY: About to load annotations for file: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
 [ANNOTATIONS] 📖 Loading annotations for: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started'}
 File tree updated: 3 items
 Expanded folders: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Selected file: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
 Selected folder: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents
 Current view mode: explorer
 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 0, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
 [LABELS] 🎨 SmartLabelingInterface render called
 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
 [LABELS] 🎨 getDisplayIdeas called
 [LABELS] 🎨 labelState.available_ideas.length: 0
 [LABELS] 🎨 labelState.show_all_ideas: false
 [LABELS] 🎨 labelState.processing_status: idle
 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
 [ANNOTATIONS] 📖 No existing intelligence data found
 [ANNOTATIONS] 📖 ✅ Loaded 0 annotations
 File getFileContent result: success 2400
 🔄 [OVERLAY] Set fileContent for text file: 1782 characters
 [ANNOTATIONS] 📖 No existing intelligence data found
 DocumentViewer: File read result: {success: true, content: '### Memory – Aug 10\r\n\r\n- **Current state**\r\n  - Ke… normalization, dual write, event flow, parser.\r\n'}
 Content loaded: 1782 characters
 DocumentViewer: Content loaded successfully, length: 1782
 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
 [LABELS] 🎨 SmartLabelingInterface render called
 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
 [LABELS] 🎨 getDisplayIdeas called
 [LABELS] 🎨 labelState.available_ideas.length: 0
 [LABELS] 🎨 labelState.show_all_ideas: false
 [LABELS] 🎨 labelState.processing_status: idle
 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
 [LABELS] 🔄 SmartLabelingInterface: useEffect triggered
 [LABELS] 🔄 SmartLabelingInterface: filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
 [LABELS] 🔄 SmartLabelingInterface: fileContent length: 1782
 [LABELS] 🔄 SmartLabelingInterface: onLabelsChanged callback: true
 [LABELS] 🔄 SmartLabelingInterface: Starting intelligence loading process (persisted first)
 [LABELS] 🔍 KERNEL-STATUS: Checking kernel availability
 [LABELS] 🔍 KERNEL-STATUS: Checking if kernel has intelligence for file
 [LABELS] 🔍 KERNEL-STATUS: filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
 [LABELS] 🔄 SmartLabelingInterface: loadOrProcessFileIntelligence called {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782}
 [LABELS] 🔄 KERNEL: Querying kernel for existing intelligence
 [LABELS] 🔍 KERNEL: Querying for existing intelligence: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
 [LABELS] 🔍 KERNEL-STATUS: Kernel response: {success: true, hasData: false, ideasCount: 0, hasIntelligence: false}
 [LABELS] 🔍 KERNEL-STATUS: Kernel status: {available: true, hasAnalyze: true, hasGet: true, hasIntelligence: false, electronAPI: true, …}
 [LABELS] 🔍 KERNEL-STATUS: Kernel available but no existing intelligence found
 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
 [LABELS] 🔍 KERNEL: Using contextPath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
 [LABELS] 🔍 KERNEL: ℹ️ Kernel returned no intelligence: no data
 [LABELS] 🔄 SmartLabelingInterface: Setting idle state - no existing intelligence
 [LABELS] 🏷️ SmartLabelingInterface: Notifying overlay with empty ideas array (no existing intelligence)
 [LABELS] ℹ️ SmartLabelingInterface: No stored intelligence found. Waiting for user action to analyze...
 Checking expansion for 📦 Shared Dropbox (C:\Users\<USER>\Documents\Post-Kernel-Test3/shared-dropbox): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Personal Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/personal-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for getting-started (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for Work Vault (C:\Users\<USER>\Documents\Post-Kernel-Test3/work-vault): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for project (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for artifacts (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\artifacts): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for documents (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\documents): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Checking expansion for images (C:\Users\<USER>\Documents\Post-Kernel-Test3\work-vault\project\images): true
 Available expanded paths: (11) ['C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/shared-dropbox', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/personal-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\images', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3/work-vault', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\artifacts', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\documents', 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\work-vault\\project\\images']
 Rendering right column, viewMode.currentMode: explorer
 Master content length: 1094
 Master loading: false
 Hub open: false
 [LABELS] 🏷️ Calling onLabelsChanged with empty array
 [LABELS] 🎯 OVERLAY: handleLabelsChanged called with 0 ideas
 [LABELS] 🎯 OVERLAY: Received ideas: []
 [LABELS] 🎯 OVERLAY: Current fileIntelligence state: null
 [LABELS] 🎯 OVERLAY: Setting fileIntelligence with 0 ideas
 [LABELS] 🎯 OVERLAY: ✅ Updated fileIntelligence from SmartLabelingInterface: {totalIdeas: 0, selectedIdeas: 0, fileIntelligenceSet: true, hasIdeas: false}
 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
 [LABELS] 🏷️ onLabelsChanged with empty array completed
 ✅ [INTELLIGENCE] Updated UI state (persistence handled by SmartLabelingInterface)
 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
 [LABELS] 🎨 SmartLabelingInterface render called
 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
 [LABELS] 🎨 getDisplayIdeas called
 [LABELS] 🎨 labelState.available_ideas.length: 0
 [LABELS] 🎨 labelState.show_all_ideas: false
 [LABELS] 🎨 labelState.processing_status: idle
 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
 [LABELS] 🎨 SmartLabelingInterface render called
 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
 [LABELS] 🎨 getDisplayIdeas called
 [LABELS] 🎨 labelState.available_ideas.length: 0
 [LABELS] 🎨 labelState.show_all_ideas: false
 [LABELS] 🎨 labelState.processing_status: idle
 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
 [LABELS] 🤖 processFileIntelligence: Starting local LLM analysis
 [LABELS] 🤖 filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
 [LABELS] 🤖 fileContent length: 1782
 [LABELS] 🤖 LOCAL-LLM: Checking for available local models
 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
 [LABELS] 🎨 SmartLabelingInterface render called
 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
 [LABELS] 🎨 getDisplayIdeas called
 [LABELS] 🎨 labelState.available_ideas.length: 0
 [LABELS] 🎨 labelState.show_all_ideas: false
 [LABELS] 🎨 labelState.processing_status: processing
 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
 ℹ️ 17:12:32 [LocalModelService] checkOllama: Ollama connected successfully {modelCount: 4, models: Array(4)}
 ℹ️ 17:12:32 [LocalModelService] checkOllama: Operation completed successfully: checkOllama 
ServiceLogger.ts:135 ℹ️ 17:12:32 [LocalModelService] checkOllama: Operation completed in 8ms: checkOllama {duration: 8}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
localModelService.ts:137  GET http://localhost:1234/v1/models net::ERR_CONNECTION_REFUSED
(anonymous) @ localModelService.ts:137
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
checkLMStudio @ localModelService.ts:132
getProviderStatus @ localModelService.ts:199
processFileIntelligence @ SmartLabelingInterface.tsx:471
onClick @ SmartLabelingInterface.tsx:791
executeDispatch @ react-dom_client.js?v=c55d7f12:11736
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
processDispatchQueue @ react-dom_client.js?v=c55d7f12:11772
(anonymous) @ react-dom_client.js?v=c55d7f12:12182
batchedUpdates$1 @ react-dom_client.js?v=c55d7f12:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=c55d7f12:11877
dispatchEvent @ react-dom_client.js?v=c55d7f12:14792
dispatchDiscreteEvent @ react-dom_client.js?v=c55d7f12:14773
<button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
SmartLabelingInterface @ SmartLabelingInterface.tsx:790
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<SmartLabelingInterface>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
IntelligenceHub @ IntelligenceHub.tsx:389
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<IntelligenceHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilePageOverlay @ FilePageOverlay.tsx:1074
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<FilePageOverlay>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilesPage @ FilesPage.tsx:1506
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
ServiceLogger.ts:141 ❌ 17:12:35 [LocalModelService] checkLMStudio: Operation failed: checkLMStudio ServiceError: Failed to fetch
    at ServiceError.fromError (ServiceError.ts:114:12)
    at wrapServiceOperation (ServiceError.ts:194:39)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:261:22)
    at async LocalModelService.executeOperation (BaseService.ts:213:12)
    at async LocalModelService.checkLMStudio (localModelService.ts:132:20)
    at async Promise.all (:5173/index 1)
    at async LocalModelService.getProviderStatus (localModelService.ts:197:44)
    at async processFileIntelligence (SmartLabelingInterface.tsx:471:30)
outputToConsole @ ServiceLogger.ts:141
log @ ServiceLogger.ts:112
error @ ServiceLogger.ts:63
operationFailure @ ServiceLogger.ts:84
(anonymous) @ BaseService.ts:219
await in (anonymous)
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
checkLMStudio @ localModelService.ts:132
getProviderStatus @ localModelService.ts:199
processFileIntelligence @ SmartLabelingInterface.tsx:471
onClick @ SmartLabelingInterface.tsx:791
executeDispatch @ react-dom_client.js?v=c55d7f12:11736
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
processDispatchQueue @ react-dom_client.js?v=c55d7f12:11772
(anonymous) @ react-dom_client.js?v=c55d7f12:12182
batchedUpdates$1 @ react-dom_client.js?v=c55d7f12:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=c55d7f12:11877
dispatchEvent @ react-dom_client.js?v=c55d7f12:14792
dispatchDiscreteEvent @ react-dom_client.js?v=c55d7f12:14773
<button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
SmartLabelingInterface @ SmartLabelingInterface.tsx:790
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<SmartLabelingInterface>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
IntelligenceHub @ IntelligenceHub.tsx:389
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<IntelligenceHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilePageOverlay @ FilePageOverlay.tsx:1074
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<FilePageOverlay>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilesPage @ FilesPage.tsx:1506
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
ServiceLogger.ts:143 ServiceError: Failed to fetch
    at ServiceError.fromError (ServiceError.ts:114:12)
    at wrapServiceOperation (ServiceError.ts:194:39)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:261:22)
    at async LocalModelService.executeOperation (BaseService.ts:213:12)
    at async LocalModelService.checkLMStudio (localModelService.ts:132:20)
    at async Promise.all (:5173/index 1)
    at async LocalModelService.getProviderStatus (localModelService.ts:197:44)
    at async processFileIntelligence (SmartLabelingInterface.tsx:471:30)
outputToConsole @ ServiceLogger.ts:143
log @ ServiceLogger.ts:112
error @ ServiceLogger.ts:63
operationFailure @ ServiceLogger.ts:84
(anonymous) @ BaseService.ts:219
await in (anonymous)
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
checkLMStudio @ localModelService.ts:132
getProviderStatus @ localModelService.ts:199
processFileIntelligence @ SmartLabelingInterface.tsx:471
onClick @ SmartLabelingInterface.tsx:791
executeDispatch @ react-dom_client.js?v=c55d7f12:11736
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
processDispatchQueue @ react-dom_client.js?v=c55d7f12:11772
(anonymous) @ react-dom_client.js?v=c55d7f12:12182
batchedUpdates$1 @ react-dom_client.js?v=c55d7f12:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=c55d7f12:11877
dispatchEvent @ react-dom_client.js?v=c55d7f12:14792
dispatchDiscreteEvent @ react-dom_client.js?v=c55d7f12:14773
<button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
SmartLabelingInterface @ SmartLabelingInterface.tsx:790
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<SmartLabelingInterface>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
IntelligenceHub @ IntelligenceHub.tsx:389
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<IntelligenceHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilePageOverlay @ FilePageOverlay.tsx:1074
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<FilePageOverlay>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilesPage @ FilesPage.tsx:1506
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
ServiceLogger.ts:135 ℹ️ 17:12:35 [LocalModelService] checkLMStudio: Operation completed in 2332ms: checkLMStudio {duration: 2332}
ServiceLogger.ts:138 ⚠️ 17:12:35 [LocalModelService] checkLMStudio: LM Studio not available ServiceError: Failed to fetch
    at ServiceError.fromError (ServiceError.ts:114:12)
    at wrapServiceOperation (ServiceError.ts:194:39)
    at async BaseService.ts:214:22
    at async PerformanceLogger.measureAsync (ServiceLogger.ts:261:22)
    at async LocalModelService.executeOperation (BaseService.ts:213:12)
    at async LocalModelService.checkLMStudio (localModelService.ts:132:20)
    at async Promise.all (:5173/index 1)
    at async LocalModelService.getProviderStatus (localModelService.ts:197:44)
    at async processFileIntelligence (SmartLabelingInterface.tsx:471:30)
outputToConsole @ ServiceLogger.ts:138
log @ ServiceLogger.ts:112
warn @ ServiceLogger.ts:56
checkLMStudio @ localModelService.ts:175
await in checkLMStudio
getProviderStatus @ localModelService.ts:199
processFileIntelligence @ SmartLabelingInterface.tsx:471
onClick @ SmartLabelingInterface.tsx:791
executeDispatch @ react-dom_client.js?v=c55d7f12:11736
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
processDispatchQueue @ react-dom_client.js?v=c55d7f12:11772
(anonymous) @ react-dom_client.js?v=c55d7f12:12182
batchedUpdates$1 @ react-dom_client.js?v=c55d7f12:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=c55d7f12:11877
dispatchEvent @ react-dom_client.js?v=c55d7f12:14792
dispatchDiscreteEvent @ react-dom_client.js?v=c55d7f12:14773
<button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
SmartLabelingInterface @ SmartLabelingInterface.tsx:790
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<SmartLabelingInterface>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
IntelligenceHub @ IntelligenceHub.tsx:389
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<IntelligenceHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilePageOverlay @ FilePageOverlay.tsx:1074
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
<FilePageOverlay>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=c55d7f12:250
FilesPage @ FilesPage.tsx:1506
react-stack-bottom-frame @ react-dom_client.js?v=c55d7f12:17424
renderWithHooks @ react-dom_client.js?v=c55d7f12:4206
updateFunctionComponent @ react-dom_client.js?v=c55d7f12:6619
beginWork @ react-dom_client.js?v=c55d7f12:7654
runWithFiberInDEV @ react-dom_client.js?v=c55d7f12:1485
performUnitOfWork @ react-dom_client.js?v=c55d7f12:10868
workLoopSync @ react-dom_client.js?v=c55d7f12:10728
renderRootSync @ react-dom_client.js?v=c55d7f12:10711
performWorkOnRoot @ react-dom_client.js?v=c55d7f12:10330
performSyncWorkOnRoot @ react-dom_client.js?v=c55d7f12:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=c55d7f12:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=c55d7f12:11558
(anonymous) @ react-dom_client.js?v=c55d7f12:11649
SmartLabelingInterface.tsx:472 [LABELS] 🤖 Provider Status: {ollama: {…}, lmstudio: {…}}
SmartLabelingInterface.tsx:497 [LABELS] 🤖 LOCAL-LLM: Using model: gemma3:4b
SmartLabelingInterface.tsx:503 [LABELS] 🤖 📡 Sending request to local model: ollama:gemma3:4b
SmartLabelingInterface.tsx:504 [LABELS] 🤖 📡 Prompt preview: You are an expert document analyzer. Extract key ideas and labels from the following document content.

REQUIREMENTS:
- Extract at least 1 meaningful label/key idea (minimum requirement)
- Focus on the most important topics, themes, and concepts
- Each label should be 1-4 words maximum
- Labels shou...
ServiceLogger.ts:135 ℹ️ 17:12:35 [LocalModelService] sendOllamaMessage: Sending message to Ollama {model: 'gemma3', messageCount: 1, streaming: false}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
ServiceLogger.ts:135 ℹ️ 17:12:36 [LocalModelService] sendOllamaMessage: Ollama response received {model: 'gemma3', responseLength: 2}
ServiceLogger.ts:135 ℹ️ 17:12:36 [LocalModelService] sendMessage: Operation completed successfully: sendMessage {modelId: 'ollama:gemma3:4b', messageCount: 1, hasChunkCallback: false}
ServiceLogger.ts:135 ℹ️ 17:12:36 [LocalModelService] sendMessage: Operation completed in 1023ms: sendMessage {duration: 1023}
SmartLabelingInterface.tsx:519 [LABELS] 🤖 📡 Raw response received: {}...
SmartLabelingInterface.tsx:970 [LABELS] 🤖 📝 Parsing LLM response...
SmartLabelingInterface.tsx:989 [LABELS] 🤖 📝 Attempting to parse JSON: {}...
SmartLabelingInterface.tsx:1013 [LABELS] 🤖 📝 Using fallback text extraction...
SmartLabelingInterface.tsx:1086 [LABELS] 🤖 📝 ✅ Fallback extraction created 1 labels
SmartLabelingInterface.tsx:531 [LABELS] 🤖 LOCAL-LLM: ✅ Analysis successful, extracted 1 ideas
FilePageOverlay.tsx:321 [LABELS] 🎯 OVERLAY: handleLabelsChanged called with 1 ideas
FilePageOverlay.tsx:322 [LABELS] 🎯 OVERLAY: Received ideas: [{…}]
FilePageOverlay.tsx:329 [LABELS] 🎯 OVERLAY: Current fileIntelligence state: exists
FilePageOverlay.tsx:348 [LABELS] 🎯 OVERLAY: Setting fileIntelligence with 1 ideas
FilePageOverlay.tsx:365 [LABELS] 🎯 OVERLAY: ✅ Updated fileIntelligence from SmartLabelingInterface: {totalIdeas: 1, selectedIdeas: 1, fileIntelligenceSet: true, hasIdeas: true}
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
SmartLabelingInterface.tsx:260 [LABELS] 💾 Persisting analysis results: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', ideasCount: 1}
FilePageOverlay.tsx:105 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
FilePageOverlay.tsx:409 ✅ [INTELLIGENCE] Updated UI state (persistence handled by SmartLabelingInterface)
SmartLabelingInterface.tsx:270 [LABELS] ✅ Analysis results persisted successfully
FilePageOverlay.tsx:419 🎯 [OVERLAY] Processing complete signal received: {success: true, file_path: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', processing_time_ms: 3361, ideas_extracted: 1, entities_found: 0, …}
SmartLabelingInterface.tsx:560 [LABELS] 🤖 LOCAL-LLM: Analysis completed successfully: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', ideasCount: 1, modelUsed: 'gemma3:4b', processingTime: '3361ms'}
FilePageOverlay.tsx:105 [ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
performanceMonitor.ts:153 📊 [PERFORMANCE] JS Heap: 40MB, System RAM: 57.5% (9GB/16GB)
IntelligenceHub.tsx:319 [ANNOTATIONS] 📝 Adding new user note
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:85 [ANNOTATIONS] 💾 Saving annotation: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', annotationId: 'note_1755018779560'}
annotationStorageService.ts:127 [ANNOTATIONS] 💾 ✅ Annotation saved successfully
IntelligenceHub.tsx:80 [ANNOTATIONS] 📖 Loading annotations for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:19 [ANNOTATIONS] 📖 Loading annotations for: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started'}
annotationStorageService.ts:27 [ANNOTATIONS] 🔍 DEBUG: Intelligence data structure: {hasKeyIdeas: true, keyIdeasCount: 1, hasSmartAnnotations: true, smartAnnotationsCount: 0, keys: Array(16)}
annotationStorageService.ts:39 [ANNOTATIONS] 📖 🔄 Creating and auto-saving first annotation from 1 labels
annotationStorageService.ts:58 [ANNOTATIONS] 📖 ✅ Auto-saved first annotation to storage
annotationStorageService.ts:64 [ANNOTATIONS] 📖 ✅ Created and auto-saved default annotation
annotationStorageService.ts:67 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:89 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:339 [ANNOTATIONS] 📝 ✅ New user note added
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
IntelligenceHub.tsx:210 [ANNOTATIONS] ✏️ Saving edited annotation: default_labels_summary
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:85 [ANNOTATIONS] 💾 Saving annotation: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', annotationId: 'default_labels_summary'}
annotationStorageService.ts:127 [ANNOTATIONS] 💾 ✅ Annotation saved successfully
IntelligenceHub.tsx:80 [ANNOTATIONS] 📖 Loading annotations for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:19 [ANNOTATIONS] 📖 Loading annotations for: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started'}
annotationStorageService.ts:27 [ANNOTATIONS] 🔍 DEBUG: Intelligence data structure: {hasKeyIdeas: true, keyIdeasCount: 1, hasSmartAnnotations: true, smartAnnotationsCount: 1, keys: Array(16)}
annotationStorageService.ts:67 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:89 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:225 [ANNOTATIONS] ✏️ ✅ Annotation updated successfully
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
IntelligenceHub.tsx:169 [ANNOTATIONS] ✏️ Started editing annotation: default_labels_summary
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
IntelligenceHub.tsx:210 [ANNOTATIONS] ✏️ Saving edited annotation: default_labels_summary
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:85 [ANNOTATIONS] 💾 Saving annotation: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', annotationId: 'default_labels_summary'}
annotationStorageService.ts:127 [ANNOTATIONS] 💾 ✅ Annotation saved successfully
IntelligenceHub.tsx:80 [ANNOTATIONS] 📖 Loading annotations for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:19 [ANNOTATIONS] 📖 Loading annotations for: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started'}
annotationStorageService.ts:27 [ANNOTATIONS] 🔍 DEBUG: Intelligence data structure: {hasKeyIdeas: true, keyIdeasCount: 1, hasSmartAnnotations: true, smartAnnotationsCount: 1, keys: Array(16)}
annotationStorageService.ts:67 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:89 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:225 [ANNOTATIONS] ✏️ ✅ Annotation updated successfully
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
IntelligenceHub.tsx:319 [ANNOTATIONS] 📝 Adding new user note
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:85 [ANNOTATIONS] 💾 Saving annotation: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', annotationId: 'note_1755018789409'}
annotationStorageService.ts:127 [ANNOTATIONS] 💾 ✅ Annotation saved successfully
IntelligenceHub.tsx:80 [ANNOTATIONS] 📖 Loading annotations for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:19 [ANNOTATIONS] 📖 Loading annotations for: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started'}
annotationStorageService.ts:27 [ANNOTATIONS] 🔍 DEBUG: Intelligence data structure: {hasKeyIdeas: true, keyIdeasCount: 1, hasSmartAnnotations: true, smartAnnotationsCount: 1, keys: Array(16)}
annotationStorageService.ts:67 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:89 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:339 [ANNOTATIONS] 📝 ✅ New user note added
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
IntelligenceHub.tsx:210 [ANNOTATIONS] ✏️ Saving edited annotation: default_labels_summary
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:85 [ANNOTATIONS] 💾 Saving annotation: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started', annotationId: 'default_labels_summary'}
annotationStorageService.ts:127 [ANNOTATIONS] 💾 ✅ Annotation saved successfully
IntelligenceHub.tsx:80 [ANNOTATIONS] 📖 Loading annotations for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\memory-aug10.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/memory-aug10.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
annotationStorageService.ts:19 [ANNOTATIONS] 📖 Loading annotations for: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', contextPath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started'}
annotationStorageService.ts:27 [ANNOTATIONS] 🔍 DEBUG: Intelligence data structure: {hasKeyIdeas: true, keyIdeasCount: 1, hasSmartAnnotations: true, smartAnnotationsCount: 1, keys: Array(16)}
annotationStorageService.ts:67 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:89 [ANNOTATIONS] 📖 ✅ Loaded 1 annotations
IntelligenceHub.tsx:225 [ANNOTATIONS] ✏️ ✅ Annotation updated successfully
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\memory-aug10.md', fileContentLength: 1782, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:763 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:764 [LABELS] 🎨 Current labelState: {available_ideas_count: 1, selected_idea_ids_count: 1, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:737 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:738 [LABELS] 🎨 labelState.available_ideas.length: 1
SmartLabelingInterface.tsx:739 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:740 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:754 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 1, others: 0, total: 1}
