client:733 [vite] connecting...
client:826 [vite] connected.
react-dom_client.js?v=fe429164:17987 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
ServiceLogger.ts:135 ℹ️ 17:29:58 [OpenRouterService] doInitialize: OpenRouter service initialized Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [LocalModelService] doInitialize: Local model service initialized Object
vaultUIManager.ts:48 getVaultRegistry: Checking electronAPI availability...
vaultUIManager.ts:55 getVaultRegistry: Calling electronAPI.vault.getVaultRegistry()...
performanceMonitor.ts:385 🖥️ [SYSTEM] Using default hardware profile: 16GB RAM, mid-range tier
performanceMonitor.ts:420 ⚙️ [ADAPTIVE] Set thresholds for mid-range: JS heap 512MB, RAM 80.0%
ServiceLogger.ts:135 ℹ️ 17:29:58 [IntelligenceService] doInitialize: Intelligence service initialized Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [VaultFileHandler] VaultFileHandler initialized with streaming support 
relativeStorageService.ts:55 [RELATIVE-STORAGE] 🚀 Service initialized
storageServiceAdapter.ts:29 [STORAGE-ADAPTER] 🚀 Service initialized
ServiceLogger.ts:135 ℹ️ 17:29:58 [FileAnalysisService] doInitialize: File Analysis Service initialized 
cacheManager.ts:182 🔥 [CACHE] Loaded cache from storage: 0 warm, 0 cold
ServiceLogger.ts:135 ℹ️ 17:29:58 [CacheManager] doInitialize: Cache manager initialized Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [StreamingFileProcessor] doInitialize: Streaming file processor initialized Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [FileProcessingQueue] doInitialize: File processing queue initialized 
ServiceLogger.ts:135 ℹ️ 17:29:58 [BatchFileProcessingService] doInitialize: Batch File Processing Service initialized 
ServiceLogger.ts:135 ℹ️ 17:29:58 [SmartInstructionService] doInitialize: Smart Instruction service initialized with LLM integration Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [DocumentIntelligenceService] Document Intelligence Service initialized 
main.tsx:9 Window electronAPI available: true
main.tsx:11 ElectronAPI methods: Array(9)
ServiceLogger.ts:135 ℹ️ 17:29:58 [OpenRouterService] initialization: Operation completed in 34ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [OpenRouterService] initialize: Operation completed successfully: initialize Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [LocalModelService] initialization: Operation completed in 34ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [LocalModelService] initialize: Operation completed successfully: initialize Object
performanceMonitor.ts:420 ⚙️ [ADAPTIVE] Set thresholds for undefined: JS heap 512MB, RAM 85.0%
ServiceLogger.ts:135 ℹ️ 17:29:58 [PerformanceMonitor] doInitialize: Performance monitor initialized with adaptive thresholds Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [IntelligenceService] initialization: Operation completed in 12ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [IntelligenceService] initialize: Operation completed successfully: initialize Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [VaultFileHandler] initialization: Operation completed in 11ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [VaultFileHandler] initialize: Operation completed successfully: initialize Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [undefined] initialization: Operation completed in 10ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [undefined] initialize: Operation completed successfully: initialize Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [undefined] initialization: Operation completed in 10ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [undefined] initialize: Operation completed successfully: initialize Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [FileAnalysisService] initialization: Operation completed in 11ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [FileAnalysisService] initialize: Operation completed successfully: initialize Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [CacheManager] initialization: Operation completed in 10ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [CacheManager] initialize: Operation completed successfully: initialize Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [StreamingFileProcessor] initialization: Operation completed in 10ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [StreamingFileProcessor] initialize: Operation completed successfully: initialize Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [FileProcessingQueue] initialization: Operation completed in 9ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [FileProcessingQueue] initialize: Operation completed successfully: initialize Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [BatchFileProcessingService] initialization: Operation completed in 9ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [BatchFileProcessingService] initialize: Operation completed successfully: initialize Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [SmartInstructionService] initialization: Operation completed in 9ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [SmartInstructionService] initialize: Operation completed successfully: initialize Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [DocumentIntelligenceService] initialization: Operation completed in 8ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [DocumentIntelligenceService] initialize: Operation completed successfully: initialize Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [PerformanceMonitor] initialization: Operation completed in 14ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [PerformanceMonitor] initialize: Operation completed successfully: initialize Object
vaultUIManager.ts:57 getVaultRegistry: Result from electronAPI: Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [ContextVaultService] loadVaults: Operation completed successfully: loadVaults 
ServiceLogger.ts:135 ℹ️ 17:29:58 [ContextVaultService] loadVaults: Operation completed in 18ms: loadVaults Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [ContextVaultService] doInitialize: Context vault service initialized successfully Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [ContextVaultService] initialization: Operation completed in 18ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [ContextVaultService] initialize: Operation completed successfully: initialize Object
index.ts:1366 Auto-loading models with saved API key...
useKeyboardShortcuts.ts:35 Navigation history updated: Object
vaultUIManager.ts:48 getVaultRegistry: Checking electronAPI availability...
vaultUIManager.ts:55 getVaultRegistry: Calling electronAPI.vault.getVaultRegistry()...
ServiceLogger.ts:135 ℹ️ 17:29:58 [PerformanceMonitor] startMonitoring: Performance monitoring started Object
vaultUIManager.ts:57 getVaultRegistry: Result from electronAPI: Object
vaultUIManager.ts:48 getVaultRegistry: Checking electronAPI availability...
vaultUIManager.ts:55 getVaultRegistry: Calling electronAPI.vault.getVaultRegistry()...
ServiceLogger.ts:135 ℹ️ 17:29:58 [SharedDropboxService] getVaultRoot: Operation completed successfully: getVaultRoot 
ServiceLogger.ts:135 ℹ️ 17:29:58 [SharedDropboxService] getVaultRoot: Operation completed in 35ms: getVaultRoot Object
vaultUIManager.ts:57 getVaultRegistry: Result from electronAPI: Object
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\.context\metadata.json
ServiceLogger.ts:135 ℹ️ 17:29:58 [SharedDropboxService] getVaultRoot: Operation completed successfully: getVaultRoot 
ServiceLogger.ts:135 ℹ️ 17:29:58 [SharedDropboxService] getVaultRoot: Operation completed in 8ms: getVaultRoot Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [SharedDropboxService] loadFiles: Operation completed successfully: loadFiles 
ServiceLogger.ts:135 ℹ️ 17:29:58 [SharedDropboxService] loadFiles: Operation completed in 12ms: loadFiles Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [SharedDropboxService] doInitialize: Shared dropbox initialized successfully Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [SharedDropboxService] initialization: Operation completed in 91ms: initialization Object
ServiceLogger.ts:135 ℹ️ 17:29:58 [SharedDropboxService] initialize: Operation completed successfully: initialize Object
vaultUIManager.ts:163 ✅ [VAULT-SCAN] Valid metadata loaded from C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\.context\metadata.json
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\master.md
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\.context\metadata.json
vaultUIManager.ts:163 ✅ [VAULT-SCAN] Valid metadata loaded from C:\Users\<USER>\Documents\Test20\work-vault\project\.context\metadata.json
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\master.md
ServiceLogger.ts:135 ℹ️ 17:30:08 [BatchFileProcessingService] processAllVaultsWithQueue: Starting queued vault processing 
vaultUIManager.ts:48 getVaultRegistry: Checking electronAPI availability...
vaultUIManager.ts:55 getVaultRegistry: Calling electronAPI.vault.getVaultRegistry()...
vaultUIManager.ts:57 getVaultRegistry: Result from electronAPI: {version: '1.0', vaultRoot: 'C:\\Users\\<USER>\\Documents\\Test20', vaults: Array(2), lastScan: '2025-08-09T17:29:58.445Z', preferences: {…}}
ServiceLogger.ts:135 ℹ️ 17:30:08 [BatchFileProcessingService] processAllVaultsWithQueue: Vault registry retrieved for queue processing {hasRegistry: true, vaultCount: 2, vaults: Array(2)}
batchFileProcessingService.ts:411 ✅ [VAULT-DISCOVERY] Found 2 registered vaults: (2) [{…}, {…}]
batchFileProcessingService.ts:418 🏗️ [QUEUE] Processing vault: Personal Vault at C:\Users\<USER>\Documents\Test20\personal-vault
batchFileProcessingService.ts:203 🚀 [VAULT-SCAN] Starting recursive vault scan for: C:\Users\<USER>\Documents\Test20\personal-vault
batchFileProcessingService.ts:204 🚀 [VAULT-SCAN] Allowed extensions: (54) ['pdf', 'md', 'txt', 'doc', 'docx', 'rtf', 'json', 'csv', 'xml', 'yaml', 'yml', 'log', 'js', 'ts', 'tsx', 'jsx', 'html', 'css', 'scss', 'less', 'py', 'java', 'cpp', 'c', 'h', 'cs', 'php', 'rb', 'go', 'rs', 'ini', 'conf', 'config', 'env', 'properties', 'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg', 'tiff', 'xlsx', 'xls', 'ods', 'pptx', 'ppt', 'odp', 'zip', 'rar', '7z', 'tar', 'gz']
batchFileProcessingService.ts:205 🚀 [VAULT-SCAN] Excluded directories: (4) ['.vault', '.context', 'node_modules', '.git']
batchFileProcessingService.ts:206 🚀 [VAULT-SCAN] Excluded files: (3) ['master.md', '.gitignore', '.DS_Store']
ServiceLogger.ts:135 ℹ️ 17:30:08 [BatchFileProcessingService] getVaultFiles: Starting recursive vault scan {vaultPath: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault', allowedExtensions: Array(54), excludedDirs: Array(4), excludedFiles: Array(3)}
batchFileProcessingService.ts:246 🔍 [SCAN] Starting scan of directory: C:\Users\<USER>\Documents\Test20\personal-vault
batchFileProcessingService.ts:266 📁 [SCAN] Found 4 items in C:\Users\<USER>\Documents\Test20\personal-vault: (4) ['.context (undefined)', '.vault (undefined)', 'getting-started (undefined)', 'master.md (undefined)']
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: .context (undefined)
batchFileProcessingService.ts:273 ⏭️ [SCAN] Skipping excluded item: .context
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: .vault (undefined)
batchFileProcessingService.ts:273 ⏭️ [SCAN] Skipping excluded item: .vault
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: getting-started (undefined)
batchFileProcessingService.ts:285 🔍 [SCAN] Item analysis: getting-started, type: undefined, isDirectory: true, isFile: false
batchFileProcessingService.ts:288 📂 [SCAN] Entering subdirectory: getting-started
batchFileProcessingService.ts:246 🔍 [SCAN] Starting scan of directory: C:\Users\<USER>\Documents\Test20\personal-vault/getting-started
batchFileProcessingService.ts:266 📁 [SCAN] Found 5 items in C:\Users\<USER>\Documents\Test20\personal-vault/getting-started: (5) ['.context (undefined)', 'artifacts (undefined)', 'documents (undefined)', 'images (undefined)', 'master.md (undefined)']
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: .context (undefined)
batchFileProcessingService.ts:273 ⏭️ [SCAN] Skipping excluded item: .context
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: artifacts (undefined)
batchFileProcessingService.ts:285 🔍 [SCAN] Item analysis: artifacts, type: undefined, isDirectory: true, isFile: false
batchFileProcessingService.ts:288 📂 [SCAN] Entering subdirectory: artifacts
batchFileProcessingService.ts:246 🔍 [SCAN] Starting scan of directory: C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/artifacts
batchFileProcessingService.ts:266 📁 [SCAN] Found 0 items in C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/artifacts: []
batchFileProcessingService.ts:315 📊 [SCAN] Directory scan complete for C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/artifacts: 0 processable files found
batchFileProcessingService.ts:292 📂 [SCAN] Found 0 files in subdirectory: artifacts
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: documents (undefined)
batchFileProcessingService.ts:285 🔍 [SCAN] Item analysis: documents, type: undefined, isDirectory: true, isFile: false
batchFileProcessingService.ts:288 📂 [SCAN] Entering subdirectory: documents
batchFileProcessingService.ts:246 🔍 [SCAN] Starting scan of directory: C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/documents
batchFileProcessingService.ts:266 📁 [SCAN] Found 1 items in C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/documents: ['modelUpdate-externalization.md (undefined)']
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: modelUpdate-externalization.md (undefined)
batchFileProcessingService.ts:285 🔍 [SCAN] Item analysis: modelUpdate-externalization.md, type: undefined, isDirectory: false, isFile: true
batchFileProcessingService.ts:297 📄 [SCAN] File: modelUpdate-externalization.md, extension: md, allowed: true
batchFileProcessingService.ts:305 ✅ [SCAN] Added processable file: {name: 'modelUpdate-externalization.md', path: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault/getting-started/documents/modelUpdate-externalization.md', size: 5314}
batchFileProcessingService.ts:315 📊 [SCAN] Directory scan complete for C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/documents: 1 processable files found
batchFileProcessingService.ts:292 📂 [SCAN] Found 1 files in subdirectory: documents
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: images (undefined)
batchFileProcessingService.ts:285 🔍 [SCAN] Item analysis: images, type: undefined, isDirectory: true, isFile: false
batchFileProcessingService.ts:288 📂 [SCAN] Entering subdirectory: images
batchFileProcessingService.ts:246 🔍 [SCAN] Starting scan of directory: C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/images
batchFileProcessingService.ts:266 📁 [SCAN] Found 0 items in C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/images: []
batchFileProcessingService.ts:315 📊 [SCAN] Directory scan complete for C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/images: 0 processable files found
batchFileProcessingService.ts:292 📂 [SCAN] Found 0 files in subdirectory: images
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: master.md (undefined)
batchFileProcessingService.ts:273 ⏭️ [SCAN] Skipping excluded item: master.md
batchFileProcessingService.ts:315 📊 [SCAN] Directory scan complete for C:\Users\<USER>\Documents\Test20\personal-vault/getting-started: 1 processable files found
batchFileProcessingService.ts:292 📂 [SCAN] Found 1 files in subdirectory: getting-started
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: master.md (undefined)
batchFileProcessingService.ts:273 ⏭️ [SCAN] Skipping excluded item: master.md
batchFileProcessingService.ts:315 📊 [SCAN] Directory scan complete for C:\Users\<USER>\Documents\Test20\personal-vault: 1 processable files found
batchFileProcessingService.ts:217 🎯 [VAULT-SCAN] Recursive scan complete for C:\Users\<USER>\Documents\Test20\personal-vault
batchFileProcessingService.ts:218 🎯 [VAULT-SCAN] Total files found: 1
batchFileProcessingService.ts:219 🎯 [VAULT-SCAN] Files: [{…}]
ServiceLogger.ts:135 ℹ️ 17:30:08 [BatchFileProcessingService] getVaultFiles: Recursive scan complete {totalFiles: 1, fileNames: Array(1)}
batchFileProcessingService.ts:421 🏗️ [QUEUE] Vault Personal Vault scan result: 1 files found
ServiceLogger.ts:135 ℹ️ 17:30:08 [BatchFileProcessingService] processAllVaultsWithQueue: Queueing vault files {vaultName: 'Personal Vault', totalFiles: 1}
batchFileProcessingService.ts:435 ➕ [QUEUE] Adding file to queue: modelUpdate-externalization.md (5314 bytes, priority: high)
ServiceLogger.ts:135 ℹ️ 17:30:08 [FileProcessingQueue] addTask: Task added to queue {taskId: 'task_1754760608336_pxvgof48d', fileName: 'modelUpdate-externalization.md', priority: 'high', queueSize: 1}
ServiceLogger.ts:135 ℹ️ 17:30:08 [FileProcessingQueue] startProcessing: Queue processing started 
performanceMonitor.ts:221 ✅ [PERFORMANCE] Can process batch: JS heap 27MB, System 55.6%
ServiceLogger.ts:135 ℹ️ 17:30:08 [FileProcessingQueue] processNext: Processing task started {taskId: 'task_1754760608336_pxvgof48d', fileName: 'modelUpdate-externalization.md', attempt: 1}
cacheManager.ts:260 ❌ [CACHE] Cache miss: file_processing_task_1754760608336_pxvgof48d_modelUpdate-externalization.md
streamingFileProcessor.ts:148 🌊 [STREAMING] File: C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/documents/modelUpdate-externalization.md, extension: md, use streaming: false
ServiceLogger.ts:135 ℹ️ 17:30:08 [FileProcessingQueue] addTask: Operation completed successfully: addTask {filePath: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault/getting-started/documents/modelUpdate-externalization.md', fileName: 'modelUpdate-externalization.md', priority: 'high'}
ServiceLogger.ts:135 ℹ️ 17:30:08 [FileProcessingQueue] addTask: Operation completed in 2ms: addTask {duration: 2}
batchFileProcessingService.ts:445 ✅ [QUEUE] File queued with task ID: task_1754760608336_pxvgof48d
batchFileProcessingService.ts:418 🏗️ [QUEUE] Processing vault: Work Vault at C:\Users\<USER>\Documents\Test20\work-vault
batchFileProcessingService.ts:203 🚀 [VAULT-SCAN] Starting recursive vault scan for: C:\Users\<USER>\Documents\Test20\work-vault
batchFileProcessingService.ts:204 🚀 [VAULT-SCAN] Allowed extensions: (54) ['pdf', 'md', 'txt', 'doc', 'docx', 'rtf', 'json', 'csv', 'xml', 'yaml', 'yml', 'log', 'js', 'ts', 'tsx', 'jsx', 'html', 'css', 'scss', 'less', 'py', 'java', 'cpp', 'c', 'h', 'cs', 'php', 'rb', 'go', 'rs', 'ini', 'conf', 'config', 'env', 'properties', 'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg', 'tiff', 'xlsx', 'xls', 'ods', 'pptx', 'ppt', 'odp', 'zip', 'rar', '7z', 'tar', 'gz']
batchFileProcessingService.ts:205 🚀 [VAULT-SCAN] Excluded directories: (4) ['.vault', '.context', 'node_modules', '.git']
batchFileProcessingService.ts:206 🚀 [VAULT-SCAN] Excluded files: (3) ['master.md', '.gitignore', '.DS_Store']
ServiceLogger.ts:135 ℹ️ 17:30:08 [BatchFileProcessingService] getVaultFiles: Starting recursive vault scan {vaultPath: 'C:\\Users\\<USER>\\Documents\\Test20\\work-vault', allowedExtensions: Array(54), excludedDirs: Array(4), excludedFiles: Array(3)}
batchFileProcessingService.ts:246 🔍 [SCAN] Starting scan of directory: C:\Users\<USER>\Documents\Test20\work-vault
fileProcessingQueue.ts:282 📖 [QUEUE-PROCESS] Reading file content: C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/documents/modelUpdate-externalization.md
fileProcessingQueue.ts:328 📄 [QUEUE-PROCESS] Using direct read for MD file
batchFileProcessingService.ts:266 📁 [SCAN] Found 2 items in C:\Users\<USER>\Documents\Test20\work-vault: (2) ['.vault (undefined)', 'project (undefined)']
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: .vault (undefined)
batchFileProcessingService.ts:273 ⏭️ [SCAN] Skipping excluded item: .vault
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: project (undefined)
batchFileProcessingService.ts:285 🔍 [SCAN] Item analysis: project, type: undefined, isDirectory: true, isFile: false
batchFileProcessingService.ts:288 📂 [SCAN] Entering subdirectory: project
batchFileProcessingService.ts:246 🔍 [SCAN] Starting scan of directory: C:\Users\<USER>\Documents\Test20\work-vault/project
batchFileProcessingService.ts:266 📁 [SCAN] Found 5 items in C:\Users\<USER>\Documents\Test20\work-vault/project: (5) ['.context (undefined)', 'artifacts (undefined)', 'documents (undefined)', 'images (undefined)', 'master.md (undefined)']
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: .context (undefined)
batchFileProcessingService.ts:273 ⏭️ [SCAN] Skipping excluded item: .context
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: artifacts (undefined)
batchFileProcessingService.ts:285 🔍 [SCAN] Item analysis: artifacts, type: undefined, isDirectory: true, isFile: false
batchFileProcessingService.ts:288 📂 [SCAN] Entering subdirectory: artifacts
batchFileProcessingService.ts:246 🔍 [SCAN] Starting scan of directory: C:\Users\<USER>\Documents\Test20\work-vault/project/artifacts
fileProcessingQueue.ts:333 ✅ [QUEUE-PROCESS] File content read successfully: 5246 characters
fileProcessingQueue.ts:343 🧠 [QUEUE-PROCESS] Analyzing document: modelUpdate-externalization.md
fileProcessingQueue.ts:345 📄 [QUEUE-PROCESS] Markdown file detected; sending raw text to LLM
fileAnalysisService.ts:997 [LABELS] 🤖 analyzeAndStoreDocument called
fileAnalysisService.ts:998 [LABELS] 🤖 filePath: C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/documents/modelUpdate-externalization.md
fileAnalysisService.ts:999 [LABELS] 🤖 vaultPath: C:\Users\<USER>\Documents\Test20\personal-vault
fileAnalysisService.ts:1000 [LABELS] 🤖 content length: 5246
ServiceLogger.ts:135 ℹ️ 17:30:08 [FileAnalysisService] analyzeAndStoreDocument: Analyzing and storing document {filePath: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault/getting-started/documents/modelUpdate-externalization.md'}
fileAnalysisService.ts:1007 [LABELS] 🤖 Starting document analysis...
ServiceLogger.ts:135 ℹ️ 17:30:08 [FileAnalysisService] analyzeDocument: Starting document analysis {contentLength: 5246, model: 'ollama:gemma3:latest', minIdeas: 10}
fileAnalysisService.ts:73 [LABELS] 🤖 Attempting local model analysis...
fileAnalysisService.ts:138 [LABELS] 🔍 ===== SYSTEMATIC SERVICE CHECK =====
fileAnalysisService.ts:139 [LABELS] 🔍 1. Checking local model service health...
batchFileProcessingService.ts:266 📁 [SCAN] Found 0 items in C:\Users\<USER>\Documents\Test20\work-vault/project/artifacts: []
batchFileProcessingService.ts:315 📊 [SCAN] Directory scan complete for C:\Users\<USER>\Documents\Test20\work-vault/project/artifacts: 0 processable files found
batchFileProcessingService.ts:292 📂 [SCAN] Found 0 files in subdirectory: artifacts
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: documents (undefined)
batchFileProcessingService.ts:285 🔍 [SCAN] Item analysis: documents, type: undefined, isDirectory: true, isFile: false
batchFileProcessingService.ts:288 📂 [SCAN] Entering subdirectory: documents
batchFileProcessingService.ts:246 🔍 [SCAN] Starting scan of directory: C:\Users\<USER>\Documents\Test20\work-vault/project/documents
batchFileProcessingService.ts:266 📁 [SCAN] Found 0 items in C:\Users\<USER>\Documents\Test20\work-vault/project/documents: []
batchFileProcessingService.ts:315 📊 [SCAN] Directory scan complete for C:\Users\<USER>\Documents\Test20\work-vault/project/documents: 0 processable files found
batchFileProcessingService.ts:292 📂 [SCAN] Found 0 files in subdirectory: documents
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: images (undefined)
batchFileProcessingService.ts:285 🔍 [SCAN] Item analysis: images, type: undefined, isDirectory: true, isFile: false
batchFileProcessingService.ts:288 📂 [SCAN] Entering subdirectory: images
batchFileProcessingService.ts:246 🔍 [SCAN] Starting scan of directory: C:\Users\<USER>\Documents\Test20\work-vault/project/images
batchFileProcessingService.ts:266 📁 [SCAN] Found 0 items in C:\Users\<USER>\Documents\Test20\work-vault/project/images: []
batchFileProcessingService.ts:315 📊 [SCAN] Directory scan complete for C:\Users\<USER>\Documents\Test20\work-vault/project/images: 0 processable files found
batchFileProcessingService.ts:292 📂 [SCAN] Found 0 files in subdirectory: images
batchFileProcessingService.ts:269 🔎 [SCAN] Processing item: master.md (undefined)
batchFileProcessingService.ts:273 ⏭️ [SCAN] Skipping excluded item: master.md
batchFileProcessingService.ts:315 📊 [SCAN] Directory scan complete for C:\Users\<USER>\Documents\Test20\work-vault/project: 0 processable files found
batchFileProcessingService.ts:292 📂 [SCAN] Found 0 files in subdirectory: project
batchFileProcessingService.ts:315 📊 [SCAN] Directory scan complete for C:\Users\<USER>\Documents\Test20\work-vault: 0 processable files found
batchFileProcessingService.ts:217 🎯 [VAULT-SCAN] Recursive scan complete for C:\Users\<USER>\Documents\Test20\work-vault
batchFileProcessingService.ts:218 🎯 [VAULT-SCAN] Total files found: 0
batchFileProcessingService.ts:219 🎯 [VAULT-SCAN] Files: []
ServiceLogger.ts:135 ℹ️ 17:30:08 [BatchFileProcessingService] getVaultFiles: Recursive scan complete {totalFiles: 0, fileNames: Array(0)}
batchFileProcessingService.ts:421 🏗️ [QUEUE] Vault Work Vault scan result: 0 files found
ServiceLogger.ts:135 ℹ️ 17:30:08 [BatchFileProcessingService] processAllVaultsWithQueue: Queueing vault files {vaultName: 'Work Vault', totalFiles: 0}
batchFileProcessingService.ts:429 ⚠️ [QUEUE] No files found in vault: Work Vault
ServiceLogger.ts:135 ℹ️ 17:30:08 [BatchFileProcessingService] processAllVaultsWithQueue: All files queued for processing {totalTasks: 1, totalVaults: 2}
ServiceLogger.ts:135 ℹ️ 17:30:08 [BatchFileProcessingService] processAllVaultsWithQueue: Operation completed successfully: processAllVaultsWithQueue {totalVaults: 'unknown'}
ServiceLogger.ts:135 ℹ️ 17:30:08 [BatchFileProcessingService] processAllVaultsWithQueue: Operation completed in 63ms: processAllVaultsWithQueue {duration: 63}
vaultUIManager.ts:48 getVaultRegistry: Checking electronAPI availability...
vaultUIManager.ts:55 getVaultRegistry: Calling electronAPI.vault.getVaultRegistry()...
vaultUIManager.ts:57 getVaultRegistry: Result from electronAPI: {version: '1.0', vaultRoot: 'C:\\Users\\<USER>\\Documents\\Test20', vaults: Array(2), lastScan: '2025-08-09T17:29:58.445Z', preferences: {…}}
vaultUIManager.ts:48 getVaultRegistry: Checking electronAPI availability...
vaultUIManager.ts:55 getVaultRegistry: Calling electronAPI.vault.getVaultRegistry()...
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkOllama: Ollama connected successfully {modelCount: 4, models: Array(4)}
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkOllama: Operation completed successfully: checkOllama 
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkOllama: Operation completed in 28ms: checkOllama {duration: 28}
vaultUIManager.ts:57 getVaultRegistry: Result from electronAPI: {version: '1.0', vaultRoot: 'C:\\Users\\<USER>\\Documents\\Test20', vaults: Array(2), lastScan: '2025-08-09T17:29:58.445Z', preferences: {…}}
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\.context\metadata.json
vaultUIManager.ts:163 ✅ [VAULT-SCAN] Valid metadata loaded from C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\.context\metadata.json
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\master.md
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\.context\metadata.json
vaultUIManager.ts:163 ✅ [VAULT-SCAN] Valid metadata loaded from C:\Users\<USER>\Documents\Test20\work-vault\project\.context\metadata.json
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\master.md
modelUpdateLogic.ts:215 Loaded manifest from primary URL: /models-manifest.json
index.ts:1181 Using existing models or fetching from OpenRouter...
modelUpdateLogic.ts:215 Loaded manifest from primary URL: /models-manifest.json
modelUpdateLogic.ts:293 Using model manifest v2025.07.16
index.ts:1187 Loaded 318 models
index.ts:1229 Latest models found: (16) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
index.ts:1304 🔍 Checking local models...
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkOllama: Ollama connected successfully {modelCount: 4, models: Array(4)}
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkOllama: Operation completed successfully: checkOllama 
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkOllama: Operation completed in 7ms: checkOllama {duration: 7}
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkLMStudio: LM Studio connected successfully {modelCount: 1, models: Array(1)}
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkLMStudio: Operation completed successfully: checkLMStudio 
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkLMStudio: Operation completed in 153ms: checkLMStudio {duration: 153}
fileAnalysisService.ts:144 [LABELS] 🔍 Provider Status: {ollama: {…}, lmstudio: {…}}
fileAnalysisService.ts:168 [LABELS] 🔍 2. Checking model availability...
fileAnalysisService.ts:169 [LABELS] 🔍 Requested model: ollama:gemma3:latest
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkLMStudio: LM Studio connected successfully {modelCount: 1, models: Array(1)}
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkLMStudio: Operation completed successfully: checkLMStudio 
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkLMStudio: Operation completed in 5ms: checkLMStudio {duration: 5}
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkOllama: Ollama connected successfully {modelCount: 4, models: Array(4)}
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkOllama: Operation completed successfully: checkOllama 
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkOllama: Operation completed in 11ms: checkOllama {duration: 11}
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkLMStudio: LM Studio connected successfully {modelCount: 1, models: Array(1)}
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkLMStudio: Operation completed successfully: checkLMStudio 
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkLMStudio: Operation completed in 16ms: checkLMStudio {duration: 16}
fileAnalysisService.ts:172 [LABELS] 🔍 Available models: (5) [{…}, {…}, {…}, {…}, {…}]
fileAnalysisService.ts:194 [LABELS] 🔍 ✅ Model found: gemma3:latest
fileAnalysisService.ts:195 [LABELS] 🔍 ===== SERVICE CHECK COMPLETE =====
fileAnalysisService.ts:196 [LABELS] 🤖 Using model: gemma3:latest
fileAnalysisService.ts:202 [LABELS] 🤖 📡 Sending request to local model: ollama:gemma3:latest
fileAnalysisService.ts:203 [LABELS] 🤖 📡 Prompt preview: You are a precise extractor. Do NOT explain. Output exactly ONE fenced Markdown block, nothing else.

Task
- Read the document between DOC and DOC.
- Produce 10 key ideas as 1–4 word noun phrases (no verbs, no punctuation).
- Be tolerant: if the doc is short, output as many as you can (≥3).
- Use on...
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] sendOllamaMessage: Sending message to Ollama {model: 'gemma3', messageCount: 1, streaming: false}
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkOllama: Ollama connected successfully {modelCount: 4, models: Array(4)}
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkOllama: Operation completed successfully: checkOllama 
ServiceLogger.ts:138 ⚠️ 17:30:08 [LocalModelService] checkOllama: Timer not found for operation: checkOllama 
outputToConsole @ ServiceLogger.ts:138
log @ ServiceLogger.ts:112
warn @ ServiceLogger.ts:56
endTimer @ ServiceLogger.ts:236
measureAsync @ ServiceLogger.ts:262
await in measureAsync
executeOperation @ BaseService.ts:213
checkOllama @ localModelService.ts:80
getAllLocalModels @ localModelService.ts:185
checkLocalModels @ index.ts:1308
await in checkLocalModels
initializeApp @ index.ts:1375
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkLMStudio: LM Studio connected successfully {modelCount: 1, models: Array(1)}
ServiceLogger.ts:135 ℹ️ 17:30:08 [LocalModelService] checkLMStudio: Operation completed successfully: checkLMStudio 
ServiceLogger.ts:138 ⚠️ 17:30:08 [LocalModelService] checkLMStudio: Timer not found for operation: checkLMStudio 
outputToConsole @ ServiceLogger.ts:138
log @ ServiceLogger.ts:112
warn @ ServiceLogger.ts:56
endTimer @ ServiceLogger.ts:236
measureAsync @ ServiceLogger.ts:262
await in measureAsync
executeOperation @ BaseService.ts:213
checkLMStudio @ localModelService.ts:132
getAllLocalModels @ localModelService.ts:186
checkLocalModels @ index.ts:1308
await in checkLocalModels
initializeApp @ index.ts:1375
index.ts:1310 📊 Provider Status: {ollama: {…}, lmstudio: {…}}
index.ts:1314 🤖 All Local Models: (5) [{…}, {…}, {…}, {…}, {…}]
index.ts:1324 ✅ Local model check complete. Found 5 local models. Total external models: 318
ServiceLogger.ts:135 ℹ️ 17:30:28 [LocalModelService] sendOllamaMessage: Ollama response received {model: 'gemma3', responseLength: 1239}
ServiceLogger.ts:135 ℹ️ 17:30:28 [LocalModelService] sendMessage: Operation completed successfully: sendMessage {modelId: 'ollama:gemma3:latest', messageCount: 1, hasChunkCallback: false}
ServiceLogger.ts:135 ℹ️ 17:30:28 [LocalModelService] sendMessage: Operation completed in 19764ms: sendMessage {duration: 19764}
fileAnalysisService.ts:212 [LABELS] 🤖 📝 ===== RAW LLM RESPONSE ANALYSIS =====
fileAnalysisService.ts:213 [LABELS] 🤖 📝 Response type: string
fileAnalysisService.ts:214 [LABELS] 🤖 📝 Response length: 1239
fileAnalysisService.ts:217 [LABELS] 🤖 📝 FULL RESPONSE:
fileAnalysisService.ts:218 ---START---
fileAnalysisService.ts:219 ```markdown
!-- FILE_INTEL:BEGIN --
## Key Ideas (1–4 words each)
- "Project Management" ; score=99; intents=topic,action; context=long-term strategy; entities=Agile,Scrum
- "Risk Assessment" ; score=97; intents=knowledge,action; context=strategic planning; entities=Probability,Impact
- "Stakeholder Engagement" ; score=95; intents=connection,action; context=communication channels; entities=Feedback,Meetings
- "Resource Allocation" ; score=92; intents=knowledge,action; context=budget management; entities=Personnel,Equipment
- "Performance Metrics" ; score=88; intents=topic,knowledge; context=KPIs,measurement; entities=ROI,Efficiency
- "Change Management" ; score=85; intents=topic,action; context=transition process; entities=Resistance,Adoption
- "Strategic Alignment" ; score=82; intents=connection,knowledge; context=business goals; entities=Vision,Mission
- "Operational Efficiency" ; score=79; intents=topic,knowledge; context=process optimization; entities=Lean,Six Sigma
- "Data Analysis" ; score=76; intents=knowledge,action; context=insights generation; entities=Statistics,Trends
- "Innovation Pipeline" ; score=73; intents=topic,action; context=new product development; entities=Ideation,Testing
--! FILE_INTEL:END --
```
fileAnalysisService.ts:220 ---END---
fileAnalysisService.ts:227 [LABELS] 🤖 📝 DEBUG: Raw response written to: C:\Users\<USER>\Documents\Test20\debug_llm_response.txt
fileAnalysisService.ts:239 [LABELS] 🤖 📝 Format analysis: {hasFileIntelBlock: true, hasJsonFence: true, hasJsonObject: false, hasKeyIdeas: false, isEmpty: false}
fileAnalysisService.ts:249 [LABELS] 🤖 📝 First 10 lines:
fileAnalysisService.ts:251 [LABELS] 🤖 📝 Line 1: "```markdown"
fileAnalysisService.ts:251 [LABELS] 🤖 📝 Line 2: "!-- FILE_INTEL:BEGIN --"
fileAnalysisService.ts:251 [LABELS] 🤖 📝 Line 3: "## Key Ideas (1–4 words each)"
fileAnalysisService.ts:251 [LABELS] 🤖 📝 Line 4: "- "Project Management" ; score=99; intents=topic,action; context=long-term strategy; entities=Agile,Scrum"
fileAnalysisService.ts:251 [LABELS] 🤖 📝 Line 5: "- "Risk Assessment" ; score=97; intents=knowledge,action; context=strategic planning; entities=Probability,Impact"
fileAnalysisService.ts:251 [LABELS] 🤖 📝 Line 6: "- "Stakeholder Engagement" ; score=95; intents=connection,action; context=communication channels; entities=Feedback,Meetings"
fileAnalysisService.ts:251 [LABELS] 🤖 📝 Line 7: "- "Resource Allocation" ; score=92; intents=knowledge,action; context=budget management; entities=Personnel,Equipment"
fileAnalysisService.ts:251 [LABELS] 🤖 📝 Line 8: "- "Performance Metrics" ; score=88; intents=topic,knowledge; context=KPIs,measurement; entities=ROI,Efficiency"
fileAnalysisService.ts:251 [LABELS] 🤖 📝 Line 9: "- "Change Management" ; score=85; intents=topic,action; context=transition process; entities=Resistance,Adoption"
fileAnalysisService.ts:251 [LABELS] 🤖 📝 Line 10: "- "Strategic Alignment" ; score=82; intents=connection,knowledge; context=business goals; entities=Vision,Mission"
fileAnalysisService.ts:259 [LABELS] 🤖 📝 ===== END RESPONSE ANALYSIS =====
ServiceLogger.ts:135 ℹ️ 17:30:28 [FileAnalysisService] extractKeyIdeasWithLocalModel: Received LLM response {length: 1239, responseType: 'string'}
fileAnalysisService.ts:334 [LABELS] 🤖 📝 Parsing AI model response...
fileAnalysisService.ts:335 [LABELS] 🤖 📝 Response length: 1239
fileAnalysisService.ts:336 [LABELS] 🤖 📝 Response preview: ```markdown
!-- FILE_INTEL:BEGIN --
## Key Ideas (1–4 words each)
- "Project Management" ; score=99; intents=topic,action; context=long-term strategy; entities=Agile,Scrum
- "Risk Assessment" ; score=97; intents=knowledge,action; context=strategic planning; entities=Probability,Impact
- "Stakeholder Engagement" ; score=95; intents=connection,action; context=communication channels; entities=Feedback,Meetings
- "Resource Allocation" ; score=92; intents=knowledge,action; context=budget management; 
fileAnalysisService.ts:340 [LABELS] 🤖 📝 Using NEW LLM Response Parser...
llmResponseParser.ts:30 [LLM-PARSER] 🚀 Starting LLM response parsing...
llmResponseParser.ts:31 [LLM-PARSER] 📝 Response length: 1239
llmResponseParser.ts:76 [LLM-PARSER] 🔍 Attempting FILE_INTEL format parsing...
llmResponseParser.ts:93 [LLM-PARSER] ⚠️ No FILE_INTEL:END marker found, looking for alternative end points...
llmResponseParser.ts:105 [LLM-PARSER] 🔧 Using alternative end point at index: 1236
llmResponseParser.ts:110 [LLM-PARSER] 📄 Extracted content length: 1201
llmResponseParser.ts:121 [LLM-PARSER] 📋 Total lines found: 12
llmResponseParser.ts:164 [LLM-PARSER] 🔍 Parsing line: - "Project Management" ; score=99; intents=topic,action; context=long-term strategy; entities=Agile,Scrum
llmResponseParser.ts:216 [LLM-PARSER] 📊 Extracted: {text: 'Project Management', pairs: {…}}
llmResponseParser.ts:138 [LLM-PARSER] ✅ Parsed idea: Project Management (score: 99)
llmResponseParser.ts:164 [LLM-PARSER] 🔍 Parsing line: - "Risk Assessment" ; score=97; intents=knowledge,action; context=strategic planning; entities=Probability,Impact
llmResponseParser.ts:216 [LLM-PARSER] 📊 Extracted: {text: 'Risk Assessment', pairs: {…}}
llmResponseParser.ts:138 [LLM-PARSER] ✅ Parsed idea: Risk Assessment (score: 97)
llmResponseParser.ts:164 [LLM-PARSER] 🔍 Parsing line: - "Stakeholder Engagement" ; score=95; intents=connection,action; context=communication channels; entities=Feedback,Meetings
llmResponseParser.ts:216 [LLM-PARSER] 📊 Extracted: {text: 'Stakeholder Engagement', pairs: {…}}
llmResponseParser.ts:138 [LLM-PARSER] ✅ Parsed idea: Stakeholder Engagement (score: 95)
llmResponseParser.ts:164 [LLM-PARSER] 🔍 Parsing line: - "Resource Allocation" ; score=92; intents=knowledge,action; context=budget management; entities=Personnel,Equipment
llmResponseParser.ts:216 [LLM-PARSER] 📊 Extracted: {text: 'Resource Allocation', pairs: {…}}
llmResponseParser.ts:138 [LLM-PARSER] ✅ Parsed idea: Resource Allocation (score: 92)
llmResponseParser.ts:164 [LLM-PARSER] 🔍 Parsing line: - "Performance Metrics" ; score=88; intents=topic,knowledge; context=KPIs,measurement; entities=ROI,Efficiency
llmResponseParser.ts:216 [LLM-PARSER] 📊 Extracted: {text: 'Performance Metrics', pairs: {…}}
llmResponseParser.ts:138 [LLM-PARSER] ✅ Parsed idea: Performance Metrics (score: 88)
llmResponseParser.ts:164 [LLM-PARSER] 🔍 Parsing line: - "Change Management" ; score=85; intents=topic,action; context=transition process; entities=Resistance,Adoption
llmResponseParser.ts:216 [LLM-PARSER] 📊 Extracted: {text: 'Change Management', pairs: {…}}
llmResponseParser.ts:138 [LLM-PARSER] ✅ Parsed idea: Change Management (score: 85)
llmResponseParser.ts:164 [LLM-PARSER] 🔍 Parsing line: - "Strategic Alignment" ; score=82; intents=connection,knowledge; context=business goals; entities=Vision,Mission
llmResponseParser.ts:216 [LLM-PARSER] 📊 Extracted: {text: 'Strategic Alignment', pairs: {…}}
llmResponseParser.ts:138 [LLM-PARSER] ✅ Parsed idea: Strategic Alignment (score: 82)
llmResponseParser.ts:164 [LLM-PARSER] 🔍 Parsing line: - "Operational Efficiency" ; score=79; intents=topic,knowledge; context=process optimization; entities=Lean,Six Sigma
llmResponseParser.ts:216 [LLM-PARSER] 📊 Extracted: {text: 'Operational Efficiency', pairs: {…}}
llmResponseParser.ts:138 [LLM-PARSER] ✅ Parsed idea: Operational Efficiency (score: 79)
llmResponseParser.ts:164 [LLM-PARSER] 🔍 Parsing line: - "Data Analysis" ; score=76; intents=knowledge,action; context=insights generation; entities=Statistics,Trends
llmResponseParser.ts:216 [LLM-PARSER] 📊 Extracted: {text: 'Data Analysis', pairs: {…}}
llmResponseParser.ts:138 [LLM-PARSER] ✅ Parsed idea: Data Analysis (score: 76)
llmResponseParser.ts:164 [LLM-PARSER] 🔍 Parsing line: - "Innovation Pipeline" ; score=73; intents=topic,action; context=new product development; entities=Ideation,Testing
llmResponseParser.ts:216 [LLM-PARSER] 📊 Extracted: {text: 'Innovation Pipeline', pairs: {…}}
llmResponseParser.ts:138 [LLM-PARSER] ✅ Parsed idea: Innovation Pipeline (score: 73)
llmResponseParser.ts:143 [LLM-PARSER] 🎯 FILE_INTEL parsing complete: 10 ideas extracted
llmResponseParser.ts:37 [LLM-PARSER] ✅ Successfully parsed FILE_INTEL format
fileAnalysisService.ts:349 [LABELS] 🤖 📝 ✅ NEW PARSER successful! Extracted 10 ideas
fileAnalysisService.ts:350 [LABELS] 🤖 📝 ✅ Debug info: {format_detected: 'file_intel', lines_processed: 10, raw_response_length: 1239, has_file_intel_block: true, has_end_marker: false}
ServiceLogger.ts:135 ℹ️ 17:30:28 [FileAnalysisService] parseAndValidateKeyIdeas: Successfully parsed key ideas from AI model {totalIdeas: 10, autoSelected: 3, avgRelevance: 86.6}
fileAnalysisService.ts:391 [LLM-Parse] ideas (top 10): (10) ['Project Management (99)', 'Risk Assessment (97)', 'Stakeholder Engagement (95)', 'Resource Allocation (92)', 'Performance Metrics (88)', 'Change Management (85)', 'Strategic Alignment (82)', 'Operational Efficiency (79)', 'Data Analysis (76)', 'Innovation Pipeline (73)']
fileAnalysisService.ts:75 [LABELS] 🤖 ✅ Local model analysis successful, extracted 10 ideas
ServiceLogger.ts:135 ℹ️ 17:30:28 [FileAnalysisService] analyzeDocument: Document analysis completed {ideasExtracted: 10, entitiesFound: 30, humanConnections: 22, confidence: 1, processingTime: 20146}
ServiceLogger.ts:135 ℹ️ 17:30:28 [FileAnalysisService] analyzeDocument: Operation completed successfully: analyzeDocument {contentLength: 5246}
ServiceLogger.ts:135 ℹ️ 17:30:28 [FileAnalysisService] analyzeDocument: Operation completed in 20146ms: analyzeDocument {duration: 20146}
fileAnalysisService.ts:1009 [LABELS] 🤖 ✅ Document analysis completed successfully
fileAnalysisService.ts:1049 [LABELS] 🤖 Storing intelligence data...
fileAnalysisService.ts:1050 [LABELS] 🤖 fileIntelligence.file_path: C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/documents/modelUpdate-externalization.md
fileAnalysisService.ts:1051 [LABELS] 🤖 fileIntelligence.key_ideas.length: 10
storageServiceAdapter.ts:45 [STORAGE-ADAPTER] 🔄 Converting legacy call to new storage system
storageServiceAdapter.ts:46 [STORAGE-ADAPTER] 📁 Original file path: C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/documents/modelUpdate-externalization.md
storageServiceAdapter.ts:351 [STORAGE-ADAPTER] 🔄 Converting FileIntelligence to IntelligenceData
storageServiceAdapter.ts:352 [STORAGE-ADAPTER] 📊 Input key_ideas count: 10
storageServiceAdapter.ts:353 [STORAGE-ADAPTER] 📊 Sample key_ideas: (2) [{…}, {…}]
storageServiceAdapter.ts:376 [STORAGE-ADAPTER] ✅ Converted FileIntelligence to IntelligenceData
storageServiceAdapter.ts:377 [STORAGE-ADAPTER] 📊 Output labels count: 10
storageServiceAdapter.ts:378 [STORAGE-ADAPTER] 📊 Output key_ideas count: 10
storageServiceAdapter.ts:57 [STORAGE-ADAPTER] ⚠️ Kernel write failed, trying legacy alias save: Error: Error invoking remote method 'intelligence:write': Error: No handler registered for 'intelligence:write'
(anonymous) @ storageServiceAdapter.ts:57
await in (anonymous)
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
storeFileIntelligence @ storageServiceAdapter.ts:42
executeOperationOrThrow.filePath.filePath @ fileAnalysisService.ts:1053
await in executeOperationOrThrow.filePath.filePath
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
analyzeAndStoreDocument @ fileAnalysisService.ts:994
processFile @ fileProcessingQueue.ts:347
await in processFile
processNext @ fileProcessingQueue.ts:204
startProcessing @ fileProcessingQueue.ts:130
executeOperationOrThrow.filePath.filePath @ fileProcessingQueue.ts:113
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
addTask @ fileProcessingQueue.ts:85
executeOperationOrThrow.totalVaults @ batchFileProcessingService.ts:437
await in executeOperationOrThrow.totalVaults
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
processAllVaultsWithQueue @ batchFileProcessingService.ts:378
handleOrganize @ HomePage.tsx:176
executeDispatch @ react-dom_client.js?v=fe429164:11736
runWithFiberInDEV @ react-dom_client.js?v=fe429164:1485
processDispatchQueue @ react-dom_client.js?v=fe429164:11772
(anonymous) @ react-dom_client.js?v=fe429164:12182
batchedUpdates$1 @ react-dom_client.js?v=fe429164:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=fe429164:11877
dispatchEvent @ react-dom_client.js?v=fe429164:14792
dispatchDiscreteEvent @ react-dom_client.js?v=fe429164:14773
storageServiceAdapter.ts:351 [STORAGE-ADAPTER] 🔄 Converting FileIntelligence to IntelligenceData
storageServiceAdapter.ts:352 [STORAGE-ADAPTER] 📊 Input key_ideas count: 10
storageServiceAdapter.ts:353 [STORAGE-ADAPTER] 📊 Sample key_ideas: (2) [{…}, {…}]
storageServiceAdapter.ts:376 [STORAGE-ADAPTER] ✅ Converted FileIntelligence to IntelligenceData
storageServiceAdapter.ts:377 [STORAGE-ADAPTER] 📊 Output labels count: 10
storageServiceAdapter.ts:378 [STORAGE-ADAPTER] 📊 Output key_ideas count: 10
ServiceLogger.ts:138 ⚠️ 17:30:38 [FileProcessingQueue] handleTaskTimeout: Task timed out {taskId: 'task_1754760608336_pxvgof48d', fileName: 'modelUpdate-externalization.md', timeout: 30000}
outputToConsole @ ServiceLogger.ts:138
log @ ServiceLogger.ts:112
warn @ ServiceLogger.ts:56
handleTaskTimeout @ fileProcessingQueue.ts:532
(anonymous) @ fileProcessingQueue.ts:198
setTimeout
processNext @ fileProcessingQueue.ts:197
startProcessing @ fileProcessingQueue.ts:130
executeOperationOrThrow.filePath.filePath @ fileProcessingQueue.ts:113
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
addTask @ fileProcessingQueue.ts:85
executeOperationOrThrow.totalVaults @ batchFileProcessingService.ts:437
await in executeOperationOrThrow.totalVaults
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
processAllVaultsWithQueue @ batchFileProcessingService.ts:378
handleOrganize @ HomePage.tsx:176
executeDispatch @ react-dom_client.js?v=fe429164:11736
runWithFiberInDEV @ react-dom_client.js?v=fe429164:1485
processDispatchQueue @ react-dom_client.js?v=fe429164:11772
(anonymous) @ react-dom_client.js?v=fe429164:12182
batchedUpdates$1 @ react-dom_client.js?v=fe429164:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=fe429164:11877
dispatchEvent @ react-dom_client.js?v=fe429164:14792
dispatchDiscreteEvent @ react-dom_client.js?v=fe429164:14773
ServiceLogger.ts:138 ⚠️ 17:30:38 [FileProcessingQueue] handleTaskError: Task failed {taskId: 'task_1754760608336_pxvgof48d', fileName: 'modelUpdate-externalization.md', attempt: 1, maxRetries: 3, error: 'Task timed out after 30000ms'}
outputToConsole @ ServiceLogger.ts:138
log @ ServiceLogger.ts:112
warn @ ServiceLogger.ts:56
handleTaskError @ fileProcessingQueue.ts:478
handleTaskTimeout @ fileProcessingQueue.ts:536
(anonymous) @ fileProcessingQueue.ts:198
setTimeout
processNext @ fileProcessingQueue.ts:197
startProcessing @ fileProcessingQueue.ts:130
executeOperationOrThrow.filePath.filePath @ fileProcessingQueue.ts:113
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
addTask @ fileProcessingQueue.ts:85
executeOperationOrThrow.totalVaults @ batchFileProcessingService.ts:437
await in executeOperationOrThrow.totalVaults
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
processAllVaultsWithQueue @ batchFileProcessingService.ts:378
handleOrganize @ HomePage.tsx:176
executeDispatch @ react-dom_client.js?v=fe429164:11736
runWithFiberInDEV @ react-dom_client.js?v=fe429164:1485
processDispatchQueue @ react-dom_client.js?v=fe429164:11772
(anonymous) @ react-dom_client.js?v=fe429164:12182
batchedUpdates$1 @ react-dom_client.js?v=fe429164:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=fe429164:11877
dispatchEvent @ react-dom_client.js?v=fe429164:14792
dispatchDiscreteEvent @ react-dom_client.js?v=fe429164:14773
ServiceLogger.ts:135 ℹ️ 17:30:38 [FileProcessingQueue] handleTaskError: Task queued for retry {taskId: 'task_1754760608336_pxvgof48d', fileName: 'modelUpdate-externalization.md', retryCount: 1}
storageServiceAdapter.ts:65 [STORAGE-ADAPTER] ⚠️ Kernel save alias failed, falling back to RelativeStorageService: Error: Error invoking remote method 'intelligence:save': Error: No handler registered for 'intelligence:save'
(anonymous) @ storageServiceAdapter.ts:65
await in (anonymous)
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
storeFileIntelligence @ storageServiceAdapter.ts:42
executeOperationOrThrow.filePath.filePath @ fileAnalysisService.ts:1053
await in executeOperationOrThrow.filePath.filePath
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
analyzeAndStoreDocument @ fileAnalysisService.ts:994
processFile @ fileProcessingQueue.ts:347
await in processFile
processNext @ fileProcessingQueue.ts:204
startProcessing @ fileProcessingQueue.ts:130
executeOperationOrThrow.filePath.filePath @ fileProcessingQueue.ts:113
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
addTask @ fileProcessingQueue.ts:85
executeOperationOrThrow.totalVaults @ batchFileProcessingService.ts:437
await in executeOperationOrThrow.totalVaults
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
processAllVaultsWithQueue @ batchFileProcessingService.ts:378
handleOrganize @ HomePage.tsx:176
executeDispatch @ react-dom_client.js?v=fe429164:11736
runWithFiberInDEV @ react-dom_client.js?v=fe429164:1485
processDispatchQueue @ react-dom_client.js?v=fe429164:11772
(anonymous) @ react-dom_client.js?v=fe429164:12182
batchedUpdates$1 @ react-dom_client.js?v=fe429164:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=fe429164:11877
dispatchEvent @ react-dom_client.js?v=fe429164:14792
dispatchDiscreteEvent @ react-dom_client.js?v=fe429164:14773
storageServiceAdapter.ts:243 [STORAGE-ADAPTER] 🔄 Converting legacy path to context
storageServiceAdapter.ts:244 [STORAGE-ADAPTER] 📁 Input file path: C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/documents/modelUpdate-externalization.md
storageServiceAdapter.ts:245 [STORAGE-ADAPTER] 📁 Input vault path: C:\Users\<USER>\Documents\Test20\personal-vault
storageServiceAdapter.ts:252 [STORAGE-ADAPTER] 🧹 Removed hardcoded prefix, clean path: personal-vault/getting-started/documents/modelUpdate-externalization.md
storageServiceAdapter.ts:260 [STORAGE-ADAPTER] 📂 Path parts: (4) ['personal-vault', 'getting-started', 'documents', 'modelUpdate-externalization.md']
storageServiceAdapter.ts:291 [STORAGE-ADAPTER] ✅ Created context: {vaultId: 'personal-vault', contextId: 'getting-started', relativePath: 'documents/modelUpdate-externalization.md'}
storageServiceAdapter.ts:292 [STORAGE-ADAPTER] 🎯 No hardcoded paths in result ✅
storageServiceAdapter.ts:72 [STORAGE-ADAPTER] ✅ Converted to context: {vaultId: 'personal-vault', contextId: 'getting-started', relativePath: 'documents/modelUpdate-externalization.md'}
storageServiceAdapter.ts:351 [STORAGE-ADAPTER] 🔄 Converting FileIntelligence to IntelligenceData
storageServiceAdapter.ts:352 [STORAGE-ADAPTER] 📊 Input key_ideas count: 10
storageServiceAdapter.ts:353 [STORAGE-ADAPTER] 📊 Sample key_ideas: (2) [{…}, {…}]
storageServiceAdapter.ts:376 [STORAGE-ADAPTER] ✅ Converted FileIntelligence to IntelligenceData
storageServiceAdapter.ts:377 [STORAGE-ADAPTER] 📊 Output labels count: 10
storageServiceAdapter.ts:378 [STORAGE-ADAPTER] 📊 Output key_ideas count: 10
relativeStorageService.ts:71 [RELATIVE-STORAGE] 💾 Storing intelligence for context: getting-started
relativeStorageService.ts:79 [RELATIVE-STORAGE] 💾 Storage path: personal-vault/getting-started/.context/files/70f190a4.json
relativeStorageService.ts:80 [RELATIVE-STORAGE] 💾 No hardcoded paths detected ✅
relativeStorageService.ts:352 [RELATIVE-STORAGE] 📁 Storage directory path: personal-vault/getting-started/.context/files
relativeStorageService.ts:363 [RELATIVE-STORAGE] 💾 Writing intelligence file...
relativeStorageService.ts:364 [RELATIVE-STORAGE] 💾 Relative storage path: personal-vault/getting-started/.context/files/70f190a4.json
relativeStorageService.ts:318 [RELATIVE-STORAGE] ⚠️ Could not get vault root, using relative path as-is
relativeStorageService.ts:368 [RELATIVE-STORAGE] 💾 Absolute storage path: personal-vault/getting-started/.context/files/70f190a4.json
relativeStorageService.ts:370 [RELATIVE-STORAGE] 💾 Intelligence data preview: {document_hash: '70f190a4', file_path: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault/getting-started/documents/modelUpdate-externalization.md', analysis_timestamp: '2025-08-09T17:30:28.494Z', labels_count: 10, key_points_count: 10}
relativeStorageService.ts:389 [RELATIVE-STORAGE] 💾 JSON content length: 21944
relativeStorageService.ts:390 [RELATIVE-STORAGE] 💾 Calling vault.writeFile with absolute path...
relativeStorageService.ts:395 [RELATIVE-STORAGE] 💾 Vault writeFile result: {success: true}
relativeStorageService.ts:398 [RELATIVE-STORAGE] ✅ Intelligence file written successfully
relativeStorageService.ts:108 [RELATIVE-STORAGE] ✅ Intelligence stored successfully
ServiceLogger.ts:135 ℹ️ 17:30:40 [undefined] storeIntelligence: Operation completed successfully: storeIntelligence 
ServiceLogger.ts:135 ℹ️ 17:30:40 [undefined] storeIntelligence: Operation completed in 10ms: storeIntelligence {duration: 10}
storageServiceAdapter.ts:85 [STORAGE-ADAPTER] ✅ Successfully stored using new architecture
ServiceLogger.ts:135 ℹ️ 17:30:40 [undefined] storeFileIntelligence: Operation completed successfully: storeFileIntelligence 
ServiceLogger.ts:135 ℹ️ 17:30:40 [undefined] storeFileIntelligence: Operation completed in 12059ms: storeFileIntelligence {duration: 12059}
fileAnalysisService.ts:1058 [LABELS] 🤖 ✅ Intelligence data stored successfully
fileAnalysisService.ts:1061 [LABELS] 🤖 Updating vault intelligence...
fileAnalysisService.ts:1063 [LABELS] 🤖 ⚠️ Vault intelligence update temporarily disabled during migration
fileAnalysisService.ts:1064 [LABELS] 🤖 ✅ Vault intelligence updated
ServiceLogger.ts:135 ℹ️ 17:30:40 [FileAnalysisService] analyzeAndStoreDocument: Document analyzed and stored successfully {filePath: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault/getting-started/documents/modelUpdate-externalization.md', ideasExtracted: 10, entitiesFound: 30, humanConnections: 22}
ServiceLogger.ts:135 ℹ️ 17:30:40 [FileAnalysisService] analyzeAndStoreDocument: Operation completed successfully: analyzeAndStoreDocument {filePath: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault/getting-started/documents/modelUpdate-externalization.md', vaultPath: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault', contentLength: 5246}
ServiceLogger.ts:135 ℹ️ 17:30:40 [FileAnalysisService] analyzeAndStoreDocument: Operation completed in 32208ms: analyzeAndStoreDocument {duration: 32208}
fileProcessingQueue.ts:352 ✅ [QUEUE-PROCESS] Document analysis complete for: modelUpdate-externalization.md
ServiceLogger.ts:138 ⚠️ 17:30:40 [PerformanceMonitor] recordProcessingTime: Operation exceeded processing budget {operationType: 'batch', processingTime: 32217, budget: 200, exceeded: 32017}
outputToConsole @ ServiceLogger.ts:138
log @ ServiceLogger.ts:112
warn @ ServiceLogger.ts:56
recordProcessingTime @ performanceMonitor.ts:243
processFile @ fileProcessingQueue.ts:355
await in processFile
processNext @ fileProcessingQueue.ts:204
startProcessing @ fileProcessingQueue.ts:130
executeOperationOrThrow.filePath.filePath @ fileProcessingQueue.ts:113
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
addTask @ fileProcessingQueue.ts:85
executeOperationOrThrow.totalVaults @ batchFileProcessingService.ts:437
await in executeOperationOrThrow.totalVaults
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
processAllVaultsWithQueue @ batchFileProcessingService.ts:378
handleOrganize @ HomePage.tsx:176
executeDispatch @ react-dom_client.js?v=fe429164:11736
runWithFiberInDEV @ react-dom_client.js?v=fe429164:1485
processDispatchQueue @ react-dom_client.js?v=fe429164:11772
(anonymous) @ react-dom_client.js?v=fe429164:12182
batchedUpdates$1 @ react-dom_client.js?v=fe429164:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=fe429164:11877
dispatchEvent @ react-dom_client.js?v=fe429164:14792
dispatchDiscreteEvent @ react-dom_client.js?v=fe429164:14773
cacheManager.ts:293 Failed to compress data for cold cache: file_processing_task_1754760608336_pxvgof48d_modelUpdate-externalization.md InvalidCharacterError: Failed to execute 'btoa' on 'Window': The string to be encoded contains characters outside of the Latin1 range.
    at CacheManager.set (cacheManager.ts:290:32)
    at async FileProcessingQueue.processFile (fileProcessingQueue.ts:358:7)
    at async FileProcessingQueue.processNext (fileProcessingQueue.ts:204:22)
set @ cacheManager.ts:293
await in set
processFile @ fileProcessingQueue.ts:358
await in processFile
processNext @ fileProcessingQueue.ts:204
startProcessing @ fileProcessingQueue.ts:130
executeOperationOrThrow.filePath.filePath @ fileProcessingQueue.ts:113
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
addTask @ fileProcessingQueue.ts:85
executeOperationOrThrow.totalVaults @ batchFileProcessingService.ts:437
await in executeOperationOrThrow.totalVaults
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
processAllVaultsWithQueue @ batchFileProcessingService.ts:378
handleOrganize @ HomePage.tsx:176
executeDispatch @ react-dom_client.js?v=fe429164:11736
runWithFiberInDEV @ react-dom_client.js?v=fe429164:1485
processDispatchQueue @ react-dom_client.js?v=fe429164:11772
(anonymous) @ react-dom_client.js?v=fe429164:12182
batchedUpdates$1 @ react-dom_client.js?v=fe429164:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=fe429164:11877
dispatchEvent @ react-dom_client.js?v=fe429164:14792
dispatchDiscreteEvent @ react-dom_client.js?v=fe429164:14773
cacheManager.ts:299 💾 [CACHE] Cached data: file_processing_task_1754760608336_pxvgof48d_modelUpdate-externalization.md (31KB)
fileProcessingQueue.ts:359 💾 [QUEUE] Cached processing result for: modelUpdate-externalization.md
intelligenceAnalytics.ts:116 Analytics event tracked: processing_time {operationType: 'batch', processingTime: 32217, success: false, timestamp: '2025-08-09T17:30:40.558Z'}
performanceMonitor.ts:221 ✅ [PERFORMANCE] Can process batch: JS heap 28MB, System 58.3%
ServiceLogger.ts:135 ℹ️ 17:30:40 [FileProcessingQueue] processNext: Processing task started {taskId: 'task_1754760608336_pxvgof48d', fileName: 'modelUpdate-externalization.md', attempt: 2}
cacheManager.ts:219 🔥 [CACHE] Hot cache hit: file_processing_task_1754760608336_pxvgof48d_modelUpdate-externalization.md
fileProcessingQueue.ts:226 🔥 [QUEUE] Cache hit for file processing: modelUpdate-externalization.md
ServiceLogger.ts:135 ℹ️ 17:30:40 [FileProcessingQueue] handleTaskCompletion: Task completed successfully {taskId: 'task_1754760608336_pxvgof48d', fileName: 'modelUpdate-externalization.md', processingTime: 1}
performanceMonitor.ts:202 CPU usage too high (100%) for batch processing
canProcess @ performanceMonitor.ts:202
processNext @ fileProcessingQueue.ts:166
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:211
setTimeout
processNext @ fileProcessingQueue.ts:211
await in processNext
(anonymous) @ fileProcessingQueue.ts:211
setTimeout
processNext @ fileProcessingQueue.ts:211
await in processNext
startProcessing @ fileProcessingQueue.ts:130
executeOperationOrThrow.filePath.filePath @ fileProcessingQueue.ts:113
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
addTask @ fileProcessingQueue.ts:85
executeOperationOrThrow.totalVaults @ batchFileProcessingService.ts:437
await in executeOperationOrThrow.totalVaults
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
processAllVaultsWithQueue @ batchFileProcessingService.ts:378
handleOrganize @ HomePage.tsx:176
executeDispatch @ react-dom_client.js?v=fe429164:11736
runWithFiberInDEV @ react-dom_client.js?v=fe429164:1485
processDispatchQueue @ react-dom_client.js?v=fe429164:11772
(anonymous) @ react-dom_client.js?v=fe429164:12182
batchedUpdates$1 @ react-dom_client.js?v=fe429164:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=fe429164:11877
dispatchEvent @ react-dom_client.js?v=fe429164:14792
dispatchDiscreteEvent @ react-dom_client.js?v=fe429164:14773
performanceMonitor.ts:202 CPU usage too high (100%) for batch processing
canProcess @ performanceMonitor.ts:202
processNext @ fileProcessingQueue.ts:166
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:211
setTimeout
processNext @ fileProcessingQueue.ts:211
await in processNext
(anonymous) @ fileProcessingQueue.ts:211
setTimeout
processNext @ fileProcessingQueue.ts:211
await in processNext
startProcessing @ fileProcessingQueue.ts:130
executeOperationOrThrow.filePath.filePath @ fileProcessingQueue.ts:113
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
addTask @ fileProcessingQueue.ts:85
executeOperationOrThrow.totalVaults @ batchFileProcessingService.ts:437
await in executeOperationOrThrow.totalVaults
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
processAllVaultsWithQueue @ batchFileProcessingService.ts:378
handleOrganize @ HomePage.tsx:176
executeDispatch @ react-dom_client.js?v=fe429164:11736
runWithFiberInDEV @ react-dom_client.js?v=fe429164:1485
processDispatchQueue @ react-dom_client.js?v=fe429164:11772
(anonymous) @ react-dom_client.js?v=fe429164:12182
batchedUpdates$1 @ react-dom_client.js?v=fe429164:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=fe429164:11877
dispatchEvent @ react-dom_client.js?v=fe429164:14792
dispatchDiscreteEvent @ react-dom_client.js?v=fe429164:14773
performanceMonitor.ts:202 CPU usage too high (100%) for batch processing
canProcess @ performanceMonitor.ts:202
processNext @ fileProcessingQueue.ts:166
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:211
setTimeout
processNext @ fileProcessingQueue.ts:211
await in processNext
(anonymous) @ fileProcessingQueue.ts:211
setTimeout
processNext @ fileProcessingQueue.ts:211
await in processNext
startProcessing @ fileProcessingQueue.ts:130
executeOperationOrThrow.filePath.filePath @ fileProcessingQueue.ts:113
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
addTask @ fileProcessingQueue.ts:85
executeOperationOrThrow.totalVaults @ batchFileProcessingService.ts:437
await in executeOperationOrThrow.totalVaults
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
processAllVaultsWithQueue @ batchFileProcessingService.ts:378
handleOrganize @ HomePage.tsx:176
executeDispatch @ react-dom_client.js?v=fe429164:11736
runWithFiberInDEV @ react-dom_client.js?v=fe429164:1485
processDispatchQueue @ react-dom_client.js?v=fe429164:11772
(anonymous) @ react-dom_client.js?v=fe429164:12182
batchedUpdates$1 @ react-dom_client.js?v=fe429164:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=fe429164:11877
dispatchEvent @ react-dom_client.js?v=fe429164:14792
dispatchDiscreteEvent @ react-dom_client.js?v=fe429164:14773
performanceMonitor.ts:202 CPU usage too high (100%) for batch processing
canProcess @ performanceMonitor.ts:202
processNext @ fileProcessingQueue.ts:166
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:174
setTimeout
processNext @ fileProcessingQueue.ts:174
(anonymous) @ fileProcessingQueue.ts:211
setTimeout
processNext @ fileProcessingQueue.ts:211
await in processNext
(anonymous) @ fileProcessingQueue.ts:211
setTimeout
processNext @ fileProcessingQueue.ts:211
await in processNext
startProcessing @ fileProcessingQueue.ts:130
executeOperationOrThrow.filePath.filePath @ fileProcessingQueue.ts:113
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
addTask @ fileProcessingQueue.ts:85
executeOperationOrThrow.totalVaults @ batchFileProcessingService.ts:437
await in executeOperationOrThrow.totalVaults
wrapServiceOperation @ ServiceError.ts:191
(anonymous) @ BaseService.ts:214
measureAsync @ ServiceLogger.ts:261
executeOperation @ BaseService.ts:213
executeOperationOrThrow @ BaseService.ts:234
processAllVaultsWithQueue @ batchFileProcessingService.ts:378
handleOrganize @ HomePage.tsx:176
executeDispatch @ react-dom_client.js?v=fe429164:11736
runWithFiberInDEV @ react-dom_client.js?v=fe429164:1485
processDispatchQueue @ react-dom_client.js?v=fe429164:11772
(anonymous) @ react-dom_client.js?v=fe429164:12182
batchedUpdates$1 @ react-dom_client.js?v=fe429164:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=fe429164:11877
dispatchEvent @ react-dom_client.js?v=fe429164:14792
dispatchDiscreteEvent @ react-dom_client.js?v=fe429164:14773
performanceMonitor.ts:221 ✅ [PERFORMANCE] Can process batch: JS heap 28MB, System 57.4%
performanceMonitor.ts:221 ✅ [PERFORMANCE] Can process batch: JS heap 28MB, System 58.5%
performanceMonitor.ts:221 ✅ [PERFORMANCE] Can process batch: JS heap 28MB, System 58.4%
performanceMonitor.ts:221 ✅ [PERFORMANCE] Can process batch: JS heap 28MB, System 58.6%
performanceMonitor.ts:221 ✅ [PERFORMANCE] Can process batch: JS heap 28MB, System 58.7%
