vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\.context\metadata.json
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\master.md
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\.context\metadata.json
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\master.md
fileAnalysisService.ts:993 [LABELS] 🤖 analyzeAndStoreDocument called
fileAnalysisService.ts:994 [LABELS] 🤖 filePath: C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/documents/modelUpdate-externalization.md
fileAnalysisService.ts:995 [LABELS] 🤖 vaultPath: C:\Users\<USER>\Documents\Test20\personal-vault
fileAnalysisService.ts:996 [LABELS] 🤖 content length: 5246
fileAnalysisService.ts:1003 [LABELS] 🤖 Starting document analysis...
fileAnalysisService.ts:72 [LABELS] 🤖 Attempting local model analysis...
fileAnalysisService.ts:137 [LABELS] 🔍 ===== SYSTEMATIC SERVICE CHECK =====
fileAnalysisService.ts:138 [LABELS] 🔍 1. Checking local model service health...
fileAnalysisService.ts:143 [LABELS] 🔍 Provider Status: {ollama: {…}, lmstudio: {…}}
fileAnalysisService.ts:167 [LABELS] 🔍 2. Checking model availability...
fileAnalysisService.ts:168 [LABELS] 🔍 Requested model: ollama:gemma3:latest
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\.context\metadata.json
fileAnalysisService.ts:171 [LABELS] 🔍 Available models: (5) [{…}, {…}, {…}, {…}, {…}]
fileAnalysisService.ts:193 [LABELS] 🔍 ✅ Model found: gemma3:latest
fileAnalysisService.ts:194 [LABELS] 🔍 ===== SERVICE CHECK COMPLETE =====
fileAnalysisService.ts:195 [LABELS] 🤖 Using model: gemma3:latest
fileAnalysisService.ts:201 [LABELS] 🤖 📡 Sending request to local model: ollama:gemma3:latest
fileAnalysisService.ts:202 [LABELS] 🤖 📡 Prompt preview: You are a precise extractor. Do NOT explain. Output exactly ONE fenced Markdown block, nothing else.

Task
- Read the document between DOC and DOC.
- Produce 10 key ideas as 1–4 word noun phrases (no verbs, no punctuation).
- Be tolerant: if the doc is short, output as many as you can (≥3).
- Use on...
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\master.md
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\.context\metadata.json
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\master.md
fileAnalysisService.ts:211 [LABELS] 🤖 📝 ===== RAW LLM RESPONSE ANALYSIS =====
fileAnalysisService.ts:212 [LABELS] 🤖 📝 Response type: string
fileAnalysisService.ts:213 [LABELS] 🤖 📝 Response length: 1239
fileAnalysisService.ts:216 [LABELS] 🤖 📝 FULL RESPONSE:
fileAnalysisService.ts:226 [LABELS] 🤖 📝 DEBUG: Raw response written to: C:\Users\<USER>\Documents\Test20\debug_llm_response.txt
fileAnalysisService.ts:238 [LABELS] 🤖 📝 Format analysis: {hasFileIntelBlock: true, hasJsonFence: true, hasJsonObject: false, hasKeyIdeas: false, isEmpty: false}
fileAnalysisService.ts:248 [LABELS] 🤖 📝 First 10 lines:
fileAnalysisService.ts:250 [LABELS] 🤖 📝 Line 1: "```markdown"
fileAnalysisService.ts:250 [LABELS] 🤖 📝 Line 2: "!-- FILE_INTEL:BEGIN --"
fileAnalysisService.ts:250 [LABELS] 🤖 📝 Line 3: "## Key Ideas (1–4 words each)"
fileAnalysisService.ts:250 [LABELS] 🤖 📝 Line 4: "- "Project Management" ; score=99; intents=topic,action; context=long-term strategy; entities=Agile,Scrum"
fileAnalysisService.ts:250 [LABELS] 🤖 📝 Line 5: "- "Risk Assessment" ; score=97; intents=knowledge,action; context=strategic planning; entities=Probability,Impact"
fileAnalysisService.ts:250 [LABELS] 🤖 📝 Line 6: "- "Stakeholder Engagement" ; score=95; intents=connection,action; context=communication channels; entities=Feedback,Meetings"
fileAnalysisService.ts:250 [LABELS] 🤖 📝 Line 7: "- "Resource Allocation" ; score=92; intents=knowledge,action; context=budget management; entities=Personnel,Equipment"
fileAnalysisService.ts:250 [LABELS] 🤖 📝 Line 8: "- "Performance Metrics" ; score=88; intents=topic,knowledge; context=KPIs,measurement; entities=ROI,Efficiency"
fileAnalysisService.ts:250 [LABELS] 🤖 📝 Line 9: "- "Change Management" ; score=85; intents=topic,action; context=transition process; entities=Resistance,Adoption"
fileAnalysisService.ts:250 [LABELS] 🤖 📝 Line 10: "- "Strategic Alignment" ; score=82; intents=connection,knowledge; context=business goals; entities=Vision,Mission"
fileAnalysisService.ts:255 [LABELS] 🤖 📝 ===== END RESPONSE ANALYSIS =====
fileAnalysisService.ts:330 [LABELS] 🤖 📝 Parsing AI model response...
fileAnalysisService.ts:331 [LABELS] 🤖 📝 Response length: 1239
fileAnalysisService.ts:332 [LABELS] 🤖 📝 Response preview: ```markdown
!-- FILE_INTEL:BEGIN --
## Key Ideas (1–4 words each)
- "Project Management" ; score=99; intents=topic,action; context=long-term strategy; entities=Agile,Scrum
- "Risk Assessment" ; score=97; intents=knowledge,action; context=strategic planning; entities=Probability,Impact
- "Stakeholder Engagement" ; score=95; intents=connection,action; context=communication channels; entities=Feedback,Meetings
- "Resource Allocation" ; score=92; intents=knowledge,action; context=budget management; 
fileAnalysisService.ts:336 [LABELS] 🤖 📝 Using NEW LLM Response Parser...
fileAnalysisService.ts:345 [LABELS] 🤖 📝 ✅ NEW PARSER successful! Extracted 10 ideas
fileAnalysisService.ts:346 [LABELS] 🤖 📝 ✅ Debug info: {format_detected: 'file_intel', lines_processed: 10, raw_response_length: 1239, has_file_intel_block: true, has_end_marker: false}
fileAnalysisService.ts:74 [LABELS] 🤖 ✅ Local model analysis successful, extracted 10 ideas
fileAnalysisService.ts:1005 [LABELS] 🤖 ✅ Document analysis completed successfully
fileAnalysisService.ts:1045 [LABELS] 🤖 Storing intelligence data...
fileAnalysisService.ts:1046 [LABELS] 🤖 fileIntelligence.file_path: C:\Users\<USER>\Documents\Test20\personal-vault/getting-started/documents/modelUpdate-externalization.md
fileAnalysisService.ts:1047 [LABELS] 🤖 fileIntelligence.key_ideas.length: 10
relativeStorageService.ts:339 [RELATIVE-STORAGE] 💾 Intelligence data preview: {document_hash: '70f190a4', file_path: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault/getting-started/documents/modelUpdate-externalization.md', analysis_timestamp: '2025-08-09T10:49:21.300Z', labels_count: 10, key_points_count: 10}
fileAnalysisService.ts:1054 [LABELS] 🤖 ✅ Intelligence data stored successfully
fileAnalysisService.ts:1057 [LABELS] 🤖 Updating vault intelligence...
fileAnalysisService.ts:1059 [LABELS] 🤖 ⚠️ Vault intelligence update temporarily disabled during migration
fileAnalysisService.ts:1060 [LABELS] 🤖 ✅ Vault intelligence updated
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\master.md
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\master.md
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\master.md
vaultUIManager.ts:13 [LABELS] 🗂️ Using standardized joinLike from vaultPath.ts
vaultUIManager.ts:22 [LABELS] 🗂️ ✅ Path joined: C:\Users\<USER>\Documents\Test20\work-vault\project\master.md
FilePageOverlay.tsx:912 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:913 [LABELS] 🎯 OVERLAY: fileIntelligence: null
FilePageOverlay.tsx:914 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault\\getting-started\\documents\\modelUpdate-externalization.md', fileContentLength: 0, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:498 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:499 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:472 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:473 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:474 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:475 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:489 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:52 [LABELS] 🔄 SmartLabelingInterface: useEffect triggered
SmartLabelingInterface.tsx:53 [LABELS] 🔄 SmartLabelingInterface: filePath: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\documents\modelUpdate-externalization.md
SmartLabelingInterface.tsx:54 [LABELS] 🔄 SmartLabelingInterface: fileContent length: 0
SmartLabelingInterface.tsx:55 [LABELS] 🔄 SmartLabelingInterface: onLabelsChanged callback: function
SmartLabelingInterface.tsx:67 [LABELS] ⚠️ SmartLabelingInterface: Missing required data: {hasFileContent: false, fileContentLength: 0, hasFilePath: true}
FilePageOverlay.tsx:780 [LABELS] 🔄 OVERLAY: Resetting intelligence state for new file: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\documents\modelUpdate-externalization.md
FilePageOverlay.tsx:786 [LABELS] 🔄 OVERLAY: Intelligence state reset complete
FilePageOverlay.tsx:912 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:913 [LABELS] 🎯 OVERLAY: fileIntelligence: null
FilePageOverlay.tsx:914 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault\\getting-started\\documents\\modelUpdate-externalization.md', fileContentLength: 0, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:498 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:499 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:472 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:473 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:474 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:475 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:489 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
FilePageOverlay.tsx:912 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:913 [LABELS] 🎯 OVERLAY: fileIntelligence: null
FilePageOverlay.tsx:914 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault\\getting-started\\documents\\modelUpdate-externalization.md', fileContentLength: 5246, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:498 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:499 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:472 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:473 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:474 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:475 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:489 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:52 [LABELS] 🔄 SmartLabelingInterface: useEffect triggered
SmartLabelingInterface.tsx:53 [LABELS] 🔄 SmartLabelingInterface: filePath: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\documents\modelUpdate-externalization.md
SmartLabelingInterface.tsx:54 [LABELS] 🔄 SmartLabelingInterface: fileContent length: 5246
SmartLabelingInterface.tsx:55 [LABELS] 🔄 SmartLabelingInterface: onLabelsChanged callback: function
SmartLabelingInterface.tsx:58 [LABELS] 🔄 SmartLabelingInterface: Starting intelligence loading process
SmartLabelingInterface.tsx:61 [LABELS] 🔄 SmartLabelingInterface: Calling loadOrProcessFileIntelligence
SmartLabelingInterface.tsx:84 [LABELS] 🔄 SmartLabelingInterface: loadOrProcessFileIntelligence called {filePath: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault\\getting-started\\documents\\modelUpdate-externalization.md', fileContentLength: 5246}
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\documents\modelUpdate-externalization.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Test20/personal-vault/getting-started/documents/modelUpdate-externalization.md
vaultPath.ts:48 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Test20\personal-vault\getting-started
SmartLabelingInterface.tsx:92 [LABELS] 🔄 SmartLabelingInterface: vaultPath extracted: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started
SmartLabelingInterface.tsx:95 [LABELS] 🔄 SmartLabelingInterface: Attempting to load existing intelligence
SmartLabelingInterface.tsx:96 [LABELS] 🔄 IDENTIFICATION: filePath for identification: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\documents\modelUpdate-externalization.md
SmartLabelingInterface.tsx:97 [LABELS] 🔄 IDENTIFICATION: vaultPath for identification: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started
SmartLabelingInterface.tsx:98 [LABELS] 🔄 IDENTIFICATION: About to call getStoredFileIntelligence...
fileAnalysisService.ts:1094 [LABELS] 📂 fileAnalysisService: getStoredFileIntelligence called
fileAnalysisService.ts:1095 [LABELS] 📂 filePath: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started\documents\modelUpdate-externalization.md
fileAnalysisService.ts:1096 [LABELS] 📂 vaultPath: C:\Users\<USER>\Documents\Test20\personal-vault\getting-started
fileAnalysisService.ts:1103 [LABELS] 📂 getStoredFileIntelligence result: found
fileAnalysisService.ts:1105 [LABELS] 📂 Found intelligence with 0 labels
fileAnalysisService.ts:1106 [LABELS] 📂 Intelligence file_path in JSON: undefined
fileAnalysisService.ts:1134 [LABELS] 📂 Converted to FileIntelligence with 0 ideas
SmartLabelingInterface.tsx:100 [LABELS] 🔄 IDENTIFICATION: getStoredFileIntelligence returned: found
SmartLabelingInterface.tsx:103 [LABELS] ✅ SmartLabelingInterface: Found existing intelligence: {ideasCount: 0, entitiesCount: 0}
SmartLabelingInterface.tsx:109 [LABELS] 🔄 Updating labelState with existing intelligence...
SmartLabelingInterface.tsx:119 [LABELS] 🔄 New labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'complete'}
SmartLabelingInterface.tsx:128 [LABELS] 🏷️ SmartLabelingInterface: Found existing intelligence with 0 ideas
SmartLabelingInterface.tsx:129 [LABELS] 🏷️ Ideas to send to overlay: []
SmartLabelingInterface.tsx:139 [LABELS] 🏷️ Calling onLabelsChanged callback with 0 ideas
FilePageOverlay.tsx:173 [LABELS] 🎯 OVERLAY: handleLabelsChanged called with 0 ideas
FilePageOverlay.tsx:174 [LABELS] 🎯 OVERLAY: Received ideas: []
FilePageOverlay.tsx:181 [LABELS] 🎯 OVERLAY: Current fileIntelligence state: null
FilePageOverlay.tsx:200 [LABELS] 🎯 OVERLAY: Setting fileIntelligence with 0 ideas
FilePageOverlay.tsx:211 [LABELS] 🎯 OVERLAY: ✅ Updated fileIntelligence from SmartLabelingInterface: {totalIdeas: 0, selectedIdeas: 0, fileIntelligenceSet: true, hasIdeas: false}
SmartLabelingInterface.tsx:141 [LABELS] 🏷️ onLabelsChanged callback completed
FilePageOverlay.tsx:912 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:913 [LABELS] 🎯 OVERLAY: fileIntelligence: exists
FilePageOverlay.tsx:914 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
FilePageOverlay.tsx:916 [LABELS] 🎯 OVERLAY: Ideas to display: []
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Test20\\personal-vault\\getting-started\\documents\\modelUpdate-externalization.md', fileContentLength: 5246, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:498 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:499 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:472 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:473 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:474 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:475 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:489 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
