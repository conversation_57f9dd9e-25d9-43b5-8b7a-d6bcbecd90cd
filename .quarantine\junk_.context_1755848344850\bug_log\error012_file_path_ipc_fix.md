# Bug Report - 2025-07-27

**Issue**: `UNKNOWN_ERROR` with `Invalid file path` message during "Direct Parsing Test" when no file was selected or an invalid path was passed.

**Root Cause**: The initial implementation used a standard HTML `<input type="file">` element in the React frontend. For security reasons, web browsers (and by extension, Electron's renderer process in its default configuration) do not provide the full, absolute file system path for files selected through this input. The `File` object's `path` property was `undefined`, which was then passed as `null` through the IPC bridge, causing the backend validator for the `testDirectParsing` endpoint to fail.

**Prevention**: The core learning is to never rely on standard web APIs for native file system access in Electron. All file system operations that require a direct path must be initiated from or brokered by the main process via IPC.

**Pattern**: This issue highlights a common pattern of incorrectly applying web development practices within the Electron environment without accounting for its specific security model and architecture. Any feature requiring local file paths must use Electron's native dialogs.

## Solution Implemented (Direct Parsing Popup)

The issue was resolved by refactoring the file selection mechanism to use Electron's native file dialog, ensuring a valid and secure file path is always obtained.

1.  **Frontend Change (`DirectParsingPopup.tsx`)**: The `<input type="file">` was removed. A new button was added that, when clicked, calls a new IPC endpoint: `window.electronAPI.files.showOpenDialog`.

2.  **Backend IPC Handler (`main.ts`)**: A new endpoint, `files:showOpenDialog`, was registered in the `APIRegistry`. This handler uses Electron's `dialog.showOpenDialog()` method from the main process. This method opens the operating system's native file selection window.

3.  **Secure Data Flow**: The native dialog securely returns the selected file path(s) to the frontend. This valid path is then used in the subsequent call to `testDirectParsing`, resolving the error.

## Application to Main Chat UI

The exact same solution should be applied to the file attachment functionality in the main chat interface to ensure robust and secure file handling.

- **Action Item**: The file attachment button in the `InputArea` or `FileAttachments` component should be modified.
- **Implementation**: Instead of using a hidden `<input type="file">`, its `onClick` handler should invoke the `window.electronAPI.files.showOpenDialog` IPC call.
- **Benefit**: This will provide a consistent, native user experience for file selection and eliminate any potential bugs related to invalid file paths, making the feature more reliable.