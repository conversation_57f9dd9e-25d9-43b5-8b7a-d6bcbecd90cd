/**
 * Unified Path Resolution Service
 * 
 * This service provides a single, secure way to handle all path operations
 * across the ChatLo application. It replaces scattered path handling logic
 * with a centralized, validated approach.
 * 
 * SECURITY FEATURES:
 * - Prevents path injection attacks
 * - Validates vault boundaries
 * - Sanitizes corrupted paths
 * - Provides consistent path normalization
 */

import { extractContextPath as extractContextPathUtil } from '../utils/vaultPath'

export interface PathResolutionResult {
  success: boolean
  resolvedPath: string
  isAbsolute: boolean
  isVaultPath: boolean
  contextPath?: string
  error?: string
}

export interface VaultPathInfo {
  vaultRoot: string
  contextName: string
  relativePath: string
  fullPath: string
}

export class UnifiedPathService {
  private static instance: UnifiedPathService
  private allowedVaultRoots: string[] = []
  private vaultRegistry: any = null

  private constructor() {}

  static getInstance(): UnifiedPathService {
    if (!UnifiedPathService.instance) {
      UnifiedPathService.instance = new UnifiedPathService()
    }
    return UnifiedPathService.instance
  }

  /**
   * Initialize the service with vault registry and allowed roots
   */
  async initialize(vaultRegistry: any): Promise<void> {
    this.vaultRegistry = vaultRegistry
    
    // Extract allowed vault roots from registry
    if (vaultRegistry?.vaultRoot) {
      this.allowedVaultRoots = [vaultRegistry.vaultRoot]
      
      // Add individual vault paths
      if (vaultRegistry.vaults && Array.isArray(vaultRegistry.vaults)) {
        for (const vault of vaultRegistry.vaults) {
          if (vault.path && typeof vault.path === 'string') {
            this.allowedVaultRoots.push(vault.path)
          }
        }
      }
    }

    console.log('[UNIFIED-PATH] ✅ Service initialized with vault roots:', this.allowedVaultRoots)
  }

  /**
   * Resolve and validate a file path
   */
  async resolvePath(filePath: string, contextId?: string): Promise<PathResolutionResult> {
    try {
      if (!filePath || typeof filePath !== 'string') {
        return {
          success: false,
          resolvedPath: '',
          isAbsolute: false,
          isVaultPath: false,
          error: 'Invalid file path'
        }
      }

      // SECURITY: Detect and reject corrupted paths
      if (this.isCorruptedPath(filePath)) {
        console.error('[UNIFIED-PATH] 🚨 Corrupted path detected:', filePath)
        return {
          success: false,
          resolvedPath: '',
          isAbsolute: false,
          isVaultPath: false,
          error: 'Corrupted path detected'
        }
      }

      // Normalize the path
      const normalizedPath = await this.normalizePath(filePath)
      
      // Check if this is an absolute path
      const isAbsolute = this.isAbsolutePath(normalizedPath)
      
      // If it's a relative path and we have a context, resolve it
      let resolvedPath = normalizedPath
      let contextPath: string | undefined
      
      if (!isAbsolute && contextId) {
        const contextResult = await this.resolveContextPath(contextId, normalizedPath)
        if (contextResult.success) {
          resolvedPath = contextResult.resolvedPath
          contextPath = contextResult.contextPath
        } else {
          return {
            success: false,
            resolvedPath: '',
            isAbsolute: false,
            isVaultPath: false,
            error: `Failed to resolve context path: ${contextResult.error}`
          }
        }
      }

      // Validate that the resolved path is within allowed vault boundaries
      const isVaultPath = this.isWithinVaultBoundaries(resolvedPath)
      
      if (!isVaultPath) {
        console.error('[UNIFIED-PATH] 🚨 Path outside vault boundaries:', resolvedPath)
        return {
          success: false,
          resolvedPath: '',
          isAbsolute: false,
          isVaultPath: false,
          error: 'Path outside vault boundaries'
        }
      }

      return {
        success: true,
        resolvedPath,
        isAbsolute,
        isVaultPath: true,
        contextPath
      }

    } catch (error) {
      console.error('[UNIFIED-PATH] ❌ Error resolving path:', error)
      return {
        success: false,
        resolvedPath: '',
        isAbsolute: false,
        isVaultPath: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Resolve a context path for a given context ID
   */
  private async resolveContextPath(contextId: string, relativePath: string): Promise<{
    success: boolean
    resolvedPath: string
    contextPath: string
    error?: string
  }> {
    try {
      if (!this.vaultRegistry) {
        return {
          success: false,
          resolvedPath: '',
          contextPath: '',
          error: 'Vault registry not initialized'
        }
      }

      // Find the context in the registry
      let contextPath = ''
      for (const vault of this.vaultRegistry.vaults || []) {
        for (const context of vault.contexts || []) {
          if (context.id === contextId || context.name === contextId) {
            contextPath = context.path
            break
          }
        }
        if (contextPath) break
      }

      if (!contextPath) {
        return {
          success: false,
          resolvedPath: '',
          contextPath: '',
          error: `Context not found: ${contextId}`
        }
      }

      // Join the context path with the relative path
      const resolvedPath = await this.joinPath(contextPath, relativePath)

      return {
        success: true,
        resolvedPath,
        contextPath
      }

    } catch (error) {
      return {
        success: false,
        resolvedPath: '',
        contextPath: '',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Normalize a path using the electron API
   */
  private async normalizePath(filePath: string): Promise<string> {
    try {
      if (window.electronAPI?.path?.normalize) {
        const result = await window.electronAPI.path.normalize(filePath)
        if (result.success && result.path) {
          return result.path
        }
      }
      
      // Fallback normalization
      return filePath.replace(/[\\\/]+/g, '/').replace(/\/$/, '')
    } catch (error) {
      console.warn('[UNIFIED-PATH] Path normalization failed, using fallback:', error)
      return filePath.replace(/[\\\/]+/g, '/').replace(/\/$/, '')
    }
  }

  /**
   * Join path segments using the electron API
   */
  private async joinPath(...parts: string[]): Promise<string> {
    try {
      if (window.electronAPI?.path?.join) {
        const result = await window.electronAPI.path.join(...parts)
        if (result.success && result.path) {
          return result.path
        }
      }
      
      // Fallback path joining
      return parts.filter(Boolean).join('/')
    } catch (error) {
      console.warn('[UNIFIED-PATH] Path joining failed, using fallback:', error)
      return parts.filter(Boolean).join('/')
    }
  }

  /**
   * Check if a path is absolute
   */
  private isAbsolutePath(filePath: string): boolean {
    // Windows absolute path
    if (/^[A-Z]:[\\\/]/i.test(filePath)) {
      return true
    }
    
    // Unix absolute path
    if (filePath.startsWith('/')) {
      return true
    }
    
    return false
  }

  /**
   * Check if a path is corrupted (contains escape patterns)
   */
  private isCorruptedPath(filePath: string): boolean {
    // Detect corrupted drive letter patterns (C_Users instead of C:\Users)
    if (/^[A-Z]:_/.test(filePath)) {
      return true
    }
    
    // Detect path injection attempts
    if (filePath.includes('..') || filePath.includes('\\\\')) {
      return true
    }
    
    // Detect null bytes or control characters
    if (/[\x00-\x1f\x7f]/.test(filePath)) {
      return true
    }
    
    return false
  }

  /**
   * Check if a path is within allowed vault boundaries
   */
  private isWithinVaultBoundaries(filePath: string): boolean {
    if (this.allowedVaultRoots.length === 0) {
      console.warn('[UNIFIED-PATH] ⚠️ No vault boundaries defined')
      return true // Allow if no boundaries defined
    }

    const normalizedPath = filePath.replace(/\\/g, '/')
    
    return this.allowedVaultRoots.some(root => {
      const normalizedRoot = root.replace(/\\/g, '/')
      return normalizedPath.startsWith(normalizedRoot)
    })
  }

  /**
   * Extract context path from a file path (unified version)
   */
  extractContextPath(filePath: string): string {
    // Use the existing utility function
    return extractContextPathUtil(filePath)
  }

  /**
   * Parse a vault path into its components
   */
  parseVaultPath(filePath: string): VaultPathInfo | null {
    try {
      if (!this.vaultRegistry?.vaultRoot) {
        return null
      }

      const vaultRoot = this.vaultRegistry.vaultRoot
      const relativePath = filePath.replace(vaultRoot, '').replace(/^[\\\/]/, '')
      const pathParts = relativePath.split(/[\\\/]/)
      
      if (pathParts.length < 2) {
        return null
      }

      const contextName = pathParts[0]
      const remainingPath = pathParts.slice(1).join('/')

      return {
        vaultRoot,
        contextName,
        relativePath: remainingPath,
        fullPath: filePath
      }
    } catch (error) {
      console.error('[UNIFIED-PATH] Error parsing vault path:', error)
      return null
    }
  }

  /**
   * Get the current vault root path
   */
  getVaultRoot(): string | null {
    return this.vaultRegistry?.vaultRoot || null
  }

  /**
   * Check if a path is a valid vault file
   */
  async isValidVaultFile(filePath: string): Promise<boolean> {
    try {
      const resolved = await this.resolvePath(filePath)
      if (!resolved.success) {
        return false
      }

      // Check if file exists
      if (window.electronAPI?.vault?.pathExists) {
        const exists = await window.electronAPI.vault.pathExists(resolved.resolvedPath)
        return exists.exists
      }

      return true // Assume valid if we can't check
    } catch (error) {
      return false
    }
  }
}

// Export singleton instance
export const unifiedPathService = UnifiedPathService.getInstance()

