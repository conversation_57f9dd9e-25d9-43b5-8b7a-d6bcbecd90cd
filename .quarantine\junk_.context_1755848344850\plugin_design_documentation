# ChatLo Plugin System Developer Guide
version = 0.1
date = 26/07/2025

## Table of Contents
1. [Overview](#overview)
2. [Plugin Architecture](#plugin-architecture)
3. [API Design](#api-design)
4. [Development Guide](#development-guide)
5. [Extension Points](#extension-points)
6. [Plugin Types](#plugin-types)
7. [Best Practices](#best-practices)
8. [Testing & Debugging](#testing--debugging)
9. [Deployment](#deployment)

## Overview

ChatLo features a comprehensive plugin system that allows developers to extend functionality across multiple domains:

- **File Processing**: Custom file type handlers and content extractors
- **Chat Enhancement**: Message processing, context enhancement, and response modification
- **Model Integration**: Custom model providers and request/response interceptors
- **UI Extensions**: Custom components, toolbar items, and settings panels
- **API Extensions**: Custom endpoints and middleware
- **Intelligence Processing**: Entity extraction, topic analysis, and content enhancement

### Key Features

- **Universal Plugin Framework**: Single system for all plugin types
- **Capability-Based Architecture**: Plugins declare their capabilities for automatic discovery
- **Hot-Swappable**: Enable/disable plugins without restart
- **Extension Points**: Well-defined hooks for extending functionality
- **Type Safety**: Full TypeScript support with comprehensive interfaces

## Plugin Architecture

### Core Components

#### 1. Universal Plugin Manager
<mcfile name="PluginManager.ts" path="c:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\electron\plugins\PluginManager.ts"></mcfile>

The central orchestrator that handles:
- Plugin discovery and loading
- Capability registration
- State management
- API endpoint registration

#### 2. Plugin Registry
<mcfile name="types.ts" path="c:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\electron\plugins\types.ts"></mcfile>

Maintains:
- Plugin instances and metadata
- Capability mappings
- Configuration states
- Plugin lifecycle states

#### 3. Extension Points
<mcfile name="extensionPoints.ts" path="c:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\electron\plugins\extensionPoints.ts"></mcfile>

Defines interfaces for:
- File processing extensions
- Chat enhancement hooks
- Model integration points
- UI extension locations
- API extension capabilities
- Intelligence processing hooks

### Plugin Lifecycle

```mermaid
graph TD
    A[Discovery] --> B[Manifest Validation]
    B --> C[Module Loading]
    C --> D[Plugin Initialization]
    D --> E[Capability Registration]
    E --> F[Active State]
    F --> G[Runtime Operations]
    G --> H[Cleanup]
```

## API Design

### Core Plugin Interface

```typescript:electron/plugins/types.ts
export interface BasePlugin {
  // Plugin identification
  id: string
  name: string
  version: string
  description?: string
  author?: string
  
  // Plugin lifecycle
  initialize(): Promise<void>
  cleanup?(): Promise<void>
  
  // Plugin capabilities
  getCapabilities(): PluginCapability[]
  
  // Plugin configuration
  getDefaultConfig(): Record<string, any>
  validateConfig?(config: Record<string, any>): boolean
}
```

### Plugin Capabilities

```typescript:electron/plugins/types.ts
export enum PluginCapability {
  FILE_PROCESSING = 'file_processing',
  CHAT_ENHANCEMENT = 'chat_enhancement',
  MODEL_INTEGRATION = 'model_integration',
  UI_EXTENSION = 'ui_extension',
  API_EXTENSION = 'api_extension',
  INTELLIGENCE_PROCESSING = 'intelligence_processing'
}
```

### Plugin States

```typescript:electron/plugins/types.ts
export enum PluginState {
  UNLOADED = 'unloaded',
  LOADING = 'loading',
  LOADED = 'loaded',
  ACTIVE = 'active',
  ERROR = 'error',
  DISABLED = 'disabled'
}
```

### Plugin Manifest

```typescript:electron/plugins/types.ts
export interface PluginManifest {
  id: string
  name: string
  version: string
  description?: string
  author?: string
  main: string
  dependencies?: string[]
  optionalDependencies?: string[]
  capabilities: PluginCapability[]
  apiVersion: string
  entryPoints?: Record<string, string>
}
```

## Development Guide

### 1. Setting Up Development Environment

```bash
# Clone the repository
git clone <repository-url>
cd chatlo

# Install dependencies
npm install

# Set up development environment
npm run dev
```

### 2. Creating a New Plugin

#### Step 1: Create Plugin Directory Structure

```
plugins/
└── my-awesome-plugin/
    ├── manifest.json
    ├── index.ts
    ├── package.json
    └── README.md
```

#### Step 2: Define Plugin Manifest

```json:plugins/my-awesome-plugin/manifest.json
{
  "id": "my-awesome-plugin",
  "name": "My Awesome Plugin",
  "version": "1.0.0",
  "description": "An awesome plugin that does amazing things",
  "author": "Your Name",
  "main": "index.ts",
  "capabilities": ["chat_enhancement"],
  "apiVersion": "1.0.0",
  "dependencies": [],
  "optionalDependencies": []
}
```

#### Step 3: Implement Plugin Class

```typescript:plugins/my-awesome-plugin/index.ts
import { BasePlugin, PluginCapability } from '../../types'
import { ChatExtension } from '../../extensionPoints'

export default class MyAwesomePlugin implements BasePlugin, ChatExtension {
  id = 'my-awesome-plugin'
  name = 'My Awesome Plugin'
  version = '1.0.0'
  description = 'An awesome plugin that does amazing things'
  author = 'Your Name'
  
  async initialize(): Promise<void> {
    console.log('My Awesome Plugin initialized')
    // Perform initialization logic
  }
  
  async cleanup(): Promise<void> {
    console.log('My Awesome Plugin cleaned up')
    // Perform cleanup logic
  }
  
  getCapabilities(): PluginCapability[] {
    return [PluginCapability.CHAT_ENHANCEMENT]
  }
  
  getDefaultConfig(): Record<string, any> {
    return {
      enabled: true,
      feature1: 'default_value',
      feature2: false
    }
  }
  
  validateConfig(config: Record<string, any>): boolean {
    // Validate configuration
    return typeof config.enabled === 'boolean'
  }
  
  // Chat Enhancement Implementation
  async enhancePrompt(prompt: string, context: any): Promise<string> {
    // Enhance the prompt with additional context
    return `Enhanced: ${prompt}`
  }
  
  async beforeMessageSend(message: any): Promise<any> {
    // Process message before sending
    return message
  }
  
  async afterMessageReceived(message: any): Promise<any> {
    // Process message after receiving
    return message
  }
}
```

### 3. Plugin Registration

Plugins are automatically discovered and loaded by the Universal Plugin Manager. Place your plugin in one of these directories:

- `electron/plugins/core-*` - Core plugins (shipped with ChatLo)
- `electron/plugins/community-*` - Community plugins
- `~/.chatlo/plugins/` - User-installed plugins

## Extension Points

### File Processing Extension

```typescript:electron/plugins/extensionPoints.ts
export interface FileProcessorExtension {
  supportedTypes: string[]
  supportedExtensions: string[]
  canProcess(filePath: string, fileType: string): boolean
  process(filePath: string): Promise<ProcessedFileContent>
}
```

**Example Implementation:**

```typescript:plugins/custom-file-processor/index.ts
export default class CustomFileProcessor implements BasePlugin, FileProcessorExtension {
  // ... base plugin implementation
  
  supportedTypes = ['custom']
  supportedExtensions = ['.custom']
  
  canProcess(filePath: string, fileType: string): boolean {
    return fileType === 'custom' || filePath.endsWith('.custom')
  }
  
  async process(filePath: string): Promise<ProcessedFileContent> {
    const content = await fs.promises.readFile(filePath, 'utf8')
    return {
      text: content,
      metadata: {
        processor: this.name,
        fileSize: content.length
      }
    }
  }
}
```

### Chat Enhancement Extension

```typescript:electron/plugins/extensionPoints.ts
export interface ChatExtension {
  beforeMessageSend?(message: any): Promise<any>
  afterMessageReceived?(message: any): Promise<any>
  provideContextData?(): Promise<any>
  enhancePrompt?(prompt: string, context: any): Promise<string>
  processResponse?(response: any): Promise<any>
}
```

### UI Extension

```typescript:electron/plugins/extensionPoints.ts
export interface UIExtension {
  provideComponent?(location: UILocation, props?: any): React.ComponentType<any>
  provideMenuItem?(location: MenuLocation): MenuItem
  provideToolbarItem?(location: ToolbarLocation): ToolbarItem
  provideSettingsPanel?(): SettingsPanel
}
```

**Example Implementation:**

```typescript:plugins/custom-ui/index.ts
export default class CustomUIPlugin implements BasePlugin, UIExtension {
  // ... base plugin implementation
  
  provideToolbarItem(location: ToolbarLocation): ToolbarItem {
    if (location === ToolbarLocation.CHAT_INPUT_TOOLBAR) {
      return {
        id: 'custom-action',
        label: 'Custom Action',
        icon: 'custom-icon',
        onClick: () => this.handleCustomAction(),
        tooltip: 'Perform custom action'
      }
    }
    return null
  }
  
  private handleCustomAction(): void {
    // Custom action implementation
  }
}
```

### API Extension

```typescript:electron/plugins/extensionPoints.ts
export interface APIExtension {
  registerEndpoints(apiRegistry: APIRegistry): void
  provideMiddleware?(): Middleware[]
}
```

**Example Implementation:**

```typescript:plugins/custom-api/index.ts
export default class CustomAPIPlugin implements BasePlugin, APIExtension {
  // ... base plugin implementation
  
  registerEndpoints(apiRegistry: APIRegistry): void {
    apiRegistry.register('GET', '/api/custom/data', async (req, res) => {
      res.json({ message: 'Custom API endpoint' })
    })
    
    apiRegistry.register('POST', '/api/custom/process', async (req, res) => {
      const result = await this.processCustomData(req.body)
      res.json(result)
    })
  }
  
  private async processCustomData(data: any): Promise<any> {
    // Custom processing logic
    return { processed: true, data }
  }
}
```

## Plugin Types

### 1. File Processing Plugins

**Purpose**: Handle specific file types and extract content

**Examples**:
- <mcfile name="TextPlugin.ts" path="c:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\electron\fileProcessors\plugins\TextPlugin.ts"></mcfile>
- <mcfile name="PDFPlugin.ts" path="c:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\electron\fileProcessors\plugins\PDFPlugin.ts"></mcfile>
- <mcfile name="WordPlugin.ts" path="c:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\electron\fileProcessors\plugins\WordPlugin.ts"></mcfile>

**Key Features**:
- File type detection
- Content extraction
- Metadata generation
- Error handling

### 2. Chat Enhancement Plugins

**Purpose**: Enhance chat functionality and user experience

**Examples**:
- <mcfile name="SmartContextPlugin.ts" path="c:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\electron\plugins\examples\smart-context\SmartContextPlugin.ts"></mcfile>

**Key Features**:
- Message preprocessing
- Context enhancement
- Response modification
- Auto-completion

### 3. Model Integration Plugins

**Purpose**: Integrate with external AI models and services

**Key Features**:
- Custom model providers
- Request/response interception
- Model discovery
- Authentication handling

### 4. UI Extension Plugins

**Purpose**: Extend the user interface with custom components

**Examples**:
- <mcfile name="CustomToolbarPlugin.ts" path="c:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\electron\plugins\examples\custom-toolbar\CustomToolbarPlugin.ts"></mcfile>

**Key Features**:
- Custom toolbar items
- Settings panels
- Menu items
- React components

### 5. Intelligence Processing Plugins

**Purpose**: Provide AI-powered content analysis and enhancement

**Key Features**:
- Entity extraction
- Topic analysis
- Content summarization
- Sentiment analysis

## Best Practices

### 1. Plugin Design Principles

- **Single Responsibility**: Each plugin should have a clear, focused purpose
- **Loose Coupling**: Minimize dependencies on other plugins
- **Error Resilience**: Handle errors gracefully without breaking the system
- **Performance**: Optimize for minimal impact on application performance
- **User Experience**: Provide clear feedback and intuitive interfaces

### 2. Code Quality Standards

```typescript
// ✅ Good: Clear naming and error handling
export default class DocumentAnalyzerPlugin implements BasePlugin {
  private analysisCache = new Map<string, AnalysisResult>()
  
  async analyzeDocument(filePath: string): Promise<AnalysisResult> {
    try {
      if (this.analysisCache.has(filePath)) {
        return this.analysisCache.get(filePath)!
      }
      
      const result = await this.performAnalysis(filePath)
      this.analysisCache.set(filePath, result)
      return result
    } catch (error) {
      console.error(`Document analysis failed for ${filePath}:`, error)
      throw new PluginError(`Analysis failed: ${error.message}`)
    }
  }
}

// ❌ Bad: Generic naming and poor error handling
export default class Plugin {
  async process(file: string): Promise<any> {
    const result = await doSomething(file)
    return result
  }
}
```

### 3. Configuration Management

```typescript
// ✅ Good: Comprehensive configuration with validation
getDefaultConfig(): PluginConfig {
  return {
    enabled: true,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    supportedFormats: ['pdf', 'docx', 'txt'],
    analysisDepth: 'standard',
    cacheResults: true,
    cacheTTL: 3600000 // 1 hour
  }
}

validateConfig(config: PluginConfig): boolean {
  return (
    typeof config.enabled === 'boolean' &&
    typeof config.maxFileSize === 'number' &&
    config.maxFileSize > 0 &&
    Array.isArray(config.supportedFormats) &&
    ['basic', 'standard', 'deep'].includes(config.analysisDepth)
  )
}
```

### 4. Error Handling

```typescript
// ✅ Good: Comprehensive error handling
class PluginError extends Error {
  constructor(
    message: string,
    public code: string,
    public recoverable: boolean = true
  ) {
    super(message)
    this.name = 'PluginError'
  }
}

async initialize(): Promise<void> {
  try {
    await this.loadDependencies()
    await this.validateEnvironment()
    await this.setupResources()
  } catch (error) {
    if (error instanceof PluginError && !error.recoverable) {
      throw error
    }
    
    console.warn(`Plugin initialization warning: ${error.message}`)
    // Continue with degraded functionality
  }
}
```

### 5. Performance Optimization

```typescript
// ✅ Good: Lazy loading and caching
class OptimizedPlugin implements BasePlugin {
  private heavyDependency: any = null
  private resultCache = new LRUCache<string, any>(100)
  
  private async loadHeavyDependency(): Promise<any> {
    if (!this.heavyDependency) {
      this.heavyDependency = await import('heavy-library')
    }
    return this.heavyDependency
  }
  
  async processWithCaching(input: string): Promise<any> {
    const cacheKey = this.generateCacheKey(input)
    
    if (this.resultCache.has(cacheKey)) {
      return this.resultCache.get(cacheKey)
    }
    
    const result = await this.performProcessing(input)
    this.resultCache.set(cacheKey, result)
    return result
  }
}
```

## Testing & Debugging

### 1. Unit Testing

```typescript:tests/plugins/MyPlugin.test.ts
import MyPlugin from '../plugins/my-plugin'
import { PluginCapability } from '../types'

describe('MyPlugin', () => {
  let plugin: MyPlugin
  
  beforeEach(() => {
    plugin = new MyPlugin()
  })
  
  afterEach(async () => {
    if (plugin.cleanup) {
      await plugin.cleanup()
    }
  })
  
  test('should initialize successfully', async () => {
    await expect(plugin.initialize()).resolves.not.toThrow()
  })
  
  test('should declare correct capabilities', () => {
    const capabilities = plugin.getCapabilities()
    expect(capabilities).toContain(PluginCapability.CHAT_ENHANCEMENT)
  })
  
  test('should validate configuration correctly', () => {
    const validConfig = { enabled: true, feature1: 'value' }
    const invalidConfig = { enabled: 'not-boolean' }
    
    expect(plugin.validateConfig(validConfig)).toBe(true)
    expect(plugin.validateConfig(invalidConfig)).toBe(false)
  })
})
```

### 2. Integration Testing

```typescript:tests/integration/PluginManager.test.ts
import { UniversalPluginManager } from '../plugins/PluginManager'
import { APIRegistry } from '../api/APIRegistry'

describe('Plugin Integration', () => {
  let pluginManager: UniversalPluginManager
  let apiRegistry: APIRegistry
  
  beforeEach(() => {
    apiRegistry = new APIRegistry()
    pluginManager = new UniversalPluginManager(apiRegistry)
  })
  
  test('should discover and load plugins', async () => {
    pluginManager.addPluginDirectory('./test-plugins')
    const manifests = await pluginManager.discoverPlugins()
    
    expect(manifests.length).toBeGreaterThan(0)
    
    for (const manifest of manifests) {
      await expect(pluginManager.loadPlugin(manifest)).resolves.not.toThrow()
    }
  })
})
```

### 3. Debugging Tools

```typescript
// Plugin debugging utilities
class PluginDebugger {
  static logPluginActivity(plugin: BasePlugin, activity: string, data?: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Plugin:${plugin.name}] ${activity}`, data)
    }
  }
  
  static measurePerformance<T>(
    plugin: BasePlugin,
    operation: string,
    fn: () => Promise<T>
  ): Promise<T> {
    return new Promise(async (resolve, reject) => {
      const start = performance.now()
      try {
        const result = await fn()
        const duration = performance.now() - start
        this.logPluginActivity(plugin, `${operation} completed in ${duration.toFixed(2)}ms`)
        resolve(result)
      } catch (error) {
        const duration = performance.now() - start
        this.logPluginActivity(plugin, `${operation} failed after ${duration.toFixed(2)}ms`, error)
        reject(error)
      }
    })
  }
}
```

## Deployment

### 1. Plugin Packaging

```json:package.json
{
  "name": "chatlo-plugin-awesome",
  "version": "1.0.0",
  "description": "An awesome ChatLo plugin",
  "main": "dist/index.js",
  "scripts": {
    "build": "tsc",
    "test": "jest",
    "package": "npm run build && npm pack"
  },
  "peerDependencies": {
    "@chatlo/plugin-api": "^1.0.0"
  },
  "files": [
    "dist/",
    "manifest.json",
    "README.md"
  ]
}
```

### 2. Distribution

```bash
# Build plugin
npm run build

# Package for distribution
npm run package

# Install in ChatLo
chatlo plugin install chatlo-plugin-awesome-1.0.0.tgz
```

### 3. Plugin Registry

Plugins can be distributed through:
- **Official Plugin Registry**: Curated plugins reviewed by the ChatLo team
- **Community Registry**: Community-contributed plugins
- **Direct Installation**: Manual installation from package files
- **Development Mode**: Local development plugins

---

## Conclusion

The ChatLo plugin system provides a powerful and flexible framework for extending functionality across all aspects of the application. By following this guide and best practices, developers can create robust, performant, and user-friendly plugins that enhance the ChatLo experience.

For additional support and examples, refer to:
- <mcfolder name="examples" path="c:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\electron\plugins\examples"></mcfolder> - Example plugin implementations
- <mcfile name="plugin_implementation_plan.md" path="c:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\.context\plugin_implementation_plan.md"></mcfile> - Detailed implementation roadmap
- Community forums and documentation

**Happy plugin development!** 🚀
        